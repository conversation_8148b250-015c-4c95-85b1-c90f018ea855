import { defineStore } from 'pinia'
import { newsApi } from '../api'

export const useNewsStore = defineStore('news', {
  state: () => ({
    news: [],
    currentNews: null,
    featuredNews: [],
    latestNews: [],
    loading: false,
    error: null
  }),
  
  getters: {
    // 根据分类获取新闻
    getNewsByCategory: (state) => (category) => {
      return state.news.filter(item => item.category === category)
    },
    
    // 获取企业新闻
    companyNews: (state) => {
      return state.news.filter(item => item.category === 'company')
    },
    
    // 获取行业资讯
    industryNews: (state) => {
      return state.news.filter(item => item.category === 'industry')
    },
    
    // 获取技术支持
    supportNews: (state) => {
      return state.news.filter(item => item.category === 'support')
    },
    
    // 获取新闻总数
    totalNews: (state) => state.news.length,
    
    // 检查是否有新闻
    hasNews: (state) => state.news.length > 0
  },
  
  actions: {
    // 获取所有新闻
    async fetchNews() {
      this.loading = true
      this.error = null
      
      try {
        const news = await newsApi.getAll()
        this.news = news
      } catch (error) {
        this.error = '获取新闻列表失败'
        console.error('获取新闻失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 根据ID获取新闻
    async fetchNewsById(id) {
      this.loading = true
      this.error = null
      
      try {
        const news = await newsApi.getById(id)
        this.currentNews = news
        return news
      } catch (error) {
        this.error = '获取新闻详情失败'
        console.error('获取新闻详情失败:', error)
        return null
      } finally {
        this.loading = false
      }
    },
    
    // 根据分类获取新闻
    async fetchNewsByCategory(category) {
      this.loading = true
      this.error = null
      
      try {
        const news = await newsApi.getByCategory(category)
        return news
      } catch (error) {
        this.error = '获取分类新闻失败'
        console.error('获取分类新闻失败:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    // 搜索新闻
    async searchNews(keyword) {
      this.loading = true
      this.error = null
      
      try {
        const news = await newsApi.search(keyword)
        return news
      } catch (error) {
        this.error = '搜索新闻失败'
        console.error('搜索新闻失败:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    // 获取推荐新闻
    async fetchFeaturedNews() {
      this.loading = true
      this.error = null
      
      try {
        const news = await newsApi.getFeatured()
        this.featuredNews = news
      } catch (error) {
        this.error = '获取推荐新闻失败'
        console.error('获取推荐新闻失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 获取最新新闻
    async fetchLatestNews(limit = 6) {
      this.loading = true
      this.error = null
      
      try {
        const news = await newsApi.getLatest(limit)
        this.latestNews = news
      } catch (error) {
        this.error = '获取最新新闻失败'
        console.error('获取最新新闻失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 创建新闻
    async createNews(news) {
      this.loading = true
      this.error = null
      
      try {
        const newNews = await newsApi.create(news)
        this.news.unshift(newNews) // 添加到开头
        return newNews
      } catch (error) {
        this.error = '创建新闻失败'
        console.error('创建新闻失败:', error)
        return null
      } finally {
        this.loading = false
      }
    },
    
    // 更新新闻
    async updateNews(id, news) {
      this.loading = true
      this.error = null
      
      try {
        const updatedNews = await newsApi.update(id, news)
        const index = this.news.findIndex(n => n.id === id)
        if (index !== -1) {
          this.news[index] = updatedNews
        }
        return updatedNews
      } catch (error) {
        this.error = '更新新闻失败'
        console.error('更新新闻失败:', error)
        return null
      } finally {
        this.loading = false
      }
    },
    
    // 删除新闻
    async deleteNews(id) {
      this.loading = true
      this.error = null
      
      try {
        await newsApi.delete(id)
        this.news = this.news.filter(n => n.id !== id)
        return true
      } catch (error) {
        this.error = '删除新闻失败'
        console.error('删除新闻失败:', error)
        return false
      } finally {
        this.loading = false
      }
    },
    
    // 清除错误
    clearError() {
      this.error = null
    },
    
    // 设置当前新闻
    setCurrentNews(news) {
      this.currentNews = news
    }
  }
})
