import{c as s,a as t,t as l,n as r,e as y,f as _,F as u,r as p,m as b,i as x,g as v,z as I,l as h,o as C,h as n}from"./vendor-qI2TI6m6.js";import{A as D}from"./utils-D81Uom5a.js";import{_ as w,P}from"./index-Cqv_a1eq.js";const A={name:"ProductDetail",components:{ProductNavigation:P},setup(){const c=I(),o=v(c.params.category),d=v(c.params.productId),e={"android-face":{color:"blue",products:{"jc-f901":{name:"JC-F901 安卓人脸消费机",description:"7寸高清触摸屏，支持人脸识别和刷卡消费",fullDescription:"基于安卓系统的智能人脸识别消费终端，采用7寸高清触摸屏设计，支持人脸识别、刷卡消费等多种支付方式，识别速度快，准确率高，适用于各种消费场景。",detailedDescription:"JC-F901安卓人脸消费机采用先进的人脸识别算法，结合高性能ARM处理器，确保识别的准确性和速度。设备支持离线和在线两种工作模式，即使在网络不稳定的环境下也能正常工作。内置大容量存储，可存储数万张人脸模板，满足大型场所的使用需求。",image:"https://placehold.co/600x400",gallery:["https://placehold.co/150x100","https://placehold.co/150x100","https://placehold.co/150x100","https://placehold.co/150x100"],features:[{icon:"fas fa-user",text:"人脸识别技术"},{icon:"fas fa-mobile-alt",text:"Android 9.0系统"},{icon:"fas fa-wifi",text:"网络通讯功能"},{icon:"fas fa-shield-alt",text:"安全加密存储"}],specifications:[{name:"显示屏",value:"7寸IPS触摸屏"},{name:"处理器",value:"ARM Cortex-A7"},{name:"操作系统",value:"Android 9.0"},{name:"识别距离",value:"0.3-1.5米"},{name:"识别时间",value:"≤1秒"},{name:"存储容量",value:"10000张人脸"},{name:"工作温度",value:"-10℃~+60℃"},{name:"通讯方式",value:"TCP/IP、WiFi"}]},"p301-2d-2w":{name:"P301-2D-2W 台式人脸消费机",description:"智能识别，安全便捷",fullDescription:"采用先进的人脸识别技术，支持离线识别，识别速度快，准确率高。搭载安卓操作系统，界面友好，操作简单。适用于学校食堂、企业餐厅、医院等各种消费场景，为用户提供安全便捷的支付体验。",detailedDescription:"P301-2D-2W台式人脸消费机采用200万像素双目摄像头，支持活体检测技术，有效防止照片、视频等欺骗攻击。设备基于Android 8.1系统，界面友好，操作简单。支持WiFi/以太网/4G(可选)等多种通讯方式，适应不同的网络环境。",image:"/images/products/p301-front.png",gallery:["/images/products/p301-front.png","/images/products/p301-side.png","/images/products/p301-back.png","/images/products/p301-front.png"],features:[{icon:"fas fa-user",text:"先进人脸识别技术"},{icon:"fas fa-sync-alt",text:"离线识别功能"},{icon:"fas fa-bolt",text:"快速识别响应"},{icon:"fas fa-mobile-alt",text:"安卓操作系统"}],specifications:[{name:"产品型号",value:"P301-2D-2W"},{name:"操作系统",value:"Android 8.1"},{name:"处理器",value:"ARM Cortex-A7 四核 1.2GHz"},{name:"内存",value:"1GB DDR3"},{name:"存储",value:"8GB eMMC"},{name:"屏幕尺寸",value:"8英寸 IPS触摸屏"},{name:"分辨率",value:"1280×800"},{name:"摄像头",value:"200万像素双目摄像头"},{name:"识别距离",value:"0.3-1.5米"},{name:"识别速度",value:"≤1秒"},{name:"存储容量",value:"3万张人脸"},{name:"通讯方式",value:"WiFi/以太网/4G(可选)"},{name:"工作温度",value:"-10℃~60℃"},{name:"工作湿度",value:"10%~90%RH"},{name:"电源",value:"DC 12V/3A"},{name:"产品尺寸",value:"280×180×45mm"}]}}},fingerprint:{color:"purple",products:{"jc-z701":{name:"JC-Z701 高精度指纹消费机",description:"高精度指纹识别技术，识别速度快，准确率高",fullDescription:"采用高精度指纹识别技术的智能消费终端，支持指纹识别和刷卡双重验证，识别速度快，准确率高，防伪能力强。",detailedDescription:"JC-Z701指纹消费机采用光学指纹识别技术，具有高精度、高稳定性的特点。设备支持干湿手指识别，适应各种环境条件。内置高性能处理器，确保快速响应和稳定运行。",image:"https://placehold.co/600x400",gallery:["https://placehold.co/150x100","https://placehold.co/150x100","https://placehold.co/150x100","https://placehold.co/150x100"],features:[{icon:"fas fa-fingerprint",text:"高精度指纹识别"},{icon:"fas fa-shield-alt",text:"双重验证"},{icon:"fas fa-clock",text:"快速响应"},{icon:"fas fa-lock",text:"安全可靠"}],specifications:[{name:"指纹传感器",value:"光学指纹传感器"},{name:"指纹容量",value:"1000枚"},{name:"识别时间",value:"≤1秒"},{name:"误识率",value:"≤0.0001%"},{name:"拒识率",value:"≤1%"},{name:"工作温度",value:"0℃~+45℃"},{name:"工作湿度",value:"20%~80%"},{name:"通讯接口",value:"RS485、TCP/IP"}]}}}},m=h(()=>{var i;return((i=e[o.value])==null?void 0:i.color)||"blue"}),f=h(()=>{const i=e[o.value];if(!i)return{name:"产品未找到",description:"请检查产品链接是否正确",fullDescription:"",detailedDescription:"",image:"https://placehold.co/600x400",gallery:[],features:[],specifications:[]};const a=i.products[d.value];return a||{name:"产品型号未找到",description:"请检查产品型号是否正确",fullDescription:"",detailedDescription:"",image:"https://placehold.co/600x400",gallery:[],features:[],specifications:[]}});return C(()=>{D.init({duration:1e3,once:!0})}),{categoryId:o,productId:d,categoryColor:m,productInfo:f}}},k={class:"container mx-auto px-4 relative z-10"},F={class:"text-center"},N={class:"text-4xl md:text-6xl font-bold mb-4","data-aos":"fade-up"},R={class:"text-xl md:text-2xl","data-aos":"fade-up","data-aos-delay":"200"},M={class:"py-20 bg-white"},W={class:"container mx-auto px-4"},j={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},z={class:"lg:col-span-1"},B={class:"lg:col-span-3"},G={class:"bg-white rounded-xl shadow-lg p-8"},S={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},V={class:"space-y-4"},J={class:"aspect-w-4 aspect-h-3"},T=["src","alt"],H={class:"grid grid-cols-4 gap-2"},Z=["src","alt"],E={class:"space-y-6"},L={class:"text-3xl font-bold text-gray-800 mb-4"},O={class:"text-gray-600 text-lg leading-relaxed"},q={class:"space-y-3"},K={class:"text-gray-700"},Q={key:0},U={class:"bg-gray-50 rounded-lg p-4"},X={class:"font-medium text-gray-700"},Y={class:"text-gray-600"},$={class:"flex space-x-4"},tt={class:"mt-12 pt-8 border-t border-gray-200"},et={class:"prose max-w-none"},ot={class:"text-gray-600 leading-relaxed"};function at(c,o,d,e,m,f){const i=_("ProductNavigation");return n(),s("div",null,[t("section",{class:r(["text-white pt-24 pb-8 relative overflow-hidden",`bg-gradient-to-r from-${e.categoryColor}-600 to-${e.categoryColor}-800`])},[o[0]||(o[0]=t("div",{class:"absolute inset-0 bg-black opacity-10"},null,-1)),t("div",k,[t("div",F,[t("h1",N,l(e.productInfo.name),1),t("p",R,l(e.productInfo.description),1)])]),o[1]||(o[1]=t("div",{class:"absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"},null,-1)),o[2]||(o[2]=t("div",{class:"absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce",style:{"animation-delay":"1s"}},null,-1))],2),t("section",M,[t("div",W,[t("div",j,[t("div",z,[y(i,{"current-category":e.categoryId,"current-product":e.productId},null,8,["current-category","current-product"])]),t("div",B,[t("div",G,[t("div",S,[t("div",V,[t("div",J,[t("img",{src:e.productInfo.image,alt:e.productInfo.name,class:"w-full h-96 object-cover rounded-lg shadow-lg"},null,8,T)]),t("div",H,[(n(!0),s(u,null,p(e.productInfo.gallery,(a,g)=>(n(),s("img",{key:g,src:a,alt:`${e.productInfo.name} ${g+1}`,class:"w-full h-20 object-cover rounded cursor-pointer hover:opacity-75 transition-opacity"},null,8,Z))),128))])]),t("div",E,[t("div",null,[t("h2",L,l(e.productInfo.name),1),t("p",O,l(e.productInfo.fullDescription),1)]),t("div",null,[o[3]||(o[3]=t("h3",{class:"text-xl font-semibold text-gray-800 mb-4"},"产品特性",-1)),t("div",q,[(n(!0),s(u,null,p(e.productInfo.features,a=>(n(),s("div",{key:a.text,class:"flex items-center"},[t("i",{class:r([a.icon,`text-${e.categoryColor}-500`,"mr-3"])},null,2),t("span",K,l(a.text),1)]))),128))])]),e.productInfo.specifications?(n(),s("div",Q,[o[4]||(o[4]=t("h3",{class:"text-xl font-semibold text-gray-800 mb-4"},"技术参数",-1)),t("div",U,[(n(!0),s(u,null,p(e.productInfo.specifications,a=>(n(),s("div",{key:a.name,class:"flex justify-between py-2 border-b border-gray-200 last:border-b-0"},[t("span",X,l(a.name),1),t("span",Y,l(a.value),1)]))),128))])])):b("",!0),t("div",$,[t("button",{class:r(["flex-1 text-white py-3 px-6 rounded-lg font-semibold transition duration-300",`bg-${e.categoryColor}-600 hover:bg-${e.categoryColor}-700`])},o[5]||(o[5]=[t("i",{class:"fas fa-phone mr-2"},null,-1),x("立即咨询 ")]),2),t("button",{class:r(["flex-1 border-2 py-3 px-6 rounded-lg font-semibold transition duration-300",`border-${e.categoryColor}-600 text-${e.categoryColor}-600 hover:bg-${e.categoryColor}-50`])},o[6]||(o[6]=[t("i",{class:"fas fa-download mr-2"},null,-1),x("下载资料 ")]),2)])])]),t("div",tt,[o[7]||(o[7]=t("h3",{class:"text-2xl font-bold text-gray-800 mb-6"},"产品详情",-1)),t("div",et,[t("p",ot,l(e.productInfo.detailedDescription),1)])])])])])])])])}const it=w(A,[["render",at],["__scopeId","data-v-e9d8990f"]]);export{it as default};
