package com.youkate.repository;

import com.youkate.entity.News;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 新闻数据访问接口
 */
@Repository
public interface NewsRepository extends JpaRepository<News, Long> {
    
    /**
     * 根据分类查询已发布的新闻
     */
    List<News> findByCategoryAndIsPublishedTrueOrderByPublishedAtDesc(String category);
    
    /**
     * 查询所有已发布的新闻，按发布时间倒序
     */
    List<News> findByIsPublishedTrueOrderByPublishedAtDesc();
    
    /**
     * 根据标题模糊查询
     */
    @Query("SELECT n FROM News n WHERE n.title LIKE %:keyword% AND n.isPublished = true ORDER BY n.publishedAt DESC")
    List<News> findByTitleContaining(@Param("keyword") String keyword);
    
    /**
     * 根据内容模糊查询
     */
    @Query("SELECT n FROM News n WHERE (n.title LIKE %:keyword% OR n.content LIKE %:keyword%) AND n.isPublished = true ORDER BY n.publishedAt DESC")
    List<News> findByContentContaining(@Param("keyword") String keyword);
    
    /**
     * 查询推荐新闻
     */
    List<News> findByIsFeaturedTrueAndIsPublishedTrueOrderByPublishedAtDesc();
    
    /**
     * 查询最新新闻（前N条）
     */
    @Query("SELECT n FROM News n WHERE n.isPublished = true ORDER BY n.publishedAt DESC")
    List<News> findLatestNews();
}
