# 后台管理系统API接口设计

## 接口概述

本文档定义了后台管理系统的RESTful API接口，用于管理网站的所有内容。

## 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 1. 认证相关接口

### 1.1 管理员登录
```
POST /api/admin/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

Response:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": 1,
      "username": "admin",
      "realName": "系统管理员",
      "role": "super_admin"
    }
  }
}
```

### 1.2 退出登录
```
POST /api/admin/auth/logout
Authorization: Bearer {token}
```

## 2. 首页管理接口

### 2.1 轮播图管理

#### 获取轮播图列表
```
GET /api/admin/carousel
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "title": "智能一卡通解决方案",
      "subtitle": "为您的企业、学校提供全方位智能管理服务",
      "imageUrl": "https://example.com/image1.jpg",
      "linkUrl": "/solutions",
      "sortOrder": 1,
      "isActive": true
    }
  ]
}
```

#### 创建轮播图
```
POST /api/admin/carousel
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "新轮播图标题",
  "subtitle": "副标题",
  "imageUrl": "https://example.com/new-image.jpg",
  "linkUrl": "/new-link",
  "sortOrder": 4
}
```

#### 更新轮播图
```
PUT /api/admin/carousel/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "更新的标题",
  "subtitle": "更新的副标题",
  "imageUrl": "https://example.com/updated-image.jpg",
  "linkUrl": "/updated-link",
  "sortOrder": 2,
  "isActive": true
}
```

#### 删除轮播图
```
DELETE /api/admin/carousel/{id}
Authorization: Bearer {token}
```

### 2.2 解决方案卡片管理

#### 获取解决方案列表
```
GET /api/admin/solutions
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "title": "智慧校园解决方案",
      "description": "为学校提供全方位校园智能化服务",
      "imageUrl": "https://example.com/campus.jpg",
      "bannerImageUrl": "https://example.com/campus-banner.jpg",
      "contentImages": ["image1.jpg", "image2.jpg"],
      "badge": "推荐",
      "features": ["学生管理", "图书系统", "校园安全"],
      "routePath": "/solutions/smart-campus",
      "sortOrder": 1,
      "isActive": true
    }
  ]
}
```

#### 创建/更新/删除解决方案
```
POST /api/admin/solutions
PUT /api/admin/solutions/{id}
DELETE /api/admin/solutions/{id}
```

### 2.3 核心产品卡片管理

#### 获取核心产品列表
```
GET /api/admin/core-products
Authorization: Bearer {token}
```

#### 创建/更新/删除核心产品
```
POST /api/admin/core-products
PUT /api/admin/core-products/{id}
DELETE /api/admin/core-products/{id}
```

## 3. 产品管理接口

### 3.1 产品分类管理

#### 获取产品分类列表
```
GET /api/admin/product-categories
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "智能消费机系列",
      "code": "smart-consumer",
      "description": "人脸识别、二维码支付等智能消费终端设备",
      "imageUrl": "https://example.com/smart-consumer.jpg",
      "routePath": "/products/smart-consumer",
      "sortOrder": 1,
      "isActive": true,
      "subcategories": [
        {
          "id": 1,
          "name": "安卓人脸消费终端",
          "code": "android-face",
          "description": "基于安卓系统的人脸识别消费终端",
          "imageUrl": "https://example.com/android-face.jpg",
          "routePath": "/products/smart-consumer/android-face",
          "sortOrder": 1
        }
      ]
    }
  ]
}
```

#### 创建产品分类
```
POST /api/admin/product-categories
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "新产品系列",
  "code": "new-series",
  "description": "新产品系列描述",
  "imageUrl": "https://example.com/new-series.jpg",
  "routePath": "/products/new-series",
  "sortOrder": 10
}
```

### 3.2 产品子系列管理

#### 创建产品子系列
```
POST /api/admin/product-subcategories
Authorization: Bearer {token}
Content-Type: application/json

{
  "categoryId": 1,
  "name": "新子系列",
  "code": "new-subseries",
  "description": "新子系列描述",
  "imageUrl": "https://example.com/new-subseries.jpg",
  "routePath": "/products/parent-series/new-subseries",
  "sortOrder": 5
}
```

### 3.3 产品详情管理

#### 获取产品列表
```
GET /api/admin/products
Authorization: Bearer {token}
Query Parameters:
- categoryId: 分类ID（可选）
- subcategoryId: 子分类ID（可选）
- page: 页码（默认1）
- size: 每页数量（默认10）

Response:
{
  "code": 200,
  "data": {
    "content": [
      {
        "id": 1,
        "name": "P301-2D-2W 台式人脸消费机",
        "model": "P301-2D-2W",
        "description": "采用先进的人脸识别技术，支持离线识别",
        "mainImageUrl": "https://example.com/p301-main.jpg",
        "thumbnailImages": [
          "https://example.com/p301-thumb1.jpg",
          "https://example.com/p301-thumb2.jpg"
        ],
        "productTags": ["热销产品", "人脸识别", "安卓系统"],
        "categoryId": 1,
        "subcategoryId": 1,
        "routePath": "/products/android-face/p301",
        "sortOrder": 1,
        "isActive": true
      }
    ],
    "totalElements": 50,
    "totalPages": 5,
    "currentPage": 1
  }
}
```

#### 创建产品（基于P301模板）
```
POST /api/admin/products
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "新产品名称",
  "model": "NEW-MODEL",
  "description": "新产品描述",
  "mainImageUrl": "https://example.com/new-product-main.jpg",
  "thumbnailImages": [
    "https://example.com/thumb1.jpg",
    "https://example.com/thumb2.jpg",
    "https://example.com/thumb3.jpg"
  ],
  "productTags": ["新品", "智能识别"],
  "categoryId": 1,
  "subcategoryId": 1,
  "routePath": "/products/category/new-product"
}
```

### 3.4 产品技术规格管理

#### 获取产品技术规格
```
GET /api/admin/products/{productId}/specifications
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "specName": "产品型号",
      "specValue": "P301-2D-2W",
      "sortOrder": 1
    },
    {
      "id": 2,
      "specName": "操作系统",
      "specValue": "Android 8.1",
      "sortOrder": 2
    }
  ]
}
```

#### 批量更新技术规格
```
PUT /api/admin/products/{productId}/specifications
Authorization: Bearer {token}
Content-Type: application/json

{
  "specifications": [
    {
      "id": 1,
      "specName": "产品型号",
      "specValue": "P301-2D-2W",
      "sortOrder": 1
    },
    {
      "specName": "新规格项",
      "specValue": "新规格值",
      "sortOrder": 10
    }
  ]
}
```

### 3.5 产品详情内容管理

#### 获取产品详情内容
```
GET /api/admin/products/{productId}/details
Authorization: Bearer {token}

Response:
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "sectionType": "core_features",
      "title": "核心特点",
      "content": "先进人脸识别技术，采用深度学习算法",
      "icon": "fas fa-star",
      "sortOrder": 1
    }
  ]
}
```

## 4. 新闻管理接口

### 4.1 新闻列表管理

#### 获取新闻列表
```
GET /api/admin/news
Authorization: Bearer {token}
Query Parameters:
- category: 新闻分类（可选）
- isHot: 是否热门（可选）
- isFeatured: 是否首页展示（可选）
- page: 页码
- size: 每页数量

Response:
{
  "code": 200,
  "data": {
    "content": [
      {
        "id": 1,
        "title": "公司新产品发布会成功举办",
        "summary": "我公司于近日成功举办了新产品发布会",
        "content": "详细的新闻内容...",
        "imageUrl": "https://example.com/news1.jpg",
        "category": "company",
        "author": "优卡特",
        "isHot": true,
        "isFeatured": true,
        "viewCount": 1250,
        "publishDate": "2024-01-01T10:00:00Z",
        "isPublished": true
      }
    ],
    "totalElements": 100,
    "totalPages": 10,
    "currentPage": 1
  }
}
```

#### 创建新闻
```
POST /api/admin/news
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "新闻标题",
  "summary": "新闻摘要",
  "content": "详细的新闻内容...",
  "imageUrl": "https://example.com/news-image.jpg",
  "category": "company",
  "author": "优卡特",
  "isHot": false,
  "isFeatured": true,
  "publishDate": "2024-01-01T10:00:00Z"
}
```

## 5. 文件上传接口

### 5.1 图片上传
```
POST /api/admin/upload/image
Authorization: Bearer {token}
Content-Type: multipart/form-data

Form Data:
- file: 图片文件
- type: 图片类型（carousel, product, news, solution等）

Response:
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://cdn.example.com/images/uploaded-image.jpg",
    "filename": "uploaded-image.jpg",
    "size": 1024000
  }
}
```

### 5.2 文档上传
```
POST /api/admin/upload/document
Authorization: Bearer {token}
Content-Type: multipart/form-data

Form Data:
- file: 文档文件
- productId: 关联产品ID（可选）
- title: 文档标题
- description: 文档描述

Response:
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "id": 1,
    "title": "产品使用说明",
    "fileName": "product-manual.pdf",
    "fileUrl": "https://cdn.example.com/docs/product-manual.pdf",
    "fileSize": 2048000,
    "fileType": "application/pdf"
  }
}
```

## 6. 系统管理接口

### 6.1 系统配置管理
```
GET /api/admin/system/config
PUT /api/admin/system/config
```

### 6.2 操作日志
```
GET /api/admin/system/logs
```

## 错误码说明

- 200: 操作成功
- 400: 请求参数错误
- 401: 未授权访问
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

## 权限说明

- `super_admin`: 所有权限
- `admin`: 除用户管理外的所有权限
- `editor`: 内容编辑权限（新闻、产品信息等）

每个接口都需要相应的权限验证。
