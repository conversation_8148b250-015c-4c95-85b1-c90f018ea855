package com.youkate.repository;

import com.youkate.entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 产品数据访问接口
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    
    /**
     * 根据分类查询产品
     */
    List<Product> findByCategoryAndIsActiveTrue(String category);
    
    /**
     * 查询所有启用的产品，按排序字段排序
     */
    List<Product> findByIsActiveTrueOrderBySortOrderAsc();
    
    /**
     * 根据产品名称模糊查询
     */
    @Query("SELECT p FROM Product p WHERE p.name LIKE %:keyword% AND p.isActive = true")
    List<Product> findByNameContaining(@Param("keyword") String keyword);
    
    /**
     * 根据徽章查询产品
     */
    List<Product> findByBadgeAndIsActiveTrue(String badge);
    
    /**
     * 查询推荐产品（前6个）
     */
    @Query("SELECT p FROM Product p WHERE p.isActive = true ORDER BY p.sortOrder ASC")
    List<Product> findFeaturedProducts();
}
