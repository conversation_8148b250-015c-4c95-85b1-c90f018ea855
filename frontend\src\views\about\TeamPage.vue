<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">企业团队</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">认识优卡特的核心管理团队与技术专家</p>
        </div>
      </div>
    </section>

    <!-- 企业部门 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">       
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- 业务部 -->
          <div class="team-card bg-white rounded-lg overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500" data-aos="fade-up">
            <div class="relative overflow-hidden">
              <img src="https://placehold.co/400x400/3B82F6/FFFFFF?text=业务部团队" alt="业务部团队" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500">

            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-800 mb-2">业务部</h3>
              <p class="text-blue-600 font-semibold mb-3">市场开拓与销售管理</p>
              <p class="text-gray-600 text-sm">负责市场开拓、客户关系维护、销售管理等工作，建立了覆盖全国的销售网络，为公司业务拓展提供强有力的支撑。</p>
            </div>
          </div>

          <!-- 研发部 -->
          <div class="team-card bg-white rounded-lg overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500" data-aos="fade-up" data-aos-delay="200">
            <div class="relative overflow-hidden">
              <img src="https://placehold.co/400x400/10B981/FFFFFF?text=研发部团队" alt="研发部团队" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500">

            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-800 mb-2">研发部</h3>
              <p class="text-green-600 font-semibold mb-3">产品创新与技术研发</p>
              <p class="text-gray-600 text-sm">专注于产品创新和技术研发，负责新产品设计、核心算法开发、技术方案制定，推动公司技术创新和产品升级。</p>
            </div>
          </div>

          <!-- 技术部 -->
          <div class="team-card bg-white rounded-lg overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500" data-aos="fade-up" data-aos-delay="400">
            <div class="relative overflow-hidden">
              <img src="https://placehold.co/400x400/8B5CF6/FFFFFF?text=技术部团队" alt="技术部团队" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500">

            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-800 mb-2">技术部</h3>
              <p class="text-purple-600 font-semibold mb-3">系统运维与技术支持</p>
              <p class="text-gray-600 text-sm">负责系统运维、技术支持、产品实施等工作，确保产品稳定运行，为客户提供专业的技术服务和解决方案。</p>
            </div>
          </div>
        </div>
      </div>
    </section>


    <!-- 团队文化 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">团队文化</h2>
          <div class="w-24 h-1 bg-blue-600 mx-auto" data-aos="fade-up" data-aos-delay="200"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-start">
          <div data-aos="fade-right" class="flex flex-col">
            <h3 class="text-3xl font-bold text-gray-800 mb-4 leading-tight">协作·创新·成长</h3>
            <p class="text-gray-600 mb-4 text-lg leading-relaxed">
              我们相信每一位团队成员都是公司最宝贵的财富。在优卡特，我们营造开放包容的工作环境，鼓励创新思维，支持个人成长。
            </p>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
              通过定期的技术分享、团队建设活动和培训计划，我们不断提升团队凝聚力和专业能力，共同创造优异的成果。
            </p>
            
            <div class="grid grid-cols-2 gap-6">
              <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="text-lg font-bold text-blue-600 mb-2">200+</h4>
                <p class="text-gray-600">团队成员</p>
              </div>
              <div class="bg-green-50 p-4 rounded-lg">
                <h4 class="text-lg font-bold text-green-600 mb-2">85%</h4>
                <p class="text-gray-600">本科以上学历</p>
              </div>
              <div class="bg-orange-50 p-4 rounded-lg">
                <h4 class="text-lg font-bold text-orange-600 mb-2">60%</h4>
                <p class="text-gray-600">技术研发人员</p>
              </div>
              <div class="bg-purple-50 p-4 rounded-lg">
                <h4 class="text-lg font-bold text-purple-600 mb-2">5年</h4>
                <p class="text-gray-600">平均工作经验</p>
              </div>
            </div>
          </div>
          
          <div data-aos="fade-left" class="flex flex-col items-start">
            <img src="https://placehold.co/600x400" alt="团队合作办公场景" class="w-full rounded-lg shadow-lg mb-6">
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import AboutNavigation from '@/components/AboutNavigation.vue'

export default {
  name: 'TeamPage',
  components: {
    AboutNavigation
  }
}
</script>
