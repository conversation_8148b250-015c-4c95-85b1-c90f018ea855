package com.youkate.controller;

import com.youkate.entity.FriendlyLink;
import com.youkate.service.FriendlyLinksService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 友情链接控制器
 * 提供友情链接的基本CRUD操作
 */
@RestController
@RequestMapping("/api/friendly-links")
@CrossOrigin(origins = "*")
public class FriendlyLinksController {

    @Autowired
    private FriendlyLinksService friendlyLinksService;

    /**
     * 获取所有活跃的友情链接
     * @return 友情链接列表
     */
    @GetMapping
    public ResponseEntity<List<FriendlyLink>> getAllActiveLinks() {
        List<FriendlyLink> links = friendlyLinksService.getAllActiveLinks();
        return ResponseEntity.ok(links);
    }

    /**
     * 根据ID获取友情链接
     * @param id 链接ID
     * @return 友情链接详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<FriendlyLink> getLinkById(@PathVariable Long id) {
        FriendlyLink link = friendlyLinksService.getLinkById(id);
        if (link != null) {
            return ResponseEntity.ok(link);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 创建新的友情链接（管理员功能）
     * @param link 友情链接对象
     * @return 创建的友情链接
     */
    @PostMapping
    public ResponseEntity<FriendlyLink> createLink(@RequestBody FriendlyLink link) {
        FriendlyLink createdLink = friendlyLinksService.createLink(link);
        return ResponseEntity.ok(createdLink);
    }

    /**
     * 更新友情链接（管理员功能）
     * @param id 链接ID
     * @param link 更新的友情链接对象
     * @return 更新后的友情链接
     */
    @PutMapping("/{id}")
    public ResponseEntity<FriendlyLink> updateLink(@PathVariable Long id, @RequestBody FriendlyLink link) {
        FriendlyLink updatedLink = friendlyLinksService.updateLink(id, link);
        if (updatedLink != null) {
            return ResponseEntity.ok(updatedLink);
        }
        return ResponseEntity.notFound().build();
    }

    /**
     * 删除友情链接（管理员功能）
     * @param id 链接ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteLink(@PathVariable Long id) {
        boolean deleted = friendlyLinksService.deleteLink(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }
}
