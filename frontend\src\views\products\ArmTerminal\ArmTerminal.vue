<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">ARM消费机</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">高性能ARM架构，稳定可靠的消费终端</p>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div>
      <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
    </section>

    <!-- 产品分类介绍 -->
    <section class="py-20 bg-white">
      <div class="w-full">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- 左侧产品分类导航 -->
          <div class="lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16">
            <div class="lg:ml-4 xl:ml-8 2xl:ml-16">
              <ProductNavigation
                :current-category="'arm-terminal'"
                :current-product="currentProduct"
              />
            </div>
          </div>

          <!-- 右侧产品内容 -->
          <div class="lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16">


            <!-- 产品型号展示 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <!-- ARM刷卡消费机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="openProductDetail('YKT-ARM100')">
                  <img src="https://placehold.co/300x200" alt="ARM刷卡消费机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="product-title text-xl font-bold text-gray-800 mb-2
                             transition-colors duration-200 ease-out
                             hover:text-blue-600">YKT-ARM100 刷卡消费机</h3>
                  <p class="text-gray-600 text-sm mb-4">ARM架构IC卡消费终端</p>
                  <router-link to="/products/arm-terminal/arm100" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>

              <!-- ARM二维码消费机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up" data-aos-delay="200">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/arm-terminal/arm200')">
                  <img src="https://placehold.co/300x200" alt="ARM二维码消费机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">YKT-ARM200 二维码消费机</h3>
                  <p class="text-gray-600 text-sm mb-4">ARM架构扫码支付终端</p>
                  <router-link to="/products/arm-terminal/arm200" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>

              <!-- ARM多功能消费机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up" data-aos-delay="400">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/arm-terminal/arm300')">
                  <img src="https://placehold.co/300x200" alt="ARM多功能消费机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">YKT-ARM300 多功能消费机</h3>
                  <p class="text-gray-600 text-sm mb-4">ARM架构多功能终端</p>
                  <router-link to="/products/arm-terminal/arm300" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
    </section>


  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import AOS from 'aos'
import ProductNavigation from '@/components/ProductNavigation.vue'

export default {
  name: 'ArmTerminal',
  components: {
    ProductNavigation
  },
  setup() {
    const route = useRoute()
    const currentProduct = ref(null)

    // 根据路由参数设置当前产品
    if (route.params.productId) {
      currentProduct.value = route.params.productId
    }

    onMounted(() => {
      AOS.init({
        duration: 1000,
        once: true
      })
    })

    // 打开产品详情
    const openProductDetail = (productModel) => {
      // 这里可以跳转到具体的产品详情页面
      console.log('查看产品详情:', productModel)
      // router.push(`/products/arm-terminal/${productModel.toLowerCase()}`)
    }

    // 立即咨询功能
    const consultNow = (productModel) => {
      // 可以打开咨询弹窗或跳转到咨询页面
      alert(`您正在咨询 ${productModel}，我们的客服将尽快与您联系！\n\n联系电话：************\n微信：youkate2024`)
    }

    // 电话咨询功能
    const callPhone = () => {
      // 直接拨打电话
      window.location.href = 'tel:************'
    }

    return {
      currentProduct,
      openProductDetail,
      consultNow,
      callPhone
    }
  }
}
</script>

<style>
/* 产品卡片样式 */
.product-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f3f4f6;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  border-color: #3b82f6;
}

.product-card .product-image-container {
  position: relative;
  overflow: hidden;
}

.product-card:hover .product-image {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.1);
}

.product-card .image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  color: white;
  padding: 15px;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card .product-image-container:hover .image-overlay {
  transform: translateY(0);
}

/* 确保卡片内容区域填满剩余空间 */
.product-card .p-6 {
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* 按钮组样式 */
.product-card button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 48px;
  font-weight: 600;
}

.product-card button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-card button:active {
  transform: translateY(0);
}

/* 立即咨询按钮特效 */
.product-card button.bg-orange-600 {
  background: linear-gradient(135deg, #ea580c, #f97316);
}

.product-card button.bg-orange-600:hover {
  background: linear-gradient(135deg, #dc2626, #ea580c);
  box-shadow: 0 8px 25px rgba(234, 88, 12, 0.4);
}

/* 电话咨询按钮特效 */
.product-card button.bg-blue-600 {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.product-card button.bg-blue-600:hover {
  background: linear-gradient(135deg, #1d4ed8, #2563eb);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
}

/* 按钮组容器 */
.product-card .grid.grid-cols-2 {
  margin-top: auto;
  gap: 0.75rem;
}

.product-card .grid.grid-cols-2 button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 产品特点列表对齐 */
.product-card .space-y-2 {
  min-height: 72px; /* 确保所有卡片特点区域高度一致 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-card {
    margin-bottom: 1.5rem;
  }

  .product-card:hover {
    transform: translateY(-5px);
  }

  .product-card button {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }

  .product-card .grid-cols-2 {
    gap: 0.5rem;
  }
}

/* 图片放大效果优化 */
@media (hover: hover) {
  .product-card .group:hover .product-image {
    transform: scale(1.1);
  }
}

/* 确保布局对齐 */
.product-card .p-6 {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-card .p-6 > div:last-child {
  margin-top: auto;
}



/* 产品详情区域样式优化 */
#product-details {
  scroll-margin-top: 100px;
}



/* 特点图标样式 */
.bg-orange-100 {
  background: linear-gradient(135deg, #fed7aa, #fdba74);
}

/* 响应式优化 */


/* 响应式优化 */
@media (max-width: 1024px) {
  #product-details .grid-cols-1.lg\\:grid-cols-4 {
    gap: 2rem;
  }



  /* 隐藏右侧悬浮区域 */
  .fixed.right-4 {
    display: none !important;
  }
}

@media (max-width: 768px) {
  #product-details .grid-cols-1.lg\\:grid-cols-4 {
    grid-template-columns: 1fr;
  }

  #product-details .lg\\:col-span-2 {
    grid-column: span 1;
  }
}
</style>
