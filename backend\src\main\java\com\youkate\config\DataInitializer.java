package com.youkate.config;

import com.youkate.entity.Product;
import com.youkate.entity.News;
import com.youkate.entity.CompanyInfo;
import com.youkate.repository.ProductRepository;
import com.youkate.repository.NewsRepository;
import com.youkate.repository.CompanyInfoRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 数据初始化器
 */
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {
    
    private final ProductRepository productRepository;
    private final NewsRepository newsRepository;
    private final CompanyInfoRepository companyInfoRepository;
    
    @Override
    public void run(String... args) throws Exception {
        initializeProducts();
        initializeNews();
        initializeCompanyInfo();
    }
    
    private void initializeProducts() {
        if (productRepository.count() == 0) {
            // 产品1 - 安卓人脸消费机
            Product product1 = new Product();
            product1.setName("安卓人脸消费机");
            product1.setDescription("先进人脸识别技术，智能消费新体验");
            product1.setPrice("¥2,999");
            product1.setImageUrl("https://placehold.co/400x300");
            product1.setCategory("terminal");
            product1.setBadge("热销");
            product1.setThemeColor("from-blue-600 to-blue-800");
            product1.setSortOrder(1);
            product1.setFeatures("[\"高精度识别\", \"快速响应\", \"安全可靠\"]");
            productRepository.save(product1);
            
            // 产品2 - ARM消费机
            Product product2 = new Product();
            product2.setName("ARM消费机");
            product2.setDescription("高性能ARM处理器，稳定可靠的消费终端");
            product2.setPrice("¥2,599");
            product2.setImageUrl("https://placehold.co/400x300");
            product2.setCategory("terminal");
            product2.setBadge("推荐");
            product2.setThemeColor("from-green-600 to-green-800");
            product2.setSortOrder(2);
            product2.setFeatures("[\"ARM处理器\", \"稳定可靠\", \"低功耗\"]");
            productRepository.save(product2);
            
            // 产品3 - 人脸测温通道闸机
            Product product3 = new Product();
            product3.setName("人脸测温通道闸机");
            product3.setDescription("智能安防，健康守护，一机多用");
            product3.setPrice("¥8,999");
            product3.setImageUrl("https://placehold.co/400x300");
            product3.setCategory("access");
            product3.setBadge("新品");
            product3.setThemeColor("from-red-600 to-pink-600");
            product3.setSortOrder(3);
            product3.setFeatures("[\"人脸识别\", \"体温检测\", \"通道管理\"]");
            productRepository.save(product3);
            
            // 产品4 - 水/电控机
            Product product4 = new Product();
            product4.setName("水/电控机");
            product4.setDescription("智能水电管理，节能环保新选择");
            product4.setPrice("¥1,899");
            product4.setImageUrl("https://placehold.co/400x300");
            product4.setCategory("control");
            product4.setBadge("节能");
            product4.setThemeColor("from-green-600 to-emerald-600");
            product4.setSortOrder(4);
            product4.setFeatures("[\"智能控制\", \"节能环保\", \"远程管理\"]");
            productRepository.save(product4);
            
            // 产品5 - 门禁机
            Product product5 = new Product();
            product5.setName("门禁机");
            product5.setDescription("智能安防，便捷通行，安全可靠");
            product5.setPrice("¥3,299");
            product5.setImageUrl("https://placehold.co/400x300");
            product5.setCategory("access");
            product5.setBadge("安全");
            product5.setThemeColor("from-purple-700 to-violet-600");
            product5.setSortOrder(5);
            product5.setFeatures("[\"多种识别方式\", \"安全可靠\", \"便捷通行\"]");
            productRepository.save(product5);
            
            // 产品6 - 脸爱云平台
            Product product6 = new Product();
            product6.setName("脸爱云平台");
            product6.setDescription("智能云服务，设备管理，数据分析一体化平台");
            product6.setPrice("¥999/月");
            product6.setImageUrl("https://placehold.co/400x300");
            product6.setCategory("platform");
            product6.setBadge("云平台");
            product6.setThemeColor("from-sky-500 to-blue-600");
            product6.setSortOrder(6);
            product6.setFeatures("[\"云端管理\", \"数据分析\", \"远程监控\"]");
            productRepository.save(product6);
            
            System.out.println("产品数据初始化完成！");
        }
    }
    
    private void initializeNews() {
        if (newsRepository.count() == 0) {
            // 企业新闻
            News news1 = new News();
            news1.setTitle("优卡特荣获2023年度智能设备创新奖");
            news1.setSummary("在刚刚结束的2023年度智能设备行业评选中，优卡特凭借其创新的人脸识别技术和优秀的产品质量...");
            news1.setContent("在刚刚结束的2023年度智能设备行业评选中，优卡特凭借其创新的人脸识别技术和优秀的产品质量，荣获了'2023年度智能设备创新奖'。这一荣誉不仅是对优卡特技术实力的认可，更是对我们持续创新精神的肯定。");
            news1.setImageUrl("https://placehold.co/600x400/0066CC/FFFFFF?text=智慧校园");
            news1.setCategory("company");
            news1.setIsFeatured(true);
            news1.setIsPublished(true);
            news1.setPublishedAt(LocalDateTime.now().minusDays(1));
            newsRepository.save(news1);
            
            // 行业资讯
            News news2 = new News();
            news2.setTitle("人脸识别技术在智慧校园中的应用前景");
            news2.setSummary("随着人工智能技术的快速发展，人脸识别技术在教育行业的应用越来越广泛...");
            news2.setContent("随着人工智能技术的快速发展，人脸识别技术在教育行业的应用越来越广泛。从校园安全管理到学生考勤，从食堂消费到图书馆管理，人脸识别技术正在全面改变传统的校园管理模式。");
            news2.setImageUrl("https://placehold.co/600x400/0088FF/FFFFFF?text=人脸识别");
            news2.setCategory("industry");
            news2.setIsPublished(true);
            news2.setPublishedAt(LocalDateTime.now().minusDays(3));
            newsRepository.save(news2);
            
            // 技术支持
            News news3 = new News();
            news3.setTitle("2024年智能终端设备市场趋势分析");
            news3.setSummary("本文深入分析了智能终端设备市场的发展趋势，为行业发展提供参考...");
            news3.setContent("2024年，智能终端设备市场呈现出快速发展的态势。随着5G技术的普及和人工智能算法的不断优化，智能终端设备在各个行业的应用场景越来越丰富。预计未来三年，市场规模将保持20%以上的年增长率。");
            news3.setImageUrl("https://placehold.co/600x400/00AAFF/FFFFFF?text=市场趋势");
            news3.setCategory("support");
            news3.setIsPublished(true);
            news3.setPublishedAt(LocalDateTime.now().minusDays(2));
            newsRepository.save(news3);
            
            System.out.println("新闻数据初始化完成！共创建3条新闻");
        }
    }
    
    private void initializeCompanyInfo() {
        if (companyInfoRepository.count() == 0) {
            CompanyInfo companyInfo = new CompanyInfo();
            companyInfo.setName("深圳市优卡特实业有限公司");
            companyInfo.setDescription("深圳市优卡特实业有限公司是一家中国领先的一卡通科技公司，用创新的产品和服务提升行业消费管理能力。");
            companyInfo.setAddress("深圳市龙华新区大和路硅谷动力清湖园A9栋2楼");
            companyInfo.setPhone("159-8667-2052");
            companyInfo.setEmail("<EMAIL>");
            companyInfo.setBusinessHours("周一至周五: 9:00 - 18:00");
            companyInfo.setCoordinates("114.058232,22.681899");
            companyInfo.setFoundedYear(2003);
            companyInfo.setEmployeeCount(200);
            companyInfoRepository.save(companyInfo);
            
            System.out.println("公司信息初始化完成！");
        }
    }
}
