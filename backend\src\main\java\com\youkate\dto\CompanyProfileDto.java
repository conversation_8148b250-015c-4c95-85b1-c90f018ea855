package com.youkate.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;
import java.util.List;

/**
 * 公司简介数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyProfileDto {
    
    /**
     * 公司名称
     */
    private String name;
    
    /**
     * 公司描述
     */
    private String description;
    
    /**
     * 公司图片URL
     */
    private String image;
    
    /**
     * 图片描述
     */
    private String imageAlt;
    
    /**
     * 公司详细信息
     */
    private Map<String, String> details;
    
    /**
     * 核心业务列表
     */
    private List<CoreBusinessDto> coreBusinesses;
    
    /**
     * 公司地址
     */
    private AddressDto address;
    
    /**
     * 联系信息
     */
    private ContactInfoDto contactInfo;
    
    /**
     * 核心业务数据传输对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CoreBusinessDto {
        private Long id;
        private String icon;
        private String title;
        private String description;
        private Integer sortOrder;
    }
    
    /**
     * 地址数据传输对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDto {
        private String province;
        private String city;
        private String district;
        private String street;
        private String fullAddress;
        private Double latitude;
        private Double longitude;
    }
    
    /**
     * 联系信息数据传输对象
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfoDto {
        private String phone;
        private String fax;
        private String email;
        private String website;
        private String wechat;
        private String qq;
    }
}
