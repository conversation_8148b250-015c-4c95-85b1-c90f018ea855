<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">智能水控机/电控机系列</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能水电管理，节能环保新选择</p>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div>
      <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
    </section>

    <!-- 产品分类介绍 -->
    <section class="py-20 bg-white">
      <div class="w-full">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- 左侧产品分类导航 -->
          <div class="lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16">
            <div class="lg:ml-4 xl:ml-8 2xl:ml-16">
              <ProductNavigation
                :current-category="'water-control'"
                :current-product="currentProduct"
              />
            </div>
          </div>

          <!-- 右侧产品内容 -->
          <div class="lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16">


            <!-- 产品型号展示 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <!-- 智能一体水控机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/water-control/smart-water')">
                  <img src="https://placehold.co/300x200" alt="智能一体水控机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="product-title text-xl font-bold text-gray-800 mb-2
                             transition-colors duration-200 ease-out
                             hover:text-blue-600">智能一体水控机</h3>
                  <p class="text-gray-600 text-sm mb-4">集成式智能水控终端</p>
                  <router-link to="/products/water-control/smart-water" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>

              <!-- 智能分体水控机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up" data-aos-delay="200">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/water-control/split-water')">
                  <img src="https://placehold.co/300x200" alt="智能分体水控机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">智能分体水控机</h3>
                  <p class="text-gray-600 text-sm mb-4">分体式智能水控终端</p>
                  <router-link to="/products/water-control/split-water" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import AOS from 'aos'
import ProductNavigation from '@/components/ProductNavigation.vue'

export default {
  name: 'WaterElectric',
  components: {
    ProductNavigation
  },
  setup() {
    const route = useRoute()
    const currentProduct = ref(null)

    // 根据路由参数设置当前产品
    if (route.params.productId) {
      currentProduct.value = route.params.productId
    }

    onMounted(() => {
      AOS.init({
        duration: 1000,
        once: true
      })
    })

    return {
      currentProduct
    }
  }
}
</script>

<style>
.product-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f3f4f6;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  border-color: #2563eb;
}

.product-card .product-image-container {
  position: relative;
  overflow: hidden;
}

.product-card:hover .product-image {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.1);
}

.product-card .image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  color: white;
  padding: 15px;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card .product-image-container:hover .image-overlay {
  transform: translateY(0);
}

@media (max-width: 768px) {
  .product-card {
    margin-bottom: 1.5rem;
  }
  
  .product-card:hover {
    transform: translateY(-5px);
  }
}
</style>
