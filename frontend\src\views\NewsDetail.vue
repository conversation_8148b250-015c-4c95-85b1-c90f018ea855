<template>
  <div class="news-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="min-h-screen flex items-center justify-center">
      <div class="loading"></div>
    </div>

    <!-- 新闻不存在 -->
    <div v-else-if="!news" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <i class="fas fa-newspaper text-6xl text-gray-400 mb-4"></i>
        <h2 class="text-2xl font-bold text-gray-600 mb-2">新闻不存在</h2>
        <p class="text-gray-500 mb-6">抱歉，您访问的新闻页面不存在</p>
        <router-link to="/news" class="btn-primary">
          <i class="fas fa-arrow-left mr-2"></i>
          返回新闻中心
        </router-link>
      </div>
    </div>

    <!-- 新闻详情 -->
    <div v-else>
      <!-- 新闻头部 -->
      <section class="pt-24 pb-12 bg-white">
        <div class="container-custom">
          <div class="max-w-4xl mx-auto">
            <!-- 面包屑导航 -->
            <nav class="mb-8" data-aos="fade-up">
              <ol class="flex items-center space-x-2 text-sm text-gray-500">
                <li><router-link to="/" class="hover:text-blue-600">首页</router-link></li>
                <li><i class="fas fa-chevron-right mx-2"></i></li>
                <li><router-link to="/news" class="hover:text-blue-600">新闻中心</router-link></li>
                <li><i class="fas fa-chevron-right mx-2"></i></li>
                <li><router-link :to="`/news/${getCategoryPath()}`" class="hover:text-blue-600">{{ categoryName }}</router-link></li>
                <li><i class="fas fa-chevron-right mx-2"></i></li>
                <li class="text-gray-700">{{ news.title }}</li>
              </ol>
            </nav>

            <!-- 新闻标题 -->
            <h1 class="text-4xl md:text-5xl font-bold mb-6" data-aos="fade-up">
              {{ news.title }}
            </h1>

            <!-- 新闻元信息 -->
            <div class="flex flex-wrap items-center gap-6 mb-8 text-gray-600" data-aos="fade-up" data-aos-delay="200">
              <div class="flex items-center">
                <i class="fas fa-calendar-alt mr-2"></i>
                <span>{{ formatDate(news.publishedAt) }}</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-user mr-2"></i>
                <span>{{ news.author || '优卡特' }}</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-tag mr-2"></i>
                <span 
                  class="px-2 py-1 rounded text-sm"
                  :class="categoryClass"
                >
                  {{ categoryName }}
                </span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-eye mr-2"></i>
                <span>{{ news.viewCount || 0 }} 次浏览</span>
              </div>
            </div>

            <!-- 新闻摘要 -->
            <!-- 新闻摘要 -->
            <div v-if="news.summary" class="bg-blue-50 border-l-4 border-blue-500 p-6 mb-2" data-aos="fade-up" data-aos-delay="300">
              <p class="text-lg text-gray-700 leading-relaxed">{{ news.summary }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 新闻内容 -->
      <section class="pt-4 pb-12 bg-white">
        <div class="container-custom">
          <div class="max-w-4xl mx-auto">
            <!-- 新闻图片 -->
            <div v-if="news.imageUrl" class="mb-4 relative" data-aos="fade-up">
              <!-- 图片加载状态 -->
              <div v-if="imageLoading" class="flex justify-center items-center h-64 bg-gray-100 rounded-2xl mb-4">
                <div class="loading-spinner"></div>
                <span class="ml-2 text-gray-500">图片加载中...</span>
              </div>
              <img 
                v-show="!imageLoading"
                :src="news.imageUrl" 
                :alt="news.title" 
                class="w-full h-auto rounded-2xl shadow-lg"
                loading="lazy"
                style="max-height: 500px; object-fit: cover;"
                @error="handleImageError"
                @load="handleImageLoad"
              >
            </div>

            <!-- 新闻正文 -->
            <div class="prose prose-lg max-w-none" data-aos="fade-up" data-aos-delay="200">
              <div v-html="formatContent(news.content)"></div>
            </div>

          </div>
        </div>
      </section>

      <!-- 相关新闻 -->
      <section class="py-20 bg-gray-50">
        <div class="container-custom">
          <div class="text-center mb-12" data-aos="fade-up">
            <h2 class="text-3xl font-bold mb-4">相关新闻</h2>
            <p class="text-lg text-gray-600">您可能还感兴趣的其他新闻</p>
          </div>
          
          <div v-if="relatedNews.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <NewsCard 
              v-for="relatedNewsItem in relatedNews" 
              :key="relatedNewsItem.id" 
              :news="relatedNewsItem"
            />
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useNewsStore } from '../stores/news'
import NewsCard from '../components/news/NewsCard.vue'

export default {
  name: 'NewsDetail',
  components: {
    NewsCard
  },
  setup() {
    const route = useRoute()
    const newsStore = useNewsStore()
    
    const loading = ref(false)
    const news = ref(null)
    const relatedNews = ref([])
    const imageLoading = ref(true)
    
    // 分类名称
    const categoryName = computed(() => {
      const categoryMap = {
        'company': '企业新闻',
        'industry': '行业资讯',
        'support': '技术支持'
      }
      return categoryMap[news.value?.category] || '其他'
    })
    
    // 分类样式
    const categoryClass = computed(() => {
      const classMap = {
        'company': 'bg-blue-100 text-blue-800',
        'industry': 'bg-green-100 text-green-800',
        'support': 'bg-red-100 text-red-800'
      }
      return classMap[news.value?.category] || 'bg-gray-100 text-gray-800'
    })
    
    // 获取分类路径
    const getCategoryPath = () => {
      const pathMap = {
        'company': 'company',
        'industry': 'industry',
        'support': 'support'
      }
      return pathMap[news.value?.category] || ''
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 格式化内容
    // 格式化内容
    const formatContent = (content) => {
      if (!content) return ''
      
      // 将换行符转换为段落
      return content
        .split('\n')
        .filter(paragraph => paragraph.trim())
        .map(paragraph => `<p class="mb-4">${paragraph.trim()}</p>`)
        .join('')
    }
    
    // 处理图片加载完成
    const handleImageLoad = () => {
      imageLoading.value = false
    }
    
    // 处理图片加载错误
    const handleImageError = (event) => {
      imageLoading.value = false
      // 设置默认图片
      event.target.src = 'https://placehold.co/800x400/f3f4f6/9ca3af?text=图片加载失败'
    }
    
    // 加载新闻详情
    const loadNews = async (newsId) => {
      loading.value = true
      try {
        const newsData = await newsStore.fetchNewsById(newsId)
        news.value = newsData
        
        // 加载相关新闻
        if (newsData?.category) {
          const categoryNews = await newsStore.fetchNewsByCategory(newsData.category)
          relatedNews.value = categoryNews
            .filter(n => n.id !== newsData.id)
            .slice(0, 3)
        }
      } catch (error) {
        console.error('加载新闻详情失败:', error)
        news.value = null
      } finally {
        loading.value = false
      }
    }
    
    // 监听路由变化
    watch(() => route.params.id, (newId) => {
      if (newId) {
        loadNews(parseInt(newId))
      }
    }, { immediate: true })
    
    onMounted(() => {
      const newsId = route.params.id
      if (newsId) {
        loadNews(parseInt(newsId))
      }
    })
    
    return {
      loading,
      news,
      relatedNews,
      imageLoading,
      categoryName,
      categoryClass,
      getCategoryPath,
      formatDate,
      formatContent,
      handleImageLoad,
      handleImageError
    }
  }
}
</script>

<style scoped>
.prose {
  color: #374151;
  line-height: 1.75;
}

.prose p {
  margin-bottom: 1.25em;
}

.prose h2 {
  font-size: 1.5em;
  font-weight: 700;
  margin-top: 2em;
  margin-bottom: 1em;
}

.prose h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
}

.prose ul, .prose ol {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose blockquote {
  font-style: italic;
  border-left: 0.25rem solid #e5e7eb;
  padding-left: 1em;
  margin: 1.6em 0;
}

/* 加载动画 */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
