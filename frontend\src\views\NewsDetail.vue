<template>
  <div class="news-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="min-h-screen flex items-center justify-center">
      <div class="loading"></div>
    </div>

    <!-- 新闻不存在 -->
    <div v-else-if="!news" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <i class="fas fa-newspaper text-6xl text-gray-400 mb-4"></i>
        <h2 class="text-2xl font-bold text-gray-600 mb-2">新闻不存在</h2>
        <p class="text-gray-500 mb-6">抱歉，您访问的新闻页面不存在</p>
        <router-link to="/news" class="btn-primary">
          <i class="fas fa-arrow-left mr-2"></i>
          返回新闻中心
        </router-link>
      </div>
    </div>

    <!-- 新闻详情 -->
    <div v-else>
      <!-- 新闻头部 -->
      <section class="pt-24 pb-12 bg-white">
        <div class="container-custom">
          <div class="max-w-4xl mx-auto">
            <!-- 面包屑导航 -->
            <nav class="mb-8" data-aos="fade-up">
              <ol class="flex items-center space-x-2 text-sm text-gray-500">
                <li><router-link to="/" class="hover:text-blue-600">首页</router-link></li>
                <li><i class="fas fa-chevron-right mx-2"></i></li>
                <li><router-link to="/news" class="hover:text-blue-600">新闻中心</router-link></li>
                <li><i class="fas fa-chevron-right mx-2"></i></li>
                <li><router-link :to="`/news/${getCategoryPath()}`" class="hover:text-blue-600">{{ categoryName }}</router-link></li>
                <li><i class="fas fa-chevron-right mx-2"></i></li>
                <li class="text-gray-700">{{ news.title }}</li>
              </ol>
            </nav>

            <!-- 新闻标题 -->
            <h1 class="text-4xl md:text-5xl font-bold mb-6" data-aos="fade-up">
              {{ news.title }}
            </h1>

            <!-- 新闻元信息 -->
            <div class="flex flex-wrap items-center gap-6 mb-8 text-gray-600" data-aos="fade-up" data-aos-delay="200">
              <div class="flex items-center">
                <i class="fas fa-calendar-alt mr-2"></i>
                <span>{{ formatDate(news.publishedAt) }}</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-user mr-2"></i>
                <span>{{ news.author || '优卡特' }}</span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-tag mr-2"></i>
                <span 
                  class="px-2 py-1 rounded text-sm"
                  :class="categoryClass"
                >
                  {{ categoryName }}
                </span>
              </div>
              <div class="flex items-center">
                <i class="fas fa-eye mr-2"></i>
                <span>{{ news.viewCount || 0 }} 次浏览</span>
              </div>
            </div>

            <!-- 新闻摘要 -->
            <!-- 新闻摘要 -->
            <div v-if="news.summary" class="bg-blue-50 border-l-4 border-blue-500 p-6 mb-2" data-aos="fade-up" data-aos-delay="300">
              <p class="text-lg text-gray-700 leading-relaxed">{{ news.summary }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 新闻内容 -->
      <section class="pt-4 pb-12 bg-white">
        <div class="container-custom">
          <div class="max-w-4xl mx-auto">
            <!-- 新闻图片 -->
            <div v-if="news.imageUrl" class="mb-4 relative" data-aos="fade-up">
              <!-- 图片加载状态 -->
              <div v-if="imageLoading" class="flex justify-center items-center h-64 bg-gray-100 rounded-2xl mb-4">
                <div class="loading-spinner"></div>
                <span class="ml-2 text-gray-500">图片加载中...</span>
              </div>
              <img 
                v-show="!imageLoading"
                :src="news.imageUrl" 
                :alt="news.title" 
                class="w-full h-auto rounded-2xl shadow-lg"
                loading="lazy"
                style="max-height: 500px; object-fit: cover;"
                @error="handleImageError"
                @load="handleImageLoad"
              >
            </div>

            <!-- 新闻正文 -->
            <div class="prose prose-lg max-w-none" data-aos="fade-up" data-aos-delay="200">
              <div v-html="formatContent(news.content)"></div>
            </div>

          </div>
        </div>
      </section>

      <!-- 相关新闻 -->
      <section class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
          <div class="max-w-5xl mx-auto">
            <div class="text-center mb-12" data-aos="fade-up">
              <h2 class="text-3xl font-bold mb-4">相关新闻</h2>
            </div>
            
            <div v-if="relatedNews.length > 0" class="flex flex-col gap-6 max-w-6xl mx-auto">
            <a
              href="#"
              @click.prevent="$router.push(`/news/detail/${relatedNewsItem.id}`)"
              v-for="relatedNewsItem in relatedNews"
              :key="relatedNewsItem.id"
              class="news-card bg-gradient-to-br from-white to-gray-50 rounded-2xl overflow-hidden shadow-md hover:shadow-2xl border-0 flex group transform hover:-translate-y-1 transition-all duration-500 block relative w-full"
              data-aos="fade-up"
              style="height: 180px;"
            >
              <!-- 装饰性渐变边框 -->
              <div class="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div class="absolute inset-0.5 bg-gradient-to-br from-white to-gray-50 rounded-2xl"></div>
              
              <!-- 内容区域 -->
              <div class="relative z-10 flex w-full">
                <div class="w-40 h-full overflow-hidden rounded-l-2xl relative">
                  <img 
                    :src="relatedNewsItem.image" 
                    :alt="relatedNewsItem.title" 
                    class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  >
                  <!-- 图片遮罩 -->
                  <div class="absolute inset-0 bg-gradient-to-r from-transparent to-black/10 group-hover:to-black/20 transition-all duration-500"></div>
                </div>
                
                <div class="p-5 flex-1 flex flex-col justify-between relative">
                  <!-- 背景装饰 -->
                  <div class="absolute top-3 right-3 w-6 h-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full opacity-50 group-hover:opacity-100 group-hover:scale-125 transition-all duration-500"></div>
                  
                  <div>
                    <div class="flex items-center mb-3">
                      <span :class="relatedNewsItem.categoryClass" class="px-3 py-1 rounded-full text-xs font-medium shadow-sm group-hover:shadow-md transition-shadow duration-300">
                        {{ relatedNewsItem.categoryName }}
                      </span>
                      <span class="text-gray-400 text-xs ml-3">{{ relatedNewsItem.date }}</span>
                    </div>
                    
                    <h4 class="text-base font-semibold text-gray-800 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text transition-all duration-500 cursor-pointer line-clamp-2 leading-tight mb-3">
                      {{ relatedNewsItem.title }}
                    </h4>
                  </div>
                  
                  <p class="text-gray-500 text-sm line-clamp-3 leading-relaxed">{{ relatedNewsItem.excerpt }}</p>
                  
                  <!-- 阅读指示器 -->
                  <div class="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <i class="fas fa-arrow-right text-white text-xs"></i>
                    </div>
                  </div>
                </div>
              </div>
            </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useNewsStore } from '../stores/news'
import NewsCard from '../components/news/NewsCard.vue'

export default {
  name: 'NewsDetail',
  components: {
    NewsCard
  },
  setup() {
    const route = useRoute()
    const newsStore = useNewsStore()
    
    const loading = ref(false)
    const news = ref(null)
    const relatedNews = ref([])
    const imageLoading = ref(true)
    
    // 分类名称
    const categoryName = computed(() => {
      const categoryMap = {
        'company': '企业新闻',
        'industry': '行业资讯',
        'support': '技术支持'
      }
      return categoryMap[news.value?.category] || '其他'
    })
    
    // 分类样式
    const categoryClass = computed(() => {
      const classMap = {
        'company': 'bg-blue-100 text-blue-800',
        'industry': 'bg-green-100 text-green-800',
        'support': 'bg-red-100 text-red-800'
      }
      return classMap[news.value?.category] || 'bg-gray-100 text-gray-800'
    })
    
    // 获取分类路径
    const getCategoryPath = () => {
      const pathMap = {
        'company': 'company',
        'industry': 'industry',
        'support': 'support'
      }
      return pathMap[news.value?.category] || ''
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    // 格式化内容
    // 格式化内容
    const formatContent = (content) => {
      if (!content) return ''
      
      // 将换行符转换为段落
      return content
        .split('\n')
        .filter(paragraph => paragraph.trim())
        .map(paragraph => `<p class="mb-4">${paragraph.trim()}</p>`)
        .join('')
    }
    
    // 处理图片加载完成
    const handleImageLoad = () => {
      imageLoading.value = false
    }
    
    // 处理图片加载错误
    const handleImageError = (event) => {
      imageLoading.value = false
      // 设置默认图片
      event.target.src = 'https://placehold.co/800x400/f3f4f6/9ca3af?text=图片加载失败'
    }
    
    // 加载新闻详情
    const loadNews = async (newsId) => {
      loading.value = true
      try {
        // 模拟新闻数据（从新闻中心获取）
        const allNewsData = [
          {
            id: 1,
            title: '优卡特发布新一代人脸识别终端',
            excerpt: '公司新一代人脸识别终端采用深度学习算法，识别速度和准确率大幅提升，为客户提供更优质的服务体验。',
            category: 'company',
            categoryName: '企业新闻',
            categoryClass: 'bg-blue-100 text-blue-600',
            date: '2024-01-15',
            image: 'https://placehold.co/400x300',
            content: `优卡特科技今日正式发布了新一代人脸识别终端产品，该产品采用最新的深度学习算法，在识别速度和准确率方面都有显著提升。

新产品具有以下特点：
1. 识别速度提升50%，响应时间低于0.3秒
2. 识别准确率达到99.7%，在复杂光线环境下表现优异
3. 支持活体检测，有效防止照片和视频攻击
4. 采用工业级设计，适应各种恶劣环境

该产品将广泛应用于智慧校园、企业园区、政府机关等场景，为用户提供更加安全、便捷的身份认证服务。`,
            publishedAt: '2024-01-15T10:00:00Z',
            author: '优卡特科技',
            viewCount: 1250,
            imageUrl: 'https://placehold.co/800x400',
            summary: '优卡特科技发布新一代人脸识别终端，采用深度学习算法，识别速度和准确率大幅提升。'
          },
          {
            id: 2,
            title: '优卡特参加2024智慧校园建设展览会',
            excerpt: '本次展会上，优卡特展示了最新的智慧校园一卡通解决方案，引起了广泛关注和好评。',
            category: 'company',
            categoryName: '企业新闻',
            categoryClass: 'bg-blue-100 text-blue-600',
            date: '2024-01-10',
            image: 'https://placehold.co/400x300',
            content: `2024年智慧校园建设展览会在深圳会展中心隆重举行，优卡特科技携最新的智慧校园一卡通解决方案参展。

展会亮点：
1. 展示了完整的智慧校园生态系统
2. 现场演示人脸识别、一卡通支付等功能
3. 与多家高校达成合作意向
4. 获得了参展商和观众的一致好评

优卡特的智慧校园解决方案涵盖了门禁管理、消费支付、考勤管理、访客管理等多个应用场景，为校园数字化转型提供了完整的技术支撑。`,
            publishedAt: '2024-01-10T14:30:00Z',
            author: '优卡特科技',
            viewCount: 890,
            imageUrl: 'https://placehold.co/800x400',
            summary: '优卡特参加2024智慧校园建设展览会，展示最新一卡通解决方案，获得广泛关注。'
          },
          {
            id: 3,
            title: '公司获得ISO9001质量管理体系认证',
            excerpt: '经过严格的审核，优卡特成功获得ISO9001质量管理体系认证，标志着公司在质量管理方面达到国际先进水平。',
            category: 'company',
            categoryName: '企业新闻',
            categoryClass: 'bg-blue-100 text-blue-600',
            date: '2024-01-05',
            image: 'https://placehold.co/400x300',
            content: `经过为期三个月的严格审核，优卡特科技成功获得ISO9001质量管理体系认证，这标志着公司在质量管理方面达到了国际先进水平。

认证意义：
1. 提升了公司的质量管理水平
2. 增强了客户对产品质量的信心
3. 为公司进入国际市场奠定了基础
4. 完善了内部管理流程和制度

ISO9001认证是国际上最权威的质量管理体系认证之一，获得此认证表明优卡特在产品设计、生产、服务等各个环节都建立了完善的质量管理体系。`,
            publishedAt: '2024-01-05T09:15:00Z',
            author: '优卡特科技',
            viewCount: 756,
            imageUrl: 'https://placehold.co/800x400',
            summary: '优卡特成功获得ISO9001质量管理体系认证，质量管理达到国际先进水平。'
          },
          {
            id: 4,
            title: '2024年智能一卡通市场分析报告',
            excerpt: '根据最新市场调研数据，2024年智能一卡通市场规模预计将达到500亿元，同比增长15%。',
            category: 'industry',
            categoryName: '行业资讯',
            categoryClass: 'bg-green-100 text-green-600',
            date: '2024-01-12',
            image: 'https://placehold.co/400x300'
          },
          {
            id: 5,
            title: '人脸识别技术在校园安全中的应用',
            excerpt: '随着人工智能技术的快速发展，人脸识别技术在校园安全管理中发挥着越来越重要的作用。',
            category: 'industry',
            categoryName: '行业资讯',
            categoryClass: 'bg-green-100 text-green-600',
            date: '2024-01-08',
            image: 'https://placehold.co/400x300'
          },
          {
            id: 6,
            title: '数据安全法实施细则解读',
            excerpt: '新版数据安全法实施细则对智能设备数据采集、存储、使用提出了更严格的要求。',
            category: 'industry',
            categoryName: '行业资讯',
            categoryClass: 'bg-green-100 text-green-600',
            date: '2024-01-03',
            image: 'https://placehold.co/400x300'
          }
        ]
        
        // 根据ID查找新闻
        const newsData = allNewsData.find(n => n.id === parseInt(newsId))
        news.value = newsData
        
        // 加载相关新闻（从所有新闻中随机选择3个，排除当前新闻）
        if (newsData) {
          const otherNews = allNewsData.filter(n => n.id !== newsData.id)
          // 随机选择3个相关新闻
          const shuffled = otherNews.sort(() => 0.5 - Math.random())
          relatedNews.value = shuffled.slice(0, 3)
        }
      } catch (error) {
        console.error('加载新闻详情失败:', error)
        news.value = null
      } finally {
        loading.value = false
      }
    }
    
    // 监听路由变化
    watch(() => route.params.id, (newId) => {
      if (newId) {
        loadNews(parseInt(newId))
      }
    }, { immediate: true })
    
    onMounted(() => {
      const newsId = route.params.id
      if (newsId) {
        loadNews(parseInt(newsId))
      }
    })
    
    return {
      loading,
      news,
      relatedNews,
      imageLoading,
      categoryName,
      categoryClass,
      getCategoryPath,
      formatDate,
      formatContent,
      handleImageLoad,
      handleImageError
    }
  }
}
</script>

<style scoped>
.prose {
  color: #374151;
  line-height: 1.75;
}

.prose p {
  margin-bottom: 1.25em;
}

.prose h2 {
  font-size: 1.5em;
  font-weight: 700;
  margin-top: 2em;
  margin-bottom: 1em;
}

.prose h3 {
  font-size: 1.25em;
  font-weight: 600;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
}

.prose ul, .prose ol {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose blockquote {
  font-style: italic;
  border-left: 0.25rem solid #e5e7eb;
  padding-left: 1em;
  margin: 1.6em 0;
}

/* 加载动画 */
.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
