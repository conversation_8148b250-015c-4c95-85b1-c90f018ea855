package com.youkate.service;

import com.youkate.dto.CompanyProfileDto;
import com.youkate.dto.ContactFormDto;
import com.youkate.dto.TeamMemberDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 公司信息服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CompanyService {
    
    /**
     * 获取公司简介信息
     */
    public CompanyProfileDto getCompanyProfile() {
        log.info("获取公司简介信息");
        
        // 公司基本信息
        Map<String, String> details = new LinkedHashMap<>();
        details.put("公司名称", "深圳市优卡特实业有限公司");
        details.put("成立时间", "2003年");
        details.put("注册资本", "5000万元人民币");
        details.put("企业性质", "有限责任公司");
        details.put("所属行业", "智能一卡通设备制造");
        details.put("员工人数", "200+人");
        details.put("公司地址", "深圳市龙华新区大和路硅谷动力清湖园A9栋2楼");
        
        // 核心业务
        List<CompanyProfileDto.CoreBusinessDto> coreBusinesses = Arrays.asList(
            CompanyProfileDto.CoreBusinessDto.builder()
                .id(1L)
                .icon("fas fa-microchip")
                .title("设备研发制造")
                .description("专业研发生产智能消费机、门禁设备、人脸识别终端等一卡通核心设备")
                .sortOrder(1)
                .build(),
            CompanyProfileDto.CoreBusinessDto.builder()
                .id(2L)
                .icon("fas fa-cogs")
                .title("系统集成服务")
                .description("提供完整的一卡通系统集成方案，包括软件开发、系统部署、技术支持等")
                .sortOrder(2)
                .build(),
            CompanyProfileDto.CoreBusinessDto.builder()
                .id(3L)
                .icon("fas fa-headset")
                .title("技术支持服务")
                .description("7×24小时技术支持，专业的售后服务团队，确保客户系统稳定运行")
                .sortOrder(3)
                .build()
        );
        
        // 地址信息
        CompanyProfileDto.AddressDto address = CompanyProfileDto.AddressDto.builder()
            .province("广东省")
            .city("深圳市")
            .district("龙华区")
            .street("大和路硅谷动力清湖园A9栋2楼")
            .fullAddress("深圳市龙华新区大和路硅谷动力清湖园A9栋2楼")
            .latitude(22.681899)
            .longitude(114.058232)
            .build();
        
        // 联系信息
        CompanyProfileDto.ContactInfoDto contactInfo = CompanyProfileDto.ContactInfoDto.builder()
            .phone("159-8667-2052")
            .fax("159-8667-2052")
            .email("<EMAIL>")
            .website("www.youkate.com")
            .wechat("youkate_official")
            .qq("*********")
            .build();
        
        return CompanyProfileDto.builder()
            .name("深圳市优卡特实业有限公司")
            .description("深圳市优卡特实业有限公司成立于2003年，是一家专注于智能一卡通系统解决方案的国家高新技术企业。公司致力于为客户提供高品质的智能卡设备和完善的系统解决方案。")
            .image("/images/company-building.jpg")
            .imageAlt("优卡特公司大楼")
            .details(details)
            .coreBusinesses(coreBusinesses)
            .address(address)
            .contactInfo(contactInfo)
            .build();
    }
    
    /**
     * 获取公司基本信息
     */
    public Map<String, Object> getCompanyBasicInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", "深圳市优卡特实业有限公司");
        info.put("establishedYear", "2003");
        info.put("employeeCount", "200+");
        info.put("address", "深圳市龙华新区大和路硅谷动力清湖园A9栋2楼");
        info.put("phone", "159-8667-2052");
        info.put("email", "<EMAIL>");
        return info;
    }
    
    /**
     * 获取公司发展历程
     */
    public List<Map<String, Object>> getCompanyHistory() {
        return Arrays.asList(
            createHistoryItem("2003", "公司成立", "深圳市优卡特实业有限公司正式成立，开始专注于智能卡设备研发"),
            createHistoryItem("2005", "技术突破", "成功研发第一代智能消费机，获得多项技术专利"),
            createHistoryItem("2008", "市场拓展", "产品进入全国市场，客户覆盖教育、企业、医疗等多个行业"),
            createHistoryItem("2012", "技术升级", "推出人脸识别系列产品，引领行业技术发展"),
            createHistoryItem("2015", "规模扩张", "公司规模扩大，员工超过100人，年产值突破5000万"),
            createHistoryItem("2018", "云平台上线", "脸爱云平台正式上线，实现设备云端管理"),
            createHistoryItem("2020", "疫情应对", "快速推出测温产品，助力疫情防控"),
            createHistoryItem("2023", "持续创新", "公司成立20周年，继续在智能化道路上前行")
        );
    }
    
    private Map<String, Object> createHistoryItem(String year, String title, String description) {
        Map<String, Object> item = new HashMap<>();
        item.put("year", year);
        item.put("title", title);
        item.put("description", description);
        return item;
    }
    
    /**
     * 获取团队成员信息
     */
    public List<TeamMemberDto> getTeamMembers() {
        return Arrays.asList(
            TeamMemberDto.builder()
                .id(1L)
                .name("张明华")
                .position("总经理")
                .department("管理层")
                .avatar("/images/team/ceo.jpg")
                .bio("拥有20年企业管理经验，带领公司从初创发展为行业领军企业")
                .experience(20)
                .education("清华大学工商管理硕士")
                .email("<EMAIL>")
                .isVisible(true)
                .sortOrder(1)
                .joinDate("2003-01")
                .skills(Arrays.asList("企业管理", "战略规划", "市场开拓"))
                .achievements(Arrays.asList("带领公司20年稳健发展", "获得多项行业荣誉"))
                .build(),
            TeamMemberDto.builder()
                .id(2L)
                .name("李建国")
                .position("技术总监")
                .department("技术部")
                .avatar("/images/team/cto.jpg")
                .bio("资深技术专家，主导公司多项核心技术研发")
                .experience(15)
                .education("北京理工大学计算机硕士")
                .email("<EMAIL>")
                .isVisible(true)
                .sortOrder(2)
                .joinDate("2005-03")
                .skills(Arrays.asList("软件开发", "系统架构", "技术管理"))
                .achievements(Arrays.asList("获得15项技术专利", "主导多个重大项目"))
                .build()
        );
    }
    
    /**
     * 获取公司荣誉信息
     */
    public Map<String, Object> getCompanyHonors() {
        Map<String, Object> honors = new HashMap<>();
        honors.put("patents", 50);
        honors.put("awards", 20);
        honors.put("certifications", 15);
        honors.put("creditRating", "AAA");
        return honors;
    }
    
    /**
     * 获取招聘信息
     */
    public Map<String, Object> getCareersInfo() {
        Map<String, Object> careers = new HashMap<>();
        careers.put("openPositions", 8);
        careers.put("departments", Arrays.asList("技术部", "销售部", "市场部", "人事部"));
        careers.put("benefits", Arrays.asList("五险一金", "年终奖", "带薪年假", "培训机会"));
        return careers;
    }
    
    /**
     * 处理联系表单
     */
    public boolean processContactForm(ContactFormDto contactForm) {
        log.info("处理联系表单: 姓名={}, 邮箱={}, 主题={}", 
                contactForm.getName(), contactForm.getEmail(), contactForm.getSubject());
        
        try {
            // 这里可以添加邮件发送、数据库保存等逻辑
            // 目前返回成功状态
            return true;
        } catch (Exception e) {
            log.error("处理联系表单失败", e);
            return false;
        }
    }
    
    /**
     * 获取公司统计数据
     */
    public Map<String, Object> getCompanyStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("customers", "10000+");
        stats.put("projects", "5000+");
        stats.put("patents", "50+");
        stats.put("experience", "20+");
        return stats;
    }
    
    /**
     * 获取成功案例
     */
    public List<Map<String, Object>> getSuccessCases() {
        return Arrays.asList(
            createCase("某大学一卡通系统", "为某知名大学提供完整的校园一卡通解决方案", "/images/cases/university.jpg"),
            createCase("企业园区智能化", "为大型企业园区提供智能门禁和消费系统", "/images/cases/enterprise.jpg"),
            createCase("医院就诊卡系统", "为三甲医院提供智能就诊卡管理系统", "/images/cases/hospital.jpg")
        );
    }
    
    private Map<String, Object> createCase(String title, String description, String image) {
        Map<String, Object> caseItem = new HashMap<>();
        caseItem.put("title", title);
        caseItem.put("description", description);
        caseItem.put("image", image);
        return caseItem;
    }
    
    /**
     * 获取客户评价
     */
    public List<Map<String, Object>> getTestimonials() {
        return Arrays.asList(
            createTestimonial("张总", "某大学", "优卡特的产品质量很好，服务也很到位", 5),
            createTestimonial("李经理", "某企业", "系统稳定可靠，技术支持及时", 5),
            createTestimonial("王主任", "某医院", "解决了我们的实际需求，非常满意", 4)
        );
    }

    private Map<String, Object> createTestimonial(String name, String company, String content, int rating) {
        Map<String, Object> testimonial = new HashMap<>();
        testimonial.put("name", name);
        testimonial.put("company", company);
        testimonial.put("content", content);
        testimonial.put("rating", rating);
        return testimonial;
    }
}
