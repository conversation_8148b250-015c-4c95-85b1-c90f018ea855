{"name": "@tanstack/vue-virtual", "version": "3.13.12", "description": "Headless UI for virtualizing scrollable elements in Vue", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/virtual.git", "directory": "packages/vue-virtual"}, "homepage": "https://tanstack.com/virtual", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "keywords": ["react", "vue", "solid", "svelte", "virtual", "virtual-core", "datagrid"], "type": "module", "types": "dist/esm/index.d.ts", "main": "dist/cjs/index.cjs", "module": "dist/esm/index.js", "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "./package.json": "./package.json"}, "sideEffects": false, "files": ["dist", "src"], "dependencies": {"@tanstack/virtual-core": "3.13.12"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "vue": "^3.5.16"}, "peerDependencies": {"vue": "^2.7.0 || ^3.0.0"}, "scripts": {"clean": "premove ./dist ./coverage", "test:eslint": "eslint ./src", "test:types": "tsc", "test:build": "publint --strict", "build": "vite build"}}