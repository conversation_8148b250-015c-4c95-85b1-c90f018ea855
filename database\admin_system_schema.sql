-- 后台管理系统数据库设计
-- 深圳市优卡特实业有限公司网站管理系统

-- 1. 轮播图管理表
CREATE TABLE carousel_slides (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '轮播图标题',
    subtitle VARCHAR(300) COMMENT '轮播图副标题',
    image_url VARCHAR(500) NOT NULL COMMENT '轮播图图片URL',
    link_url VARCHAR(500) COMMENT '点击跳转链接',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 解决方案管理表
CREATE TABLE solutions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '解决方案标题',
    description TEXT COMMENT '解决方案描述',
    image_url VARCHAR(500) COMMENT '解决方案图片URL',
    banner_image_url VARCHAR(500) COMMENT '横幅图片URL',
    content_images TEXT COMMENT '内容图片JSON数组',
    badge VARCHAR(50) COMMENT '标签（热门、推荐等）',
    features JSON COMMENT '特性标签JSON数组',
    route_path VARCHAR(200) COMMENT '路由路径',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. 核心产品管理表
CREATE TABLE core_products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '产品标题',
    description TEXT COMMENT '产品描述',
    image_url VARCHAR(500) COMMENT '产品图片URL',
    badge VARCHAR(50) COMMENT '产品标签',
    features JSON COMMENT '产品特性JSON数组',
    route_path VARCHAR(200) COMMENT '路由路径',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 产品分类管理表（父系列）
CREATE TABLE product_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '分类代码',
    description TEXT COMMENT '分类描述',
    image_url VARCHAR(500) COMMENT '分类图片URL',
    icon VARCHAR(100) COMMENT '分类图标',
    route_path VARCHAR(200) COMMENT '路由路径',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 5. 产品子系列管理表
CREATE TABLE product_subcategories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_id BIGINT NOT NULL COMMENT '父分类ID',
    name VARCHAR(100) NOT NULL COMMENT '子系列名称',
    code VARCHAR(50) NOT NULL COMMENT '子系列代码',
    description TEXT COMMENT '子系列描述',
    image_url VARCHAR(500) COMMENT '子系列图片URL',
    route_path VARCHAR(200) COMMENT '路由路径',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE CASCADE
);

-- 6. 产品详情管理表
CREATE TABLE products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_id BIGINT NOT NULL COMMENT '分类ID',
    subcategory_id BIGINT COMMENT '子分类ID',
    name VARCHAR(200) NOT NULL COMMENT '产品名称',
    model VARCHAR(100) COMMENT '产品型号',
    description TEXT COMMENT '产品描述',
    main_image_url VARCHAR(500) COMMENT '主图片URL',
    thumbnail_images JSON COMMENT '缩略图JSON数组（最多5张）',
    product_tags JSON COMMENT '产品标签JSON数组',
    route_path VARCHAR(200) COMMENT '路由路径',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE CASCADE,
    FOREIGN KEY (subcategory_id) REFERENCES product_subcategories(id) ON DELETE SET NULL
);

-- 7. 产品技术规格表
CREATE TABLE product_specifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL COMMENT '产品ID',
    spec_name VARCHAR(100) NOT NULL COMMENT '规格名称',
    spec_value VARCHAR(200) NOT NULL COMMENT '规格值',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- 8. 产品详情内容表
CREATE TABLE product_details (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL COMMENT '产品ID',
    section_type VARCHAR(50) NOT NULL COMMENT '章节类型（核心特点、应用场景、技术优势）',
    title VARCHAR(200) NOT NULL COMMENT '章节标题',
    content TEXT COMMENT '章节内容',
    icon VARCHAR(100) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- 9. 新闻管理表
CREATE TABLE news (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(300) NOT NULL COMMENT '新闻标题',
    summary TEXT COMMENT '新闻摘要',
    content LONGTEXT NOT NULL COMMENT '新闻内容',
    image_url VARCHAR(500) COMMENT '新闻图片URL',
    category VARCHAR(50) NOT NULL COMMENT '新闻分类',
    author VARCHAR(100) DEFAULT '优卡特' COMMENT '作者',
    is_hot BOOLEAN DEFAULT FALSE COMMENT '是否热门新闻',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否首页展示',
    view_count INT DEFAULT 0 COMMENT '浏览次数',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    publish_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
    is_published BOOLEAN DEFAULT TRUE COMMENT '是否发布',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 10. 关于我们内容管理表
CREATE TABLE about_content (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    section_type VARCHAR(50) NOT NULL COMMENT '章节类型（company_profile, development_history, team, honors, careers）',
    title VARCHAR(200) COMMENT '标题',
    content LONGTEXT COMMENT '内容',
    image_url VARCHAR(500) COMMENT '图片URL',
    extra_data JSON COMMENT '额外数据（如团队成员信息、荣誉详情等）',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 11. 技术支持文档管理表
CREATE TABLE support_documents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT COMMENT '关联产品ID',
    title VARCHAR(200) NOT NULL COMMENT '文档标题',
    description TEXT COMMENT '文档描述',
    file_name VARCHAR(200) NOT NULL COMMENT '文件名',
    file_url VARCHAR(500) NOT NULL COMMENT '文件下载URL',
    file_size BIGINT COMMENT '文件大小（字节）',
    file_type VARCHAR(50) COMMENT '文件类型',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL
);

-- 12. 管理员用户表
CREATE TABLE admin_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
    email VARCHAR(100) COMMENT '邮箱',
    real_name VARCHAR(100) COMMENT '真实姓名',
    role VARCHAR(50) DEFAULT 'admin' COMMENT '角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    last_login_at TIMESTAMP COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 13. 系统配置表
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(200) COMMENT '配置描述',
    config_type VARCHAR(50) DEFAULT 'string' COMMENT '配置类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_carousel_sort ON carousel_slides(sort_order, is_active);
CREATE INDEX idx_solutions_sort ON solutions(sort_order, is_active);
CREATE INDEX idx_core_products_sort ON core_products(sort_order, is_active);
CREATE INDEX idx_product_categories_sort ON product_categories(sort_order, is_active);
CREATE INDEX idx_product_subcategories_category ON product_subcategories(category_id, sort_order);
CREATE INDEX idx_products_category ON products(category_id, subcategory_id, is_active);
CREATE INDEX idx_product_specs_product ON product_specifications(product_id, sort_order);
CREATE INDEX idx_product_details_product ON product_details(product_id, section_type, sort_order);
CREATE INDEX idx_news_category_date ON news(category, publish_date DESC);
CREATE INDEX idx_news_featured ON news(is_featured, is_published, publish_date DESC);
CREATE INDEX idx_news_hot ON news(is_hot, is_published);
CREATE INDEX idx_about_content_section ON about_content(section_type, sort_order);
CREATE INDEX idx_support_docs_product ON support_documents(product_id, is_active);

-- 插入初始数据

-- 1. 插入默认轮播图数据
INSERT INTO carousel_slides (title, subtitle, image_url, link_url, sort_order) VALUES
('智能一卡通解决方案', '为您的企业、学校提供全方位智能管理服务', 'https://placehold.co/1920x1080/0066CC/FFFFFF?text=智能一卡通解决方案', '/solutions', 1),
('人脸识别消费终端', '科技创新，让消费更便捷、更安全', 'https://placehold.co/1920x1080/0088FF/FFFFFF?text=人脸识别消费终端', '/products/android-face', 2),
('智慧校园管理平台', '构建数字化、智能化的现代校园生态', 'https://placehold.co/1920x1080/00AA88/FFFFFF?text=智慧校园管理平台', '/solutions/smart-campus', 3);

-- 2. 插入解决方案数据
INSERT INTO solutions (title, description, image_url, badge, features, route_path, sort_order) VALUES
('智慧校园解决方案', '为学校提供学生考勤、图书借阅、食堂消费、宿舍管理等全方位校园智能化服务。打造安全便捷的校园环境。', 'https://placehold.co/400x300/0088FF/FFFFFF?text=智慧校园解决方案', '推荐', '["学生管理", "图书系统", "校园安全"]', '/solutions/smart-campus', 1),
('企业园区解决方案', '为企业园区提供员工考勤、门禁管理、食堂消费等一体化智能管理服务。提升企业管理效率，降低运营成本。', 'https://placehold.co/400x300/0066CC/FFFFFF?text=企业园区解决方案', '热门', '["员工管理", "门禁控制", "消费管理"]', '/solutions/enterprise-park', 2),
('医疗机构解决方案', '为医院、诊所提供患者管理、医护考勤、药品管理等专业医疗信息化解决方案。提升医疗服务质量和效率。', 'https://placehold.co/400x300/00CC66/FFFFFF?text=医疗机构解决方案', '专业', '["患者管理", "医护系统", "药品追踪"]', '/solutions/medical-institution', 3);

-- 3. 插入核心产品数据
INSERT INTO core_products (title, description, image_url, badge, features, route_path, sort_order) VALUES
('人脸识别消费终端', '采用先进的人脸识别技术，支持离线识别，为用户提供安全便捷的消费体验。广泛应用于学校食堂、企业餐厅等场景。', 'https://placehold.co/400x300/FF6B35/FFFFFF?text=人脸识别消费终端', '热销', '["人脸识别", "离线支付", "安全便捷"]', '/products/android-face', 1),
('智能门禁系统', '集成多种识别技术，支持人脸、指纹、刷卡等多种验证方式。为企业、学校提供安全可靠的出入管理解决方案。', 'https://placehold.co/400x300/4ECDC4/FFFFFF?text=智能门禁系统', '推荐', '["多重验证", "安全管理", "实时监控"]', '/products/access-control', 2),
('智能水控系统', '采用先进的物联网技术，实现用水的智能化管理。支持预付费、实时监控、数据统计等功能，有效节约水资源。', 'https://placehold.co/400x300/45B7D1/FFFFFF?text=智能水控系统', '节能', '["智能计费", "实时监控", "节水环保"]', '/products/water-electric', 3);

-- 4. 插入产品分类数据
INSERT INTO product_categories (name, code, description, image_url, route_path, sort_order) VALUES
('智能消费机系列', 'smart-consumer', '人脸识别、二维码支付等智能消费终端设备', 'https://placehold.co/300x200/FF6B35/FFFFFF?text=智能消费机', '/products/smart-consumer', 1),
('AI智能算台系列', 'ai-platform', '基于人工智能的智能识别和计算平台', 'https://placehold.co/300x200/4ECDC4/FFFFFF?text=AI智能算台', '/products/ai-platform', 2),
('掌静脉系列', 'palm-vein', '掌静脉识别技术产品系列', 'https://placehold.co/300x200/45B7D1/FFFFFF?text=掌静脉系列', '/products/palm-vein', 3),
('智能水控机系列', 'smart-water', '智能水资源管理控制设备', 'https://placehold.co/300x200/96CEB4/FFFFFF?text=智能水控机', '/products/smart-water', 4),
('智能电控机系列', 'smart-electric', '智能电力管理控制设备', 'https://placehold.co/300x200/FFEAA7/FFFFFF?text=智能电控机', '/products/smart-electric', 5),
('智能电表系列', 'smart-meter', '智能电表和计量设备', 'https://placehold.co/300x200/DDA0DD/FFFFFF?text=智能电表', '/products/smart-meter', 6);

-- 5. 插入管理员用户（默认密码：admin123，实际使用时需要加密）
INSERT INTO admin_users (username, password, email, real_name, role) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLyU1uKKNjm6', '<EMAIL>', '系统管理员', 'super_admin'),
('editor', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLyU1uKKNjm6', '<EMAIL>', '内容编辑', 'editor');
