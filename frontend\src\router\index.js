import { createRouter, createWebHistory } from 'vue-router'

// 页面组件
import Home from '../views/Home.vue'
import About from '../views/AboutMain.vue'
import Products from '../views/Products.vue'

import Solutions from '../views/Solutions.vue'
import News from '../views/News.vue'
import NewsDetail from '../views/NewsDetail.vue'
import Contact from '../views/Contact.vue'

// 关于我们子页面
import DevelopmentHistory from '../views/about/DevelopmentHistory.vue'
import Team from '../views/about/TeamPage.vue'
import Honors from '../views/about/HonorsPage.vue'
import Careers from '../views/about/CareersPage.vue'

// 产品子页面
import AndroidFace from '../views/products/AndroidFace/AndroidFace.vue'
import ArmTerminal from '../views/products/ArmTerminal/ArmTerminal.vue'
import FaceTempGate from '../views/products/FaceTempGate/FaceTempGate.vue'
import WaterElectric from '../views/products/WaterElectric/WaterElectric.vue'
import AccessControl from '../views/products/AccessControl/AccessControl.vue'
import CampusShared from '../views/products/CampusShared/CampusShared.vue'
import FaceCloud from '../views/products/LianAiYun/FaceCloud.vue'

// 新产品系列页面
import SmartConsumer from '../views/products/SmartConsumer/SmartConsumer.vue'
import AiPlatform from '../views/products/AiPlatform/AiPlatform.vue'
import PalmVein from '../views/products/PalmVein/PalmVein.vue'
import SmartWater from '../views/products/SmartWater/SmartWater.vue'
import SmartElectric from '../views/products/SmartElectric/SmartElectric.vue'
import SmartMeter from '../views/products/SmartMeter/SmartMeter.vue'
import SharedWasher from '../views/products/SharedWasher/SharedWasher.vue'
import SharedDryer from '../views/products/SharedDryer/SharedDryer.vue'
import AccessAttendance from '../views/products/AccessAttendance/AccessAttendance.vue'
import SharedVideoPhone from '../views/products/SharedVideoPhone/SharedVideoPhone.vue'
import VisitorMachine from '../views/products/VisitorMachine/VisitorMachine.vue'
import SmartSelfService from '../views/products/SmartSelfService/SmartSelfService.vue'
import SmartWaterControl from '../views/products/SmartWaterControl/SmartWaterControl.vue'

// 智能消费机系列子产品页面
import HomeFace from '../views/products/SmartConsumer/HomeFace.vue'
import RealQr from '../views/products/SmartConsumer/RealQr.vue'
import ArTerminal from '../views/products/SmartConsumer/ArTerminal.vue'

// AI智能算台系列子产品页面
import ProductRecognition from '../views/products/AiPlatform/ProductRecognition.vue'
import SmartWeighing from '../views/products/AiPlatform/SmartWeighing.vue'

// 智能水控机系列子产品页面
import SmartWaterProduct from '../views/products/SmartWaterControl/SmartWater.vue'
import SplitWaterProduct from '../views/products/SmartWaterControl/SplitWater.vue'

// 智能电控机系列子产品页面
import TimingElectric from '../views/products/SmartElectric/TimingElectric.vue'
import TimingControl from '../views/products/SmartElectric/TimingControl.vue'

// 产品详情页面
import AndroidFaceDetail from '../views/products/AndroidFace/AndroidFaceDetail.vue'
import ArmTerminalDetail from '../views/products/ArmTerminal/ArmTerminalDetail.vue'
import AccessControlDetail from '../views/products/AccessControl/AccessControlDetail.vue'
import FaceTempGateDetail from '../views/products/FaceTempGate/FaceTempGateDetail.vue'
import AndroidFaceP301 from '../views/products/AndroidFace/AndroidFaceP301.vue'
import ArmTerminalA201 from '../views/products/ArmTerminal/ArmTerminalA201.vue'
import FaceCloudBasic from '../views/products/LianAiYun/FaceCloudBasic.vue'
import FaceTempGateTG100 from '../views/products/FaceTempGate/FaceTempGateTG100.vue'
import CampusSharedWM100 from '../views/products/CampusShared/CampusSharedWM100.vue'
import WaterElectricWC100 from '../views/products/WaterElectric/WaterElectricWC100.vue'

// 新闻子页面
import CompanyNews from '../views/news/CompanyNews.vue'
import IndustryNews from '../views/news/IndustryNews.vue'
import TechnicalSupport from '../views/news/TechnicalSupport.vue'
import DownloadDocuments from '../views/news/DownloadDocuments.vue'

// 新闻分类子页面
import ProductRelease from '../views/news/ProductRelease/ProductRelease.vue'
import Exhibition from '../views/news/Exhibition/Exhibition.vue'
import CorporateCulture from '../views/news/CorporateCulture/CorporateCulture.vue'
import TechnicalInnovation from '../views/news/TechnicalInnovation/TechnicalInnovation.vue'
import CompanyNewsCategory from '../views/news/CompanyNewsCategory/CompanyNewsCategory.vue'

// 行业资讯子页面
import MarketAnalysis from '../views/news/industry/MarketAnalysis/MarketAnalysis.vue'
import PolicyInterpretation from '../views/news/industry/PolicyInterpretation/PolicyInterpretation.vue'
import IndustryTechnicalInnovation from '../views/news/industry/TechnicalInnovation/TechnicalInnovation.vue'
import InternationalCooperation from '../views/news/industry/InternationalCooperation/InternationalCooperation.vue'
import IndustryReport from '../views/news/industry/IndustryReport/IndustryReport.vue'

// 解决方案子页面
import SmartCampus from '../views/solutions/SmartCampus.vue'
import EnterprisePark from '../views/solutions/EnterprisePark.vue'
import MedicalInstitution from '../views/solutions/MedicalInstitution.vue'
import GovernmentAgency from '../views/solutions/GovernmentAgency.vue'
import FactoryPark from '../views/solutions/FactoryPark.vue'
import SmartHotel from '../views/solutions/SmartHotel.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '深圳市优卡特实业有限公司 - 智能一卡通解决方案专家',
      description: '深圳市优卡特实业有限公司专注智能一卡通系统20年，提供人脸识别、掌静脉、智能消费机等产品及解决方案'
    }
  },
  {
    path: '/products/palm-vein',
    name: 'PalmVein',
    component: PalmVein,
    meta: {
      title: '掌静脉系列 - 深圳市优卡特实业有限公司',
      description: '掌静脉识别技术，安全便捷新体验'
    }
  },
  {
    path: '/products/smart-water',
    name: 'SmartWater',
    component: SmartWater,
    meta: {
      title: '智能水控机系列 - 深圳市优卡特实业有限公司',
      description: '智能水资源管理，节约用水新标准'
    }
  },
  {
    path: '/products/smart-electric',
    name: 'SmartElectric',
    component: SmartElectric,
    meta: {
      title: '智能电控机系列 - 深圳市优卡特实业有限公司',
      description: '智能电力管理，节能环保新选择'
    }
  },
  {
    path: '/products/smart-meter',
    name: 'SmartMeter',
    component: SmartMeter,
    meta: {
      title: '智能水表/电表系列 - 深圳市优卡特实业有限公司',
      description: '智能计量管理，精准数据新标准'
    }
  },
  {
    path: '/products/shared-washer',
    name: 'SharedWasher',
    component: SharedWasher,
    meta: {
      title: '共享洗衣机系列 - 深圳市优卡特实业有限公司',
      description: '智能共享洗衣，便民服务新体验'
    }
  },
  {
    path: '/products/shared-dryer',
    name: 'SharedDryer',
    component: SharedDryer,
    meta: {
      title: '共享吹风机系列 - 深圳市优卡特实业有限公司',
      description: '智能共享吹风，便民服务新选择'
    }
  },
  {
    path: '/products/access-attendance',
    name: 'AccessAttendance',
    component: AccessAttendance,
    meta: {
      title: '门禁/考勤系列 - 深圳市优卡特实业有限公司',
      description: '智能门禁考勤，安全管理新标准'
    }
  },
  {
    path: '/products/shared-video-phone',
    name: 'SharedVideoPhone',
    component: SharedVideoPhone,
    meta: {
      title: '共享视频电话机系列 - 深圳市优卡特实业有限公司',
      description: '智能视频通话，便民通信新体验'
    }
  },
  {
    path: '/products/visitor-machine',
    name: 'VisitorMachine',
    component: VisitorMachine,
    meta: {
      title: '访客机系列 - 深圳市优卡特实业有限公司',
      description: '智能访客管理，安全接待新标准'
    }
  },
  {
    path: '/products/smart-self-service',
    name: 'SmartSelfService',
    component: SmartSelfService,
    meta: {
      title: '智能自助机系列 - 深圳市优卡特实业有限公司',
      description: '智能自助服务，便民高效新体验'
    }
  },
  {
    path: '/products/water-control',
    name: 'SmartWaterControl',
    component: SmartWaterControl,
    meta: {
      title: '智能水控机系列 - 深圳市优卡特实业有限公司',
      description: '智能水资源管理，节约用水新标准'
    }
  },
  {
    path: '/products/electric-control',
    name: 'SmartElectricControl',
    component: SmartElectric,
    meta: {
      title: '智能电控机系列 - 深圳市优卡特实业有限公司',
      description: '智能电力管理，节能高效新标准'
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: '关于我们 - 深圳市优卡特实业有限公司',
      description: '了解优卡特的发展历程与企业文化'
    }
  },
  {
    path: '/about/history',
    name: 'DevelopmentHistory',
    component: DevelopmentHistory,
    meta: {
      title: '发展历程 - 深圳市优卡特实业有限公司',
      description: '回顾优卡特20年来的发展足迹与重要里程碑'
    }
  },
  {
    path: '/about/team',
    name: 'Team',
    component: Team,
    meta: {
      title: '企业团队 - 深圳市优卡特实业有限公司',
      description: '认识优卡特的核心管理团队与技术专家'
    }
  },
  {
    path: '/about/honors',
    name: 'Honors',
    component: Honors,
    meta: {
      title: '企业荣誉 - 深圳市优卡特实业有限公司',
      description: '展示优卡特获得的各项认证、奖项与资质'
    }
  },
  {
    path: '/about/careers',
    name: 'Careers',
    component: Careers,
    meta: {
      title: '招贤纳士 - 深圳市优卡特实业有限公司',
      description: '加入优卡特，共同创造智能一卡通的美好未来'
    }
  },
  {
    path: '/products',
    name: 'Products',
    component: Products,
    meta: {
      title: '产品中心 - 深圳市优卡特实业有限公司',
      description: '智能一卡通设备，引领科技创新'
    }
  },

  {
    path: '/products/android-face',
    name: 'AndroidFace',
    component: AndroidFace,
    meta: {
      title: '安卓人脸消费机 - 深圳市优卡特实业有限公司',
      description: '智能人脸识别，安全便捷消费'
    }
  },
  {
    path: '/products/arm-terminal',
    name: 'ArmTerminal',
    component: ArmTerminal,
    meta: {
      title: 'ARM消费机 - 深圳市优卡特实业有限公司',
      description: '高性能ARM处理器，稳定可靠'
    }
  },
  {
    path: '/products/face-temp-gate',
    name: 'FaceTempGate',
    component: FaceTempGate,
    meta: {
      title: '人脸测温通道闸机 - 深圳市优卡特实业有限公司',
      description: '人脸识别+体温检测，双重安全保障'
    }
  },
  {
    path: '/products/water-electric',
    name: 'WaterElectric',
    component: WaterElectric,
    meta: {
      title: '水/电控机 - 深圳市优卡特实业有限公司',
      description: '智能水电管理，节能环保'
    }
  },
  {
    path: '/products/access-control',
    name: 'AccessControl',
    component: AccessControl,
    meta: {
      title: '门禁机 - 深圳市优卡特实业有限公司',
      description: '智能门禁管理，安全可靠'
    }
  },
  {
    path: '/products/campus-shared',
    name: 'CampusShared',
    component: CampusShared,
    meta: {
      title: '校园共享设备 - 深圳市优卡特实业有限公司',
      description: '智慧校园，共享便民'
    }
  },
  {
    path: '/products/face-cloud',
    name: 'FaceCloud',
    component: FaceCloud,
    meta: {
      title: '脸爱云平台 - 深圳市优卡特实业有限公司',
      description: '云端智能管理，数据安全可靠'
    }
  },

  // 新产品系列路由
  {
    path: '/products/smart-consumer',
    name: 'SmartConsumer',
    component: SmartConsumer,
    meta: {
      title: '智能消费机系列 - 深圳市优卡特实业有限公司',
      description: '先进智能识别技术，便捷消费新体验'
    }
  },
  // 智能消费机系列子产品路由
  {
    path: '/products/smart-consumer/home-face',
    name: 'HomeFace',
    component: HomeFace,
    meta: {
      title: '安卓人脸消费终端 - 深圳市优卡特实业有限公司',
      description: '智能人脸识别消费系统'
    }
  },
  {
    path: '/products/smart-consumer/real-qr',
    name: 'RealQr',
    component: RealQr,
    meta: {
      title: '安卓二维码消费终端 - 深圳市优卡特实业有限公司',
      description: '高效二维码扫描支付'
    }
  },
  {
    path: '/products/smart-consumer/ar-terminal',
    name: 'ArTerminal',
    component: ArTerminal,
    meta: {
      title: 'AR消费终端 - 深圳市优卡特实业有限公司',
      description: '增强现实消费体验'
    }
  },

// AI智能算台系列子产品路由
{
  path: '/products/ai-platform/product-recognition',
  name: 'ProductRecognition',
  component: ProductRecognition,
  meta: {
    title: 'AI商品识别算台 - 深圳市优卡特实业有限公司',
    description: '智能商品识别与计算系统'
  }
},
{
  path: '/products/ai-platform/smart-weighing',
  name: 'SmartWeighing',
  component: SmartWeighing,
  meta: {
    title: '智能称重算台 - 深圳市优卡特实业有限公司',
    description: '精准称重与智能计算'
  }
},

// 智能水控机系列子产品路由
{
  path: '/products/water-control/smart-water',
  name: 'SmartWaterProduct',
  component: () => import('../views/products/SmartWaterControl/SmartWater.vue'),
  meta: {
    title: '智能一体水控机 - 深圳市优卡特实业有限公司',
    description: '集成式智能水控终端'
  }
},
{
  path: '/products/water-control/split-water',
  name: 'SplitWaterProduct',
  component: () => import('../views/products/SmartWaterControl/SplitWater.vue'),
  meta: {
    title: '智能分体水控机 - 深圳市优卡特实业有限公司',
    description: '分体式智能水控终端'
  }
},

// 智能电控机系列子产品路由
{
  path: '/products/electric-control/timing-electric',
  name: 'TimingElectricProduct',
  component: () => import('../views/products/SmartElectric/TimingElectric.vue'),
  meta: {
    title: '计时计量电控机 - 深圳市优卡特实业有限公司',
    description: '精准计时计量电力控制'
  }
},
{
  path: '/products/electric-control/timing-control',
  name: 'TimingControlProduct',
  component: () => import('../views/products/SmartElectric/TimingControl.vue'),
  meta: {
    title: '计时电控机 - 深圳市优卡特实业有限公司',
    description: '智能计时电力控制'
  }
},

// 智能水表/电表系列子产品路由
{
  path: '/products/smart-meter/water-meter',
  name: 'WaterMeterProduct',
  component: () => import('../views/products/SmartMeter/WaterMeter.vue'),
  meta: {
    title: '智能水表 - 深圳市优卡特实业有限公司',
    description: '精准水量计量监控'
  }
},
{
  path: '/products/smart-meter/electric-meter',
  name: 'ElectricMeterProduct',
  component: () => import('../views/products/SmartMeter/ElectricMeter.vue'),
  meta: {
    title: '智能电表 - 深圳市优卡特实业有限公司',
    description: '精准电量计量监控'
  }
},

// 智能自助机系列子产品路由
{
  path: '/products/smart-self-service/android-self',
  name: 'AndroidSelfProduct',
  component: () => import('../views/products/SmartSelfService/AndroidSelf.vue'),
  meta: {
    title: '安卓自助机 - 深圳市优卡特实业有限公司',
    description: '基于安卓系统的智能自助服务终端'
  }
},
{
  path: '/products/smart-self-service/windows-self',
  name: 'WindowsSelfProduct',
  component: () => import('../views/products/SmartSelfService/WindowsSelf.vue'),
  meta: {
    title: 'Windows自助机 - 深圳市优卡特实业有限公司',
    description: '基于Windows系统的智能自助服务终端'
  }
},

// 掌静脉系列子产品路由
{
  path: '/products/palm-vein/palm-consumer',
  name: 'PalmConsumerProduct',
  component: () => import('../views/products/PalmVein/PalmConsumer.vue'),
  meta: {
    title: '掌静脉消费机 - 深圳市优卡特实业有限公司',
    description: '掌静脉识别消费终端'
  }
},
{
  path: '/products/palm-vein/palm-water',
  name: 'PalmWaterProduct',
  component: () => import('../views/products/PalmVein/PalmWater.vue'),
  meta: {
    title: '掌静脉水控机 - 深圳市优卡特实业有限公司',
    description: '掌静脉识别水控系统'
  }
},
{
  path: '/products/palm-vein/palm-electric',
  name: 'PalmElectricProduct',
  component: () => import('../views/products/PalmVein/PalmElectric.vue'),
  meta: {
    title: '掌静脉电控机 - 深圳市优卡特实业有限公司',
    description: '掌静脉识别电控系统'
  }
},
{
  path: '/products/palm-vein/palm-washer',
  name: 'PalmWasherProduct',
  component: () => import('../views/products/PalmVein/PalmWasher.vue'),
  meta: {
    title: '掌静脉共享洗衣机 - 深圳市优卡特实业有限公司',
    description: '掌静脉识别共享洗衣'
  }
},
{
  path: '/products/palm-vein/palm-dryer',
  name: 'PalmDryerProduct',
  component: () => import('../views/products/PalmVein/PalmDryer.vue'),
  meta: {
    title: '掌静脉共享吹风机 - 深圳市优卡特实业有限公司',
    description: '掌静脉识别共享吹风'
  }
},
{
  path: '/products/palm-vein/palm-access',
  name: 'PalmAccessProduct',
  component: () => import('../views/products/PalmVein/PalmAccess.vue'),
  meta: {
    title: '掌静脉门禁/考勤机 - 深圳市优卡特实业有限公司',
    description: '掌静脉识别门禁考勤'
  }
},
  
  // 智能水控机系列子产品路由
  {
    path: '/products/water-control/smart-water',
    name: 'SmartWaterDetail',
    component: SmartWaterProduct,
    meta: {
      title: '智能一体水控机 - 深圳市优卡特实业有限公司',
      description: '集成式智能水控终端'
    }
  },
  {
    path: '/products/water-control/split-water',
    name: 'SplitWaterDetail',
    component: SplitWaterProduct,
    meta: {
      title: '智能分体水控机 - 深圳市优卡特实业有限公司',
      description: '分体式智能水控终端'
    }
  },
  
  // 智能电控机系列子产品路由
  {
    path: '/products/electric-control/timing-electric',
    name: 'TimingElectric',
    component: TimingElectric,
    meta: {
      title: '计时计量电控机 - 深圳市优卡特实业有限公司',
      description: '精准计时计量电力控制'
    }
  },
  {
    path: '/products/electric-control/timing-control',
    name: 'TimingControl',
    component: TimingControl,
    meta: {
      title: '计时电控机 - 深圳市优卡特实业有限公司',
      description: '智能计时电力控制'
    }
  },
  {
    path: '/products/ai-platform',
    name: 'AiPlatform',
    component: AiPlatform,
    meta: {
      title: 'AI智能算台系列 - 深圳市优卡特实业有限公司',
      description: '人工智能识别技术，精准计算新标准'
    }
  },
  {
    path: '/products/palm-vein',
    name: 'PalmVein',
    component: PalmVein,
    meta: {
      title: '掌静脉系列 - 深圳市优卡特实业有限公司',
      description: '生物识别技术，安全便捷新体验'
    }
  },

  // 产品详情页面路由
  {
    path: '/products/android-face-detail',
    name: 'AndroidFaceDetail',
    component: AndroidFaceDetail,
    meta: {
      title: '安卓人脸消费机详情 - 深圳市优卡特实业有限公司',
      description: '安卓人脸消费机产品详情，智能识别，安全便捷'
    }
  },
  {
    path: '/products/arm-terminal-detail',
    name: 'ArmTerminalDetail',
    component: ArmTerminalDetail,
    meta: {
      title: 'ARM消费机详情 - 深圳市优卡特实业有限公司',
      description: 'ARM消费机产品详情，高性能处理器，稳定可靠'
    }
  },
  {
    path: '/products/access-control-detail',
    name: 'AccessControlDetail',
    component: AccessControlDetail,
    meta: {
      title: '门禁控制器详情 - 深圳市优卡特实业有限公司',
      description: '门禁控制器产品详情，智能门禁管理，安全可靠'
    }
  },
  {
    path: '/products/face-temp-gate-detail',
    name: 'FaceTempGateDetail',
    component: FaceTempGateDetail,
    meta: {
      title: '人脸测温通道闸机详情 - 深圳市优卡特实业有限公司',
      description: '人脸测温通道闸机产品详情，智能测温，安全防护'
    }
  },
  {
    path: '/products/android-face/p301-2d-2w',
    name: 'AndroidFaceP301',
    component: AndroidFaceP301,
    meta: {
      title: 'P301-2D-2W台式人脸消费机详情 - 深圳市优卡特实业有限公司',
      description: 'P301-2D-2W台式人脸消费机产品详情，智能识别，安全便捷'
    }
  },
  {
    path: '/products/android-face/p301',
    name: 'AndroidFaceP301Std',
    component: AndroidFaceP301,
    meta: {
      title: 'P301-2D-2W台式人脸消费机详情 - 深圳市优卡特实业有限公司',
      description: 'P301-2D-2W台式人脸消费机产品详情，智能识别，安全便捷'
    }
  },
  {
    path: '/products/arm-terminal/a201-ic-2w',
    name: 'ArmTerminalA201',
    component: ArmTerminalA201,
    meta: {
      title: 'A201-IC-2W ARM消费机详情 - 深圳市优卡特实业有限公司',
      description: 'A201-IC-2W ARM消费机产品详情，高性能处理器，稳定可靠'
    }
  },
  {
    path: '/products/face-cloud/basic',
    name: 'FaceCloudBasic',
    component: FaceCloudBasic,
    meta: {
      title: '脸爱云平台基础版详情 - 深圳市优卡特实业有限公司',
      description: '脸爱云平台基础版产品详情，基础设备管理云平台'
    }
  },
  {
    path: '/products/face-temp-gate/tg100-stand',
    name: 'FaceTempGateTG100',
    component: FaceTempGateTG100,
    meta: {
      title: 'YKT-TG100立式测温闸机详情 - 深圳市优卡特实业有限公司',
      description: 'YKT-TG100立式测温闸机产品详情，立式人脸测温通道闸机'
    }
  },
  {
    path: '/products/campus-shared/wm100-washer',
    name: 'CampusSharedWM100',
    component: CampusSharedWM100,
    meta: {
      title: 'YKT-WM100共享洗衣机详情 - 深圳市优卡特实业有限公司',
      description: 'YKT-WM100共享洗衣机产品详情，智能共享洗衣设备'
    }
  },
  {
    path: '/products/water-electric/wc100-water',
    name: 'WaterElectricWC100',
    component: WaterElectricWC100,
    meta: {
      title: 'YKT-WC100智能水控机详情 - 深圳市优卡特实业有限公司',
      description: 'YKT-WC100智能水控机产品详情，IC卡智能水控终端'
    }
  },

  // 新产品子型号路由 - 动态路由
  {
    path: '/products/:category/:productId',
    name: 'ProductDetail',
    component: () => import('../views/ProductDetail.vue'),
    meta: {
      title: '产品详情 - 深圳市优卡特实业有限公司',
      description: '查看产品详细信息'
    }
  },

  {
    path: '/solutions',
    name: 'Solutions',
    component: Solutions,
    meta: {
      title: '解决方案 - 深圳市优卡特实业有限公司',
      description: '为不同行业提供专业的一卡通解决方案'
    }
  },

  // 解决方案子页面路由
  {
    path: '/solutions/smart-campus',
    name: 'SmartCampus',
    component: SmartCampus,
    meta: {
      title: '智慧校园解决方案 - 深圳市优卡特实业有限公司',
      description: '构建数字化、智能化的现代校园生态，提供全方位的智慧校园解决方案'
    }
  },
  {
    path: '/solutions/enterprise-park',
    name: 'EnterprisePark',
    component: EnterprisePark,
    meta: {
      title: '企业园区解决方案 - 深圳市优卡特实业有限公司',
      description: '打造智能化、数字化的现代企业园区管理平台'
    }
  },
  {
    path: '/solutions/medical-institution',
    name: 'MedicalInstitution',
    component: MedicalInstitution,
    meta: {
      title: '医疗机构解决方案 - 深圳市优卡特实业有限公司',
      description: '构建安全、高效的智慧医疗环境管理系统'
    }
  },
  {
    path: '/solutions/government-agency',
    name: 'GovernmentAgency',
    component: GovernmentAgency,
    meta: {
      title: '政府机关解决方案 - 深圳市优卡特实业有限公司',
      description: '打造安全、高效的数字政务环境管理平台'
    }
  },
  {
    path: '/solutions/factory-park',
    name: 'FactoryPark',
    component: FactoryPark,
    meta: {
      title: '工厂园区解决方案 - 深圳市优卡特实业有限公司',
      description: '构建安全、高效的智能制造环境管理系统'
    }
  },
  {
    path: '/solutions/smart-hotel',
    name: 'SmartHotel',
    component: SmartHotel,
    meta: {
      title: '智慧酒店解决方案 - 深圳市优卡特实业有限公司',
      description: '打造智能化、个性化的酒店服务体验管理平台'
    }
  },
  {
    path: '/news',
    name: 'News',
    component: News,
    meta: {
      title: '新闻中心 - 深圳市优卡特实业有限公司',
      description: '了解优卡特最新动态与行业资讯'
    }
  },
  {
    path: '/news/company',
    name: 'CompanyNews',
    component: CompanyNews,
    meta: {
      title: '企业新闻 - 深圳市优卡特实业有限公司',
      description: '了解优卡特最新企业动态'
    }
  },
  {
    path: '/news/industry',
    name: 'IndustryNews',
    component: IndustryNews,
    meta: {
      title: '行业资讯 - 深圳市优卡特实业有限公司',
      description: '关注行业动态，把握发展趋势'
    }
  },
  {
    path: '/news/support',
    name: 'TechnicalSupport',
    component: TechnicalSupport,
    meta: {
      title: '技术支持 - 深圳市优卡特实业有限公司',
      description: '专业技术支持，解决您的疑问'
    }
  },
  {
    path: '/technical-support',
    name: 'TechnicalSupportMain',
    component: TechnicalSupport,
    meta: {
      title: '技术支持 - 深圳市优卡特实业有限公司',
      description: '专业技术支持，解决您的疑问'
    }
  },
  {
    path: '/download-documents',
    name: 'DownloadDocuments',
    component: DownloadDocuments,
    meta: {
      title: '下载文档 - 深圳市优卡特实业有限公司',
      description: '下载产品使用说明文档'
    }
  },
  {
    path: '/news/:id',
    name: 'NewsDetail',
    component: NewsDetail,
    meta: {
      title: '新闻详情 - 深圳市优卡特实业有限公司',
      description: '查看新闻详细内容'
    }
  },

  // 新闻分类子页面路由
  {
    path: '/news/product-release',
    name: 'ProductRelease',
    component: ProductRelease,
    meta: {
      title: '产品发布 - 深圳市优卡特实业有限公司',
      description: '了解优卡特最新产品发布动态'
    }
  },
  {
    path: '/news/exhibition',
    name: 'Exhibition',
    component: Exhibition,
    meta: {
      title: '展会活动 - 深圳市优卡特实业有限公司',
      description: '了解优卡特参与的各类展会活动'
    }
  },
  {
    path: '/news/corporate-culture',
    name: 'CorporateCulture',
    component: CorporateCulture,
    meta: {
      title: '企业文化 - 深圳市优卡特实业有限公司',
      description: '了解优卡特的企业文化和价值观'
    }
  },
  {
    path: '/news/technical-innovation',
    name: 'TechnicalInnovation',
    component: TechnicalInnovation,
    meta: {
      title: '技术创新 - 深圳市优卡特实业有限公司',
      description: '了解优卡特的技术创新成果'
    }
  },
  {
    path: '/news/company-news-category',
    name: 'CompanyNewsCategory',
    component: CompanyNewsCategory,
    meta: {
      title: '公司新闻 - 深圳市优卡特实业有限公司',
      description: '了解优卡特最新公司动态'
    }
  },

  // 行业资讯子页面路由
  {
    path: '/news/industry/market-analysis',
    name: 'MarketAnalysis',
    component: MarketAnalysis,
    meta: {
      title: '市场分析 - 深圳市优卡特实业有限公司',
      description: '深度解析行业市场趋势与发展机遇'
    }
  },
  {
    path: '/news/industry/policy-interpretation',
    name: 'PolicyInterpretation',
    component: PolicyInterpretation,
    meta: {
      title: '政策解读 - 深圳市优卡特实业有限公司',
      description: '深入解读行业政策法规与发展导向'
    }
  },
  {
    path: '/news/industry/technical-innovation',
    name: 'IndustryTechnicalInnovation',
    component: IndustryTechnicalInnovation,
    meta: {
      title: '技术创新 - 深圳市优卡特实业有限公司',
      description: '探索行业前沿技术与创新应用'
    }
  },
  {
    path: '/news/industry/international-cooperation',
    name: 'InternationalCooperation',
    component: InternationalCooperation,
    meta: {
      title: '国际合作 - 深圳市优卡特实业有限公司',
      description: '关注全球合作动态与国际交流'
    }
  },
  {
    path: '/news/industry/industry-report',
    name: 'IndustryReport',
    component: IndustryReport,
    meta: {
      title: '行业报告 - 深圳市优卡特实业有限公司',
      description: '权威发布行业深度研究报告'
    }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: Contact,
    meta: {
      title: '联系我们 - 深圳市优卡特实业有限公司',
      description: '我们期待与您的合作，随时为您提供专业服务'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title
  }
  if (to.meta.description) {
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', to.meta.description)
    }
  }
  next()
})

export default router
