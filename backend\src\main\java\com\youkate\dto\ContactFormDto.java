package com.youkate.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;

/**
 * 联系表单数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactFormDto {
    
    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Size(min = 2, max = 50, message = "姓名长度应在2-50个字符之间")
    private String name;
    
    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 电话号码
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 公司名称
     */
    @Size(max = 100, message = "公司名称不能超过100个字符")
    private String company;
    
    /**
     * 职位
     */
    @Size(max = 50, message = "职位不能超过50个字符")
    private String position;
    
    /**
     * 主题
     */
    @NotBlank(message = "主题不能为空")
    @Size(min = 5, max = 100, message = "主题长度应在5-100个字符之间")
    private String subject;
    
    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Size(min = 10, max = 1000, message = "消息内容长度应在10-1000个字符之间")
    private String message;
    
    /**
     * 感兴趣的产品/服务
     */
    private String interestedProducts;
    
    /**
     * 预算范围
     */
    private String budgetRange;
    
    /**
     * 期望联系时间
     */
    private String preferredContactTime;
    
    /**
     * 来源渠道
     */
    private String source;
    
    /**
     * 是否同意接收营销信息
     */
    private Boolean agreeToMarketing;
}
