<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">产品中心</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能一卡通设备，引领科技创新</p>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div>
      <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
    </section>

    <!-- 产品展示 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- 产品1 - 智能消费机系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="0">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">热销</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/smart-consumer')">
              <img src="https://placehold.co/400x300" alt="智能消费机系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">智能消费机系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">先进智能识别技术，便捷消费新体验，支持人脸识别、二维码扫描等多种支付方式。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-mobile-alt mr-1"></i>智能识别</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-qrcode mr-1"></i>多种支付</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品2 - AI智能算台系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="30">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">推荐</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/ai-platform')">
              <img src="https://placehold.co/400x300" alt="AI智能算台系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">AI智能算台系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">人工智能识别技术，精准计算新标准，支持商品识别和智能称重功能。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-brain mr-1"></i>AI识别</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-weight mr-1"></i>智能称重</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品3 - 智能水控机系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="60">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-cyan-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">节能</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/water-control')">
              <img src="https://placehold.co/400x300" alt="智能水控机系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">智能水控机系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能水资源管理系统，节能环保，支持一体式和分体式两种配置。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-tint mr-1"></i>水控管理</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-leaf mr-1"></i>节能环保</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品4 - 智能电控机系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="90">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">智能</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/electric-control')">
              <img src="https://placehold.co/400x300" alt="智能电控机系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">智能电控机系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能电力控制系统，支持计时计量和计时控制，安全可靠。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-bolt mr-1"></i>电控管理</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-clock mr-1"></i>计时计量</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品5 - 智能水表/电表系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="120">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">精准</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/smart-meter')">
              <img src="https://placehold.co/400x300" alt="智能水表/电表系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">智能水表/电表系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能计量设备，精准测量水电使用量，支持远程抄表和数据分析。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-gauge-high mr-1"></i>精准计量</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-chart-line mr-1"></i>数据分析</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品6 - 共享洗衣机系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="150">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">共享</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/shared-washer')">
              <img src="https://placehold.co/400x300" alt="共享洗衣机系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">共享洗衣机系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能共享洗衣设备，支持多种支付方式，便民服务新体验。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-tshirt mr-1"></i>智能洗衣</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-share-alt mr-1"></i>共享服务</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品7 - 共享吹风机系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="180">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">便民</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/shared-dryer')">
              <img src="https://placehold.co/400x300" alt="共享吹风机系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">共享吹风机系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能共享吹风设备，便民服务，支持扫码付费使用。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-wind mr-1"></i>智能吹风</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-qrcode mr-1"></i>扫码付费</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品8 - 门禁/考勤系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="210">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">安全</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/access-attendance')">
              <img src="https://placehold.co/400x300" alt="门禁/考勤系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">门禁/考勤系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能门禁考勤系统，多种识别方式，保障出入安全和考勤管理。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-door-open mr-1"></i>门禁控制</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-user-clock mr-1"></i>考勤管理</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品9 - 共享视频电话机系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="240">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">通讯</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/shared-video-phone')">
              <img src="https://placehold.co/400x300" alt="共享视频电话机系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">共享视频电话机系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能视频通话设备，支持远程视频通话，便民通讯服务。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-video mr-1"></i>视频通话</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-phone mr-1"></i>远程通讯</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品10 - 访客机系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="270" data-aos-easing="ease-out-quart">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-cyan-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">访客</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/visitor-machine')">
              <img src="https://placehold.co/400x300" alt="访客机系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">访客机系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能访客管理系统，身份验证，访客登记，提升安全管理水平。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-user-check mr-1"></i>访客管理</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-id-card mr-1"></i>身份验证</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品11 - 智能自助机系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="270" data-aos-easing="ease-out-quart">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">自助</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/smart-self-service')">
              <img src="https://placehold.co/400x300" alt="智能自助机系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">智能自助机系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能自助服务终端，支持安卓和Windows系统，提供多样化自助服务。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-desktop mr-1"></i>自助服务</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-cogs mr-1"></i>多系统支持</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 产品12 - 掌静脉系列 -->
          <div class="product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-duration="250" data-aos-delay="270" data-aos-easing="ease-out-quart">
            <div class="product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"></div>
            <div class="product-card__badge absolute top-4 left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300">生物识别</div>
            <div class="product-card__image relative overflow-hidden cursor-pointer" @click="$router.push('/products/palm-vein')">
              <img src="https://placehold.co/400x300" alt="掌静脉系列" class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105">
              <div class="product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"></div>
            </div>
            <div class="product-card__content p-6">
              <h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">掌静脉系列</h3>
              <p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能掌静脉识别技术，高安全性生物识别，支持多种应用场景。</p>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span class="text-sm text-gray-500"><i class="fas fa-hand-paper mr-1"></i>掌静脉识别</span>
                  <span class="text-sm text-gray-500"><i class="fas fa-shield-alt mr-1"></i>高安全性</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Products',
  mounted() {
    // 进一步优化AOS动画配置，加快过渡速度
    if (typeof AOS !== 'undefined') {
      AOS.init({
        duration: 250,           // 进一步缩短动画持续时间
        once: true,             // 动画只执行一次
        offset: 30,             // 更早触发动画
        delay: 0,               // 移除全局延迟
        easing: 'ease-out-quart', // 使用更快的缓动函数
        disable: function() {
          // 在低性能设备上禁用动画
          return window.innerWidth < 768 && 
                 (navigator.hardwareConcurrency < 4 || 
                  navigator.deviceMemory < 4);
        }
      });
    }
    
    // 预加载优化
    this.optimizePerformance();
  },
  methods: {
    optimizePerformance() {
      // 启用硬件加速
      const cards = document.querySelectorAll('.product-card');
      cards.forEach(card => {
        card.style.willChange = 'transform, opacity';
        card.style.backfaceVisibility = 'hidden';
        card.style.perspective = '1000px';
      });
      
      // 延迟移除will-change属性以节省内存
      setTimeout(() => {
        cards.forEach(card => {
          card.style.willChange = 'auto';
        });
      }, 2000);
    }
  }
}
</script>

<style scoped>
/* 产品卡片样式 - 性能优化 */
.product-card {
  /* 使用硬件加速的transform替代其他属性 */
  transition: transform 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  /* 启用硬件加速 */
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  /* 优化渲染性能 */
  contain: layout style paint;
}

.product-card:hover {
  transform: translateY(-8px) translateZ(0);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.product-card__glow {
  border-radius: 0.75rem;
  /* 优化渐变渲染 */
  will-change: opacity;
}

.product-card__badge {
  z-index: 20;
  /* 优化缩放动画 */
  will-change: transform;
}

.product-card__image {
  height: 16rem;
  /* 优化图片渲染 */
  contain: layout;
}

.product-card__image img {
  /* 优化图片缩放性能 */
  transition: transform 0.3s ease;
  will-change: transform;
}

.product-card__title {
  font-size: 1.25rem;
  line-height: 1.75rem;
  /* 优化文字渲染 */
  contain: layout;
}

.product-card__description {
  font-size: 0.875rem;
  line-height: 1.25rem;
  /* 优化文字渲染 */
  contain: layout;
}

/* 动画性能优化 */
@media (prefers-reduced-motion: no-preference) {
  .product-card {
    /* 在支持动画的设备上启用完整动画 */
    transition: transform 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

@media (prefers-reduced-motion: reduce) {
  .product-card {
    /* 在低性能设备上禁用动画 */
    transition: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .product-card__title {
    font-size: 1.125rem;
  }
  
  .product-card__description {
    font-size: 0.8rem;
  }
  
  /* 移动端性能优化 */
  .product-card {
    /* 移动端使用更快的动画 */
    transition: transform 0.2s ease-out,
                box-shadow 0.2s ease-out;
  }
  
  .product-card:hover {
    /* 移动端减少悬停效果的复杂度 */
    transform: translateY(-4px) translateZ(0);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  }
}

/* 高性能设备优化 */
@media (min-width: 1024px) and (min-resolution: 2dppx) {
  .product-card {
    /* 高分辨率设备使用更精细的动画 */
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* GPU加速优化 */
.product-card,
.product-card__glow,
.product-card__badge,
.product-card__image img {
  /* 强制启用硬件加速 */
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
}
</style>
