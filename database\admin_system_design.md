# 后台管理系统数据库设计文档

## 系统概述

本数据库设计用于支持深圳市优卡特实业有限公司网站的后台管理系统，实现对网站所有内容的动态管理。

## 核心功能模块

### 1. 首页管理

#### 1.1 轮播图管理 (`carousel_slides`)
- **功能**: 管理首页轮播图的增删改查
- **字段说明**:
  - `title`: 轮播图标题
  - `subtitle`: 轮播图副标题  
  - `image_url`: 轮播图片URL
  - `link_url`: 点击跳转链接
  - `sort_order`: 显示顺序
  - `is_active`: 是否启用

#### 1.2 解决方案卡片管理 (`solutions`)
- **功能**: 管理首页解决方案卡片的内容
- **字段说明**:
  - `title`: 解决方案标题
  - `description`: 解决方案描述
  - `image_url`: 卡片图片URL
  - `banner_image_url`: 横幅图片URL（用于解决方案详情页）
  - `content_images`: 内容图片JSON数组（填充解决方案页面）
  - `badge`: 标签（热门、推荐等）
  - `features`: 特性标签JSON数组
  - `route_path`: 路由路径

#### 1.3 核心产品卡片管理 (`core_products`)
- **功能**: 管理首页核心产品展示卡片
- **字段说明**:
  - `title`: 产品标题
  - `description`: 产品描述
  - `image_url`: 产品图片URL
  - `badge`: 产品标签
  - `features`: 产品特性JSON数组
  - `route_path`: 产品路由路径

#### 1.4 新闻管理 (`news`)
- **功能**: 管理首页新闻展示和新闻中心内容
- **字段说明**:
  - `title`: 新闻标题
  - `summary`: 新闻摘要
  - `content`: 新闻详细内容
  - `image_url`: 新闻图片URL
  - `category`: 新闻分类
  - `is_hot`: 是否热门新闻（右侧热门新闻）
  - `is_featured`: 是否首页展示
  - `view_count`: 浏览次数
  - `publish_date`: 发布时间

### 2. 产品中心管理

#### 2.1 产品分类管理 (`product_categories`)
- **功能**: 管理产品父系列（如智能消费机系列）
- **管理操作**:
  - 增加新的产品系列
  - 修改系列名称、描述、图片
  - 删除系列（级联删除子系列和产品）
  - 调整显示顺序

#### 2.2 产品子系列管理 (`product_subcategories`)
- **功能**: 管理产品子系列（如安卓人脸消费终端）
- **管理操作**:
  - 在指定父系列下增加子系列
  - 修改子系列信息
  - 删除子系列
  - 调整显示顺序

#### 2.3 产品详情管理 (`products`)
- **功能**: 管理具体产品（如P301型号）
- **字段说明**:
  - `name`: 产品名称
  - `model`: 产品型号
  - `description`: 产品描述
  - `main_image_url`: 主图片URL
  - `thumbnail_images`: 缩略图JSON数组（最多5张）
  - `product_tags`: 产品标签JSON数组
- **管理操作**:
  - 按P301.vue模板创建新产品
  - 管理产品的5个缩略图（可增减至1-5个）
  - 修改产品标签、标题、简介

#### 2.4 产品技术规格管理 (`product_specifications`)
- **功能**: 管理产品技术参数表格
- **管理操作**:
  - 增加新的技术规格项
  - 修改规格名称和值
  - 删除规格项
  - 调整显示顺序

#### 2.5 产品详情内容管理 (`product_details`)
- **功能**: 管理产品详情页的各个章节
- **章节类型**:
  - `core_features`: 核心特点
  - `application_scenarios`: 应用场景
  - `technical_advantages`: 技术优势
- **管理操作**:
  - 增删改各章节内容
  - 修改章节标题、内容、图标
  - 调整显示顺序

### 3. 解决方案管理

#### 3.1 解决方案页面管理
- **功能**: 管理解决方案主页和详情页
- **管理操作**:
  - 修改解决方案卡片图片（替换纯色图片）
  - 管理横幅下方的解决方案彩图
  - 填充整个解决方案详情页面内容
  - 调整解决方案显示顺序

### 4. 新闻中心管理

#### 4.1 新闻内容管理
- **功能**: 全面管理新闻中心内容
- **管理操作**:
  - 添加新闻（以首页三个新闻卡片页面为模板）
  - 修改新闻标题、内容、图片
  - 删除新闻
  - 设置首页展示新闻
  - 管理右侧热门新闻
  - 调整新闻显示顺序
  - 新闻分类管理

### 5. 关于我们管理

#### 5.1 关于我们内容管理 (`about_content`)
- **功能**: 管理关于我们各个子页面内容
- **章节类型**:
  - `company_profile`: 公司简介
  - `development_history`: 发展历程
  - `team`: 企业团队
  - `honors`: 企业荣誉
  - `careers`: 招贤纳士
- **管理操作**:
  - 修改各页面的图片和文字内容
  - 管理团队成员信息
  - 管理企业荣誉展示
  - 更新招聘信息

### 6. 技术支持管理

#### 6.1 文档下载管理 (`support_documents`)
- **功能**: 管理产品使用说明文档
- **管理操作**:
  - 上传产品使用说明文档
  - 关联文档到具体产品
  - 修改文档标题和描述
  - 删除过期文档
  - 统计文档下载次数

### 7. 系统管理

#### 7.1 管理员用户管理 (`admin_users`)
- **功能**: 管理后台用户权限
- **用户角色**:
  - `super_admin`: 超级管理员（所有权限）
  - `admin`: 管理员（大部分权限）
  - `editor`: 编辑员（内容编辑权限）

#### 7.2 系统配置管理 (`system_config`)
- **功能**: 管理系统全局配置
- **配置项**:
  - 网站基本信息
  - 联系方式
  - SEO设置
  - 文件上传配置

## 数据关系说明

### 层级关系
```
产品分类 (product_categories)
├── 产品子系列 (product_subcategories)
    ├── 产品 (products)
        ├── 技术规格 (product_specifications)
        ├── 产品详情 (product_details)
        └── 支持文档 (support_documents)
```

### 关联关系
- 产品子系列属于产品分类（外键关联）
- 产品属于产品分类和子系列（外键关联）
- 技术规格、产品详情、支持文档都关联到具体产品
- 新闻可以独立管理，支持分类和标签
- 解决方案和核心产品独立管理，用于首页展示

## 扩展性设计

### JSON字段设计
- `features`: 存储特性标签数组，支持动态增减
- `thumbnail_images`: 存储缩略图URL数组，支持1-5张图片
- `product_tags`: 存储产品标签数组
- `content_images`: 存储解决方案内容图片数组
- `extra_data`: 存储额外的结构化数据

### 排序和状态控制
- 所有主要表都包含 `sort_order` 字段用于排序
- 所有主要表都包含 `is_active` 字段用于启用/禁用
- 支持软删除和状态管理

### 审计字段
- `created_at`: 创建时间
- `updated_at`: 更新时间
- 支持操作日志追踪

## 使用建议

1. **数据备份**: 定期备份数据库，特别是在大量修改前
2. **图片管理**: 建议使用CDN存储图片，数据库只存储URL
3. **权限控制**: 根据用户角色限制操作权限
4. **缓存策略**: 对频繁查询的数据进行缓存优化
5. **监控日志**: 记录重要操作的日志，便于问题追踪
