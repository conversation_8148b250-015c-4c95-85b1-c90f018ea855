<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8">
      <div class="container mx-auto px-4">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">公司简介</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">了解优卡特的发展历程与企业文化</p>
        </div>
      </div>
    </section>

    <!-- 公司概览 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div data-aos="fade-right">
            <h3 class="text-5xl font-bold text-gray-800 mb-6">中国领先的一卡通科技公司</h3>
            <p class="text-gray-600 mb-6 text-lg">深圳市优卡特实业有限公司成立于2003年，是一家专注于智能一卡通系统解决方案的高新技术企业。公司总部位于中国广东深圳，以智能一卡通消费终端设备为核心，集设计、研发、生产、销售、服务于一体。</p>
            <p class="text-gray-600 mb-6 text-lg">经过20年的发展，优卡特已成为中国一卡通行业的领军企业，为超过10000家企事业单位、学校提供了专业的智能管理解决方案，帮助客户实现数字化转型升级。</p>
            
            <div class="grid grid-cols-2 gap-6 mt-8" ref="aboutStatsSection">
              <div class="text-center">
                <div class="text-4xl font-bold text-blue-600 mb-2">{{ aboutStats.foundYear }}</div>
                <p class="text-gray-600">成立年份</p>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-blue-600 mb-2">{{ aboutStats.experience }}+</div>
                <p class="text-gray-600">年行业经验</p>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-blue-600 mb-2">{{ aboutStats.customers }}+</div>
                <p class="text-gray-600">服务客户</p>
              </div>
              <div class="text-center">
                <div class="text-4xl font-bold text-blue-600 mb-2">{{ aboutStats.patents }}+</div>
                <p class="text-gray-600">技术专利</p>
              </div>
            </div>
          </div>
          
          <div data-aos="fade-left">
            <img src="https://placehold.co/600x400" alt="优卡特公司总部大楼" class="w-full rounded-lg shadow-lg">
          </div>
        </div>
      </div>
    </section>
   
    <!-- 核心业务 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          <div data-aos="fade-right" class="lg:pl-12">
            <h2 class="text-5xl font-bold text-gray-800 mb-8">核心业务</h2>
            <p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium">
              专注智能一卡通设备研发制造，提供全方位系统集成与技术支持服务。
            </p>
            <div class="space-y-6">
              <div class="flex items-start space-x-4">
                <div class="text-blue-600 text-2xl mt-1">
                  <i class="fas fa-microchip"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">设备研发制造</h3>
                  <p class="text-gray-600">专业研发生产智能消费机、门禁设备、人脸识别终端等一卡通核心设备，拥有完整的产品线和自主知识产权。</p>
                </div>
              </div>
              <div class="flex items-start space-x-4">
                <div class="text-blue-600 text-2xl mt-1">
                  <i class="fas fa-cogs"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">系统集成服务</h3>
                  <p class="text-gray-600">提供完整的一卡通系统集成方案，包括软件开发、系统部署、项目实施等全流程服务。</p>
                </div>
              </div>
              <div class="flex items-start space-x-4">
                <div class="text-blue-600 text-2xl mt-1">
                  <i class="fas fa-headset"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">技术支持服务</h3>
                  <p class="text-gray-600">7×24小时技术支持，专业的售后服务团队，确保客户系统稳定运行，提供持续的技术保障。</p>
                </div>
              </div>
            </div>
          </div>
          <div data-aos="fade-left">
            <img
              src="https://placehold.co/600x400"
              alt="核心业务展示"
              class="w-full h-auto rounded-2xl shadow-lg"
            >
          </div>
        </div>
      </div>
    </section>

    <!-- 企业资质 -->
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          <div data-aos="fade-right" class="order-2 lg:order-1">
            <img
              src="https://placehold.co/600x400"
              alt="企业资质认证"
              class="w-full h-auto rounded-2xl shadow-lg"
            >
          </div>
          <div data-aos="fade-left" class="order-1 lg:order-2 lg:pr-12">
            <h2 class="text-5xl font-bold text-gray-800 mb-8">企业资质</h2>
            <p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium">
              拥有完善的资质认证体系，确保产品质量和服务标准达到国际先进水平。
            </p>
            <div class="space-y-6">
              <div class="flex items-start space-x-4">
                <div class="text-blue-600 text-2xl mt-1">
                  <i class="fas fa-certificate"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">高新技术企业</h3>
                  <p class="text-gray-600">获得国家级高新技术企业认证，具备强大的技术创新能力和研发实力。</p>
                </div>
              </div>
              <div class="flex items-start space-x-4">
                <div class="text-green-600 text-2xl mt-1">
                  <i class="fas fa-award"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">ISO9001认证</h3>
                  <p class="text-gray-600">通过ISO9001质量管理体系认证，建立了完善的质量控制和管理流程。</p>
                </div>
              </div>
              <div class="flex items-start space-x-4">
                <div class="text-purple-600 text-2xl mt-1">
                  <i class="fas fa-shield-alt"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">3C认证</h3>
                  <p class="text-gray-600">获得中国强制性产品认证，确保产品符合国家安全标准。</p>
                </div>
              </div>
              <div class="flex items-start space-x-4">
                <div class="text-red-600 text-2xl mt-1">
                  <i class="fas fa-check-circle"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">CE认证</h3>
                  <p class="text-gray-600">通过欧盟产品安全认证，产品符合欧洲市场准入标准。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 企业使命 -->
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          <div data-aos="fade-right" class="lg:pl-12">
            <h2 class="text-5xl font-bold text-gray-800 mb-8">企业使命</h2>
            <p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium">
              用创新的产品和服务提升行业消费管理能力，让智能科技服务于人类美好生活。
            </p>
            <p class="text-xl text-gray-600 leading-relaxed">
              我们致力于通过先进的人脸识别技术和智能一卡通解决方案， 为教育、企业、政府等各个领域提供更加便捷、安全、高效的管理体验， 推动社会数字化转型，创造更美好的智能生活。
            </p>
          </div>
          <div data-aos="fade-left">
            <img
              src="https://placehold.co/600x400"
              alt="企业使命愿景"
              class="w-full h-auto rounded-2xl shadow-lg"
            >
          </div>
        </div>
      </div>
    </section>

    <!-- 企业文化 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          <div data-aos="fade-right" class="order-2 lg:order-1">
            <img
              src="https://placehold.co/600x400"
              alt="企业文化建设"
              class="w-full h-auto rounded-2xl shadow-lg"
            >
          </div>
          <div data-aos="fade-left" class="order-1 lg:order-2 lg:pr-12">
            <h2 class="text-5xl font-bold text-gray-800 mb-8">企业文化</h2>
            <p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium">
              营造积极向上的企业文化氛围，构建和谐共赢的工作环境。
            </p>
            <div class="space-y-6">
              <div class="flex items-start space-x-4">
                <div class="text-blue-600 text-2xl mt-1">
                  <i class="fas fa-rocket"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">创新驱动</h3>
                  <p class="text-gray-600">鼓励员工创新思维，营造开放包容的创新环境，让每个人都能发挥创造力，推动企业持续发展。</p>
                </div>
              </div>
              <div class="flex items-start space-x-4">
                <div class="text-green-600 text-2xl mt-1">
                  <i class="fas fa-hands-helping"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">团队协作</h3>
                  <p class="text-gray-600">倡导团队合作精神，相互支持，共同成长，打造高效协作的工作团队，实现共同目标。</p>
                </div>
              </div>
              <div class="flex items-start space-x-4">
                <div class="text-purple-600 text-2xl mt-1">
                  <i class="fas fa-graduation-cap"></i>
                </div>
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-2">学习成长</h3>
                  <p class="text-gray-600">提供完善的培训体系和职业发展通道，帮助员工实现个人价值和职业目标，与企业共同成长。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <!-- 发展愿景 -->
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          <div data-aos="fade-right" class="lg:pl-12">
            <h2 class="text-5xl font-bold text-gray-800 mb-8">发展愿景</h2>
            <p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium">
              成为全球领先的智能一卡通设备供应商，引领行业技术发展方向。
            </p>
            <p class="text-xl text-gray-600 leading-relaxed">
              我们的愿景是在未来十年内，成为全球智能一卡通行业的技术领导者， 通过持续的技术创新和产品升级，为全球客户提供最先进的智能化解决方案， 推动整个行业向更加智能化、数字化的方向发展。
            </p>
          </div>
          <div data-aos="fade-left">
            <img
              src="https://placehold.co/600x400"
              alt="企业愿景展望"
              class="w-full h-auto rounded-2xl shadow-lg"
            >
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'AboutMain',
  setup() {
    const aboutStatsSection = ref(null)

    // 统计数据
    const aboutStats = ref({
      foundYear: 2003,
      experience: 0,
      customers: 0,
      patents: 0
    })

    // 设置统计动画
    const setupAboutStatsAnimation = () => {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // 开始动画
            let current1 = 0, current2 = 0, current3 = 0

            const timer1 = setInterval(() => {
              current1 += 20 / 50
              if (current1 >= 20) { current1 = 20; clearInterval(timer1) }
              aboutStats.value.experience = Math.floor(current1)
            }, 30)

            const timer2 = setInterval(() => {
              current2 += 10000 / 50
              if (current2 >= 10000) { current2 = 10000; clearInterval(timer2) }
              aboutStats.value.customers = Math.floor(current2)
            }, 30)

            const timer3 = setInterval(() => {
              current3 += 50 / 50
              if (current3 >= 50) { current3 = 50; clearInterval(timer3) }
              aboutStats.value.patents = Math.floor(current3)
            }, 30)

            observer.unobserve(entry.target)
          }
        })
      }, { threshold: 0.5 })

      if (aboutStatsSection.value) {
        observer.observe(aboutStatsSection.value)
      }
    }

    onMounted(() => {
      // AOS动画会在index.html中自动初始化
      setupAboutStatsAnimation()
    })

    return {
      aboutStatsSection,
      aboutStats
    }
  }
}
</script>
