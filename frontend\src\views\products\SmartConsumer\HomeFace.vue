<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">安卓人脸消费终端</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能人脸识别消费系统</p>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div>
      <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
    </section>

    <!-- 产品详情内容 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- 左侧产品分类导航 -->
          <div class="lg:col-span-1">
            <ProductNavigation
              :current-category="'smart-consumer'"
              :current-product="'home-face'"
            />
          </div>

          <!-- 右侧产品详情内容 -->
          <div class="lg:col-span-3">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <div
                v-for="item in products"
                :key="item.id"
                class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1"
                data-aos="fade-up"
              >
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push(item.link)">
                  <img :src="item.image" :alt="item.title" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">{{ item.title }}</h3>
                  <p class="text-gray-600 text-sm mb-4">{{ item.subtitle }}</p>
                  <router-link :to="item.link" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import AOS from 'aos'
import ProductNavigation from '../../../components/ProductNavigation.vue'

export default {
  name: 'HomeFace',
  components: {
    ProductNavigation
  },
  setup() {
    const route = useRoute()
    const currentProduct = ref('home-face')

    const products = ref([
      {
        id: 'p301',
        title: 'P301-2D-2W 台式人脸消费机',
        subtitle: '智能识别，安全便捷',
        image: '/images/products/p301-front.png',
        link: '/products/android-face/p301'
      }
    ])

    onMounted(() => {
      AOS.init({
        duration: 1000,
        once: true
      })
    })

    return {
      currentProduct,
      products
    }
  }
}
</script>

<style>
/* 产品卡片样式 */
.product-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f3f4f6;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  border-color: #3b82f6;
}

.product-card .product-image-container {
  position: relative;
  overflow: hidden;
}

.product-card:hover .product-image {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.1);
}

.product-card .image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  color: white;
  padding: 15px;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card .product-image-container:hover .image-overlay {
  transform: translateY(0);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-card {
    margin-bottom: 1.5rem;
  }

  .product-card:hover {
    transform: translateY(-5px);
  }
}
</style>