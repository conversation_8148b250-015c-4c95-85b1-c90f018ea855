package com.youkate.controller;

import com.youkate.dto.CompanyProfileDto;
import com.youkate.dto.ContactFormDto;
import com.youkate.dto.TeamMemberDto;
import com.youkate.service.CompanyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 公司信息控制器
 * 处理公司相关的API请求
 */
@Slf4j
@RestController
@RequestMapping("/company")
@RequiredArgsConstructor
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3002", "http://localhost:8081"})
public class CompanyController {
    
    private final CompanyService companyService;
    
    /**
     * 获取公司简介信息
     */
    @GetMapping("/profile")
    public ResponseEntity<CompanyProfileDto> getCompanyProfile() {
        log.info("获取公司简介信息");
        try {
            CompanyProfileDto profile = companyService.getCompanyProfile();
            return ResponseEntity.ok(profile);
        } catch (Exception e) {
            log.error("获取公司简介信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取公司基本信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getCompanyInfo() {
        log.info("获取公司基本信息");
        try {
            Map<String, Object> info = companyService.getCompanyBasicInfo();
            return ResponseEntity.ok(info);
        } catch (Exception e) {
            log.error("获取公司基本信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取公司发展历程
     */
    @GetMapping("/history")
    public ResponseEntity<List<Map<String, Object>>> getCompanyHistory() {
        log.info("获取公司发展历程");
        try {
            List<Map<String, Object>> history = companyService.getCompanyHistory();
            return ResponseEntity.ok(history);
        } catch (Exception e) {
            log.error("获取公司发展历程失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取团队成员信息
     */
    @GetMapping("/team")
    public ResponseEntity<List<TeamMemberDto>> getTeamMembers() {
        log.info("获取团队成员信息");
        try {
            List<TeamMemberDto> team = companyService.getTeamMembers();
            return ResponseEntity.ok(team);
        } catch (Exception e) {
            log.error("获取团队成员信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取公司荣誉信息
     */
    @GetMapping("/honors")
    public ResponseEntity<Map<String, Object>> getCompanyHonors() {
        log.info("获取公司荣誉信息");
        try {
            Map<String, Object> honors = companyService.getCompanyHonors();
            return ResponseEntity.ok(honors);
        } catch (Exception e) {
            log.error("获取公司荣誉信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取招聘信息
     */
    @GetMapping("/careers")
    public ResponseEntity<Map<String, Object>> getCareersInfo() {
        log.info("获取招聘信息");
        try {
            Map<String, Object> careers = companyService.getCareersInfo();
            return ResponseEntity.ok(careers);
        } catch (Exception e) {
            log.error("获取招聘信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 提交联系表单
     */
    @PostMapping("/contact")
    public ResponseEntity<Map<String, String>> submitContactForm(@Valid @RequestBody ContactFormDto contactForm) {
        log.info("收到联系表单提交: {}", contactForm.getName());
        try {
            boolean success = companyService.processContactForm(contactForm);
            if (success) {
                return ResponseEntity.ok(Map.of(
                    "status", "success",
                    "message", "您的消息已成功提交，我们会尽快与您联系！"
                ));
            } else {
                return ResponseEntity.badRequest().body(Map.of(
                    "status", "error",
                    "message", "提交失败，请稍后重试"
                ));
            }
        } catch (Exception e) {
            log.error("处理联系表单失败", e);
            return ResponseEntity.internalServerError().body(Map.of(
                "status", "error",
                "message", "服务器错误，请稍后重试"
            ));
        }
    }
    
    /**
     * 获取公司统计数据
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getCompanyStatistics() {
        log.info("获取公司统计数据");
        try {
            Map<String, Object> statistics = companyService.getCompanyStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取公司统计数据失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取客户案例
     */
    @GetMapping("/cases")
    public ResponseEntity<List<Map<String, Object>>> getSuccessCases() {
        log.info("获取客户案例");
        try {
            List<Map<String, Object>> cases = companyService.getSuccessCases();
            return ResponseEntity.ok(cases);
        } catch (Exception e) {
            log.error("获取客户案例失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 获取客户评价
     */
    @GetMapping("/testimonials")
    public ResponseEntity<List<Map<String, Object>>> getTestimonials() {
        log.info("获取客户评价");
        try {
            List<Map<String, Object>> testimonials = companyService.getTestimonials();
            return ResponseEntity.ok(testimonials);
        } catch (Exception e) {
            log.error("获取客户评价失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "company-service",
            "timestamp", String.valueOf(System.currentTimeMillis())
        ));
    }
}
