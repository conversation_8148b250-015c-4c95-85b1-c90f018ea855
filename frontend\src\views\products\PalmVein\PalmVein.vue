<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">掌静脉系列</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">生物识别技术，安全便捷新体验</p>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div>
      <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
    </section>

    <!-- 产品分类介绍 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- 左侧产品分类导航 -->
          <div class="lg:col-span-1">
            <ProductNavigation
              :current-category="'palm-vein'"
              :current-product="currentProduct"
            />
          </div>

          <!-- 右侧产品内容 -->
          <div class="lg:col-span-3">
            <!-- 产品型号展示 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <!-- 掌静脉消费机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/palm-vein/palm-consumer')">
                  <img src="https://placehold.co/300x200" alt="掌静脉消费机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="product-title text-xl font-bold text-gray-800 mb-2
                             transition-colors duration-200 ease-out
                             hover:text-blue-600">掌静脉消费机</h3>
                  <p class="text-gray-600 text-sm mb-4">掌静脉识别消费终端</p>
                  <router-link to="/products/palm-vein/palm-consumer" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>

              <!-- 掌静脉水控机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up" data-aos-delay="200">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/palm-vein/palm-water')">
                  <img src="https://placehold.co/300x200" alt="掌静脉水控机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">掌静脉水控机</h3>
                  <p class="text-gray-600 text-sm mb-4">掌静脉识别水控系统</p>
                  <router-link to="/products/palm-vein/palm-water" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>

              <!-- 掌静脉电控机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up" data-aos-delay="400">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/palm-vein/palm-electric')">
                  <img src="https://placehold.co/300x200" alt="掌静脉电控机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">掌静脉电控机</h3>
                  <p class="text-gray-600 text-sm mb-4">掌静脉识别电控系统</p>
                  <router-link to="/products/palm-vein/palm-electric" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>
            </div>

            <!-- 第二行产品 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-8">
              <!-- 掌静脉共享洗衣机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/palm-vein/palm-washer')">
                  <img src="https://placehold.co/300x200" alt="掌静脉共享洗衣机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">掌静脉共享洗衣机</h3>
                  <p class="text-gray-600 text-sm mb-4">掌静脉识别共享洗衣</p>
                  <router-link to="/products/palm-vein/palm-washer" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>

              <!-- 掌静脉共享吹风机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up" data-aos-delay="200">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/palm-vein/palm-dryer')">
                  <img src="https://placehold.co/300x200" alt="掌静脉共享吹风机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">掌静脉共享吹风机</h3>
                  <p class="text-gray-600 text-sm mb-4">掌静脉识别共享吹风</p>
                  <router-link to="/products/palm-vein/palm-dryer" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>

              <!-- 掌静脉门禁/考勤机 -->
              <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden
                           transition-all duration-200 ease-out
                           hover:shadow-xl hover:-translate-y-1" data-aos="fade-up" data-aos-delay="400">
                <div class="product-image-container h-64 relative overflow-hidden cursor-pointer" @click="$router.push('/products/palm-vein/palm-access')">
                  <img src="https://placehold.co/300x200" alt="掌静脉门禁/考勤机" class="product-image w-full h-full object-cover">
                  <div class="image-overlay">
                    <span><i class="fas fa-eye mr-2"></i>查看详情</span>
                  </div>
                </div>
                <div class="p-6">
                  <h3 class="text-xl font-bold text-gray-800 mb-2">掌静脉门禁/考勤机</h3>
                  <p class="text-gray-600 text-sm mb-4">掌静脉识别门禁考勤</p>
                  <router-link to="/products/palm-vein/palm-access" class="block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center">
                    查看详情
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import AOS from 'aos'
import ProductNavigation from '@/components/ProductNavigation.vue'

export default {
  name: 'PalmVein',
  components: {
    ProductNavigation
  },
  setup() {
    const route = useRoute()
    const currentProduct = ref(null)

    // 根据路由参数设置当前产品
    if (route.params.productId) {
      currentProduct.value = route.params.productId
    }

    onMounted(() => {
      AOS.init({
        duration: 1000,
        once: true
      })
    })

    return {
      currentProduct
    }
  }
}
</script>

<style>
/* 产品卡片样式 */
.product-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f3f4f6;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  border-color: #3b82f6;
}

.product-card .product-image-container {
  position: relative;
  overflow: hidden;
}

.product-card:hover .product-image {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.1);
}

.product-card .image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  color: white;
  padding: 15px;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card .product-image-container:hover .image-overlay {
  transform: translateY(0);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-card {
    margin-bottom: 1.5rem;
  }

  .product-card:hover {
    transform: translateY(-5px);
  }
}
</style>
