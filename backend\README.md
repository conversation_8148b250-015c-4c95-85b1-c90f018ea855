# Youkate Backend

优卡特官网后端服务

## 数据库配置

本项目使用MySQL数据库，默认配置如下：

- 数据库名: `youkate_db`
- 主机: `localhost`
- 端口: `3306`
- 用户名: `root`
- 密码: `123456`

### 初始化数据库

1. 确保MySQL服务正在运行
2. 在Navicat中执行 `data/init.sql` 文件来创建数据库和表结构
3. 或者使用命令行执行：
   ```bash
   mysql -u root -p < data/init.sql
   ```

### 服务器部署注意事项

在服务器上部署时，请根据实际情况修改 `src/main/resources/application.yml` 中的数据库连接配置。

## 构建和运行

```bash
# 构建项目
mvn clean package

# 运行项目
java -jar target/youkate-backend-1.0.0.jar
```