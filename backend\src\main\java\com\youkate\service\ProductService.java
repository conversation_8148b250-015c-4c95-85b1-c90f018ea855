package com.youkate.service;

import com.youkate.entity.Product;
import com.youkate.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 产品服务类
 */
@Service
@RequiredArgsConstructor
@Transactional
public class ProductService {
    
    private final ProductRepository productRepository;
    
    /**
     * 获取所有产品
     */
    public List<Product> getAllProducts() {
        return productRepository.findByIsActiveTrueOrderBySortOrderAsc();
    }
    
    /**
     * 根据ID获取产品
     */
    public Optional<Product> getProductById(Long id) {
        return productRepository.findById(id);
    }
    
    /**
     * 根据分类获取产品
     */
    public List<Product> getProductsByCategory(String category) {
        return productRepository.findByCategoryAndIsActiveTrue(category);
    }
    
    /**
     * 搜索产品
     */
    public List<Product> searchProducts(String keyword) {
        return productRepository.findByNameContaining(keyword);
    }
    
    /**
     * 获取推荐产品
     */
    public List<Product> getFeaturedProducts() {
        return productRepository.findFeaturedProducts();
    }
    
    /**
     * 创建产品
     */
    public Product createProduct(Product product) {
        return productRepository.save(product);
    }
    
    /**
     * 更新产品
     */
    public Product updateProduct(Long id, Product productDetails) {
        return productRepository.findById(id)
            .map(product -> {
                product.setName(productDetails.getName());
                product.setDescription(productDetails.getDescription());
                product.setPrice(productDetails.getPrice());
                product.setImageUrl(productDetails.getImageUrl());
                product.setDetailImages(productDetails.getDetailImages());
                product.setCategory(productDetails.getCategory());
                product.setBadge(productDetails.getBadge());
                product.setFeatures(productDetails.getFeatures());
                product.setSpecifications(productDetails.getSpecifications());
                product.setThemeColor(productDetails.getThemeColor());
                product.setSortOrder(productDetails.getSortOrder());
                product.setIsActive(productDetails.getIsActive());
                return productRepository.save(product);
            })
            .orElseThrow(() -> new RuntimeException("产品不存在: " + id));
    }
    
    /**
     * 删除产品（软删除）
     */
    public void deleteProduct(Long id) {
        productRepository.findById(id)
            .map(product -> {
                product.setIsActive(false);
                return productRepository.save(product);
            })
            .orElseThrow(() -> new RuntimeException("产品不存在: " + id));
    }
    
    /**
     * 物理删除产品
     */
    public void hardDeleteProduct(Long id) {
        productRepository.deleteById(id);
    }
}
