<template>
  <div class="bg-white rounded-xl shadow-lg p-6 sticky top-24" data-aos="fade-right">
    <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">产品分类</h3>
    <nav class="space-y-2">
      <div 
        v-for="category in productCategories" 
        :key="category.id"
        class="category-item"
      >
        <!-- 主分类 -->
        <div
          @click="onCategoryClick"
          :data-category-id="category.id"
          :class="[
            'flex items-center justify-between p-4 rounded-lg cursor-pointer transition-all duration-300 hover:transform hover:translate-x-1 group',
            currentCategory === category.id ? 'bg-blue-200' : 'hover:bg-blue-100'
          ]"
        >
          <div class="flex items-center space-x-3">
            <span
              :class="[
                'font-semibold transition-colors duration-300',
                (expandedCategory === category.id || currentCategory === category.id) ? 'text-blue-700' :
                'text-gray-700 group-hover:text-blue-600'
              ]"
            >
              {{ category.name }}
            </span>
          </div>
          <i
            :class="[
              'fas fa-chevron-down transition-transform duration-300',
              expandedCategory === category.id ? 'rotate-180' : '',
              currentCategory === category.id ? 'text-blue-600' :
              expandedCategory === category.id ? 'text-blue-600' :
              'text-gray-400 group-hover:text-blue-600'
            ]"
          ></i>
        </div>

        <!-- 子菜单 -->
        <div
          :class="[
            'overflow-hidden transition-all duration-300 ease-in-out',
            expandedCategory === category.id ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          ]"
        >
          <div class="pl-1 pt-2 space-y-1">
            <div
              v-for="product in category.products"
              :key="product.id"
              @click.stop="navigateToProduct(category.id, product.id)"
              :class="[
                'p-3 rounded-lg cursor-pointer transition-all duration-300 group bg-white'
              ]"
            >
              <span
              :class="[
                'font-medium transition-all duration-300 inline-block text-center w-full',
                currentProduct === product.id ?
                  'text-blue-700 font-bold' :
                  'text-gray-600',
                currentProduct !== product.id ? 'group-hover:text-blue-600' : ''
              ]"
              >
                {{ product.name }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

export default {
  name: 'ProductNavigation',
  props: {
    currentCategory: {
      type: String,
      default: null
    },
    currentProduct: {
      type: String,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const expandedCategory = ref(null)
    
    // 根据当前路由自动检测并设置导航状态
    const detectCurrentCategory = () => {
      const currentPath = route.path
      
      // 路由路径到分类ID的映射
      const routeToCategory = {
        '/products/smart-consumer': 'smart-consumer',
        '/products/ai-platform': 'ai-platform', 
        '/products/water-control': 'water-control',
        '/products/electric-control': 'electric-control',
        '/products/smart-meter': 'smart-meter',
        '/products/shared-washer': 'shared-washer',
        '/products/shared-dryer': 'shared-dryer',
        '/products/access-attendance': 'access-attendance',
        '/products/shared-video-phone': 'shared-video-phone',
        '/products/visitor-machine': 'visitor-machine',
        '/products/smart-self-service': 'smart-self-service',
        '/products/palm-vein': 'palm-vein'
      }
      
      // 检查是否匹配任何已知的产品系列路径
      for (const [routePath, categoryId] of Object.entries(routeToCategory)) {
        if (currentPath === routePath || currentPath.startsWith(routePath + '/')) {
          return categoryId
        }
      }
      
      return null
    }
    
    // 初始化时自动检测当前分类
    const currentDetectedCategory = detectCurrentCategory()
    if (currentDetectedCategory) {
      expandedCategory.value = currentDetectedCategory
    }

    // 产品类别数据结构 - 根据新的分类清单更新
    const productCategories = ref([
      {
        id: 'smart-consumer',
        name: '智能消费机系列',
        color: 'blue',
        icon: 'fas fa-mobile-alt',
        products: [
          { id: 'home-face', name: '安卓人脸消费终端' },
          { id: 'real-qr', name: '安卓二维码消费终端' },
          { id: 'ar-terminal', name: 'AR消费终端' }
        ]
      },
      {
        id: 'ai-platform',
        name: 'AI智能算台系列',
        color: 'green',
        icon: 'fas fa-brain',
        products: [
          { id: 'product-recognition', name: 'AI商品识别算台' },
          { id: 'smart-weighing', name: '智能称重算台' }
        ]
      },
      {
        id: 'water-control',
        name: '智能水控机系列',
        color: 'cyan',
        icon: 'fas fa-tint',
        products: [
          { id: 'smart-water', name: '智能一体水控机' },
          { id: 'split-water', name: '智能分体水控机' }
        ]
      },
      {
        id: 'electric-control',
        name: '智能电控机系列',
        color: 'orange',
        icon: 'fas fa-bolt',
        products: [
          { id: 'timing-electric', name: '计时计量电控机' },
          { id: 'timing-control', name: '计时电控机' }
        ]
      },
      {
        id: 'smart-meter',
        name: '智能水表/电表系列',
        color: 'purple',
        icon: 'fas fa-gauge-high',
        products: [
          { id: 'water-meter', name: '智能水表' },
          { id: 'electric-meter', name: '智能电表' }
        ]
      },
      {
        id: 'shared-washer',
        name: '共享洗衣机系列',
        color: 'indigo',
        icon: 'fas fa-washing-machine',
        products: []
      },
      {
        id: 'shared-dryer',
        name: '共享吹风机系列',
        color: 'red',
        icon: 'fas fa-wind',
        products: []
      },
      {
        id: 'access-attendance',
        name: '门禁/考勤系列',
        color: 'blue',
        icon: 'fas fa-door-open',
        products: []
      },
      {
        id: 'shared-video-phone',
        name: '共享视频电话机系列',
        color: 'green',
        icon: 'fas fa-video',
        products: []
      },
      {
        id: 'visitor-machine',
        name: '访客机系列',
        color: 'cyan',
        icon: 'fas fa-user-check',
        products: []
      },
      {
        id: 'smart-self-service',
        name: '智能自助机系列',
        color: 'orange',
        icon: 'fas fa-kiosk',
        products: [
          { id: 'android-self', name: '安卓自助机' },
          { id: 'windows-self', name: 'Windows自助机' }
        ]
      },
      {
        id: 'palm-vein',
        name: '掌静脉系列',
        color: 'purple',
        icon: 'fas fa-hand-paper',
        products: [
          { id: 'palm-consumer', name: '掌静脉消费机' },
          { id: 'palm-water', name: '掌静脉水控机' },
          { id: 'palm-electric', name: '掌静脉电控机' },
          { id: 'palm-washer', name: '掌静脉共享洗衣机' },
          { id: 'palm-dryer', name: '掌静脉共享吹风机' },
          { id: 'palm-access', name: '掌静脉门禁/考勤机' }
        ]
      }
    ])

    // 方法
    const onCategoryClick = (event) => {
      // 完全阻止事件传播和默认行为
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()
      
      // 从 data 属性获取分类ID
      const categoryId = event.currentTarget.getAttribute('data-category-id')
      
      // 如果点击的是当前已展开的分类，则收起
      if (expandedCategory.value === categoryId) {
        expandedCategory.value = null
      } else {
        // 否则，展开新分类（这会自动收起之前的分类，因为expandedCategory是单一值）
        expandedCategory.value = categoryId
      }
      
      // 返回 false 进一步阻止事件
      return false
    }

    const navigateToProduct = (categoryId, productId) => {
      // 确保当前分类保持展开状态
      expandedCategory.value = categoryId
      
      // 根据分类ID映射到正确的路由路径
      const categoryRouteMap = {
        'smart-consumer': 'smart-consumer',
        'ai-platform': 'ai-platform',
        'water-control': 'water-control',
        'electric-control': 'electric-control',
        'smart-meter': 'smart-meter',
        'shared-washer': 'shared-washer',
        'shared-dryer': 'shared-dryer',
        'access-attendance': 'access-attendance',
        'shared-video-phone': 'shared-video-phone',
        'visitor-machine': 'visitor-machine',
        'smart-self-service': 'smart-self-service',
        'palm-vein': 'palm-vein'
      }
      
      const routePath = categoryRouteMap[categoryId] || categoryId
      
      // 跳转到子产品页面
      router.push(`/products/${routePath}/${productId}`)
    }

    // 监听路由变化，自动更新导航状态（但不干扰手动点击）
    watch(() => route.path, () => {
      const detectedCategory = detectCurrentCategory()
      if (detectedCategory && !expandedCategory.value) {
        // 只有在没有展开任何分类时才自动展开
        expandedCategory.value = detectedCategory
      }
    }, { immediate: true })
    
    // 计算当前产品ID
    const currentProduct = computed(() => {
      return props.currentProduct || null
    })

    // 监听当前分类变化，自动展开对应分类（但不干扰手动点击）
    watch(() => props.currentCategory, (newCategory) => {
      if (newCategory && !expandedCategory.value) {
        // 只有在没有展开任何分类时才自动展开
        expandedCategory.value = newCategory
      }
    }, { immediate: true })

    return {
      expandedCategory,
      productCategories,
      currentProduct,
      onCategoryClick,
      navigateToProduct
    }
  }
}
</script>

<style scoped>
/* 自定义颜色类 - 蓝色主题 */
.bg-blue-25 { background-color: rgba(59, 130, 246, 0.25); }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #bfdbfe; }
.bg-blue-200 { background-color: #93c5fd; }
.border-blue-200 { border-color: #bfdbfe; }
.border-blue-300 { border-color: #93c5fd; }
.text-blue-500 { color: #3b82f6; }
.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }

/* 自定义颜色类 - 绿色主题 */
.bg-green-25 { background-color: rgba(34, 197, 94, 0.25); }
.bg-green-50 { background-color: #f0fdf4; }
.bg-green-100 { background-color: #bbf7d0; }
.bg-green-200 { background-color: #86efac; }
.border-green-200 { border-color: #bbf7d0; }
.border-green-300 { border-color: #86efac; }
.text-green-500 { color: #22c55e; }
.text-green-600 { color: #16a34a; }
.text-green-700 { color: #15803d; }

/* 自定义颜色类 - 紫色主题 */
.bg-purple-25 { background-color: rgba(168, 85, 247, 0.25); }
.bg-purple-50 { background-color: #faf5ff; }
.bg-purple-100 { background-color: #e9d5ff; }
.bg-purple-200 { background-color: #d8b4fe; }
.border-purple-200 { border-color: #e9d5ff; }
.border-purple-300 { border-color: #d8b4fe; }
.text-purple-500 { color: #a855f7; }
.text-purple-600 { color: #9333ea; }
.text-purple-700 { color: #7c3aed; }

/* 自定义颜色类 - 橙色主题 */
.bg-orange-25 { background-color: rgba(249, 115, 22, 0.25); }
.bg-orange-50 { background-color: #fff7ed; }
.bg-orange-100 { background-color: #fed7aa; }
.bg-orange-200 { background-color: #fdba74; }
.border-orange-200 { border-color: #fed7aa; }
.border-orange-300 { border-color: #fdba74; }
.text-orange-500 { color: #f97316; }
.text-orange-600 { color: #ea580c; }
.text-orange-700 { color: #c2410c; }

/* 自定义颜色类 - 红色主题 */
.bg-red-25 { background-color: rgba(239, 68, 68, 0.25); }
.bg-red-50 { background-color: #fef2f2; }
.bg-red-100 { background-color: #fecaca; }
.bg-red-200 { background-color: #fca5a5; }
.border-red-200 { border-color: #fecaca; }
.border-red-300 { border-color: #fca5a5; }
.text-red-500 { color: #ef4444; }
.text-red-600 { color: #dc2626; }
.text-red-700 { color: #b91c1c; }

/* 自定义颜色类 - 靛蓝主题 */
.bg-indigo-25 { background-color: rgba(99, 102, 241, 0.25); }
.bg-indigo-50 { background-color: #eef2ff; }
.bg-indigo-100 { background-color: #c7d2fe; }
.bg-indigo-200 { background-color: #a5b4fc; }
.border-indigo-200 { border-color: #c7d2fe; }
.border-indigo-300 { border-color: #a5b4fc; }
.text-indigo-500 { color: #6366f1; }
.text-indigo-600 { color: #4f46e5; }
.text-indigo-700 { color: #4338ca; }

/* 自定义颜色类 - 青色主题 */
.bg-cyan-25 { background-color: rgba(6, 182, 212, 0.25); }
.bg-cyan-50 { background-color: #ecfeff; }
.bg-cyan-100 { background-color: #a5f3fc; }
.bg-cyan-200 { background-color: #67e8f9; }
.border-cyan-200 { border-color: #a5f3fc; }
.border-cyan-300 { border-color: #67e8f9; }
.text-cyan-500 { color: #06b6d4; }
.text-cyan-600 { color: #0891b2; }
.text-cyan-700 { color: #0e7490; }

/* 分类项样式 */
.category-item {
  position: relative;
}

/* 响应式设计优化 */
@media (max-width: 1024px) {
  .sticky {
    position: relative;
  }
}
</style>