-- =============================================
-- 友情链接管理表 - 简化版数据库架构
-- =============================================

-- 友情链接表
CREATE TABLE friendly_links (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '链接ID',
    name VARCHAR(100) NOT NULL COMMENT '链接名称',
    url VARCHAR(500) NOT NULL COMMENT '链接URL地址',
    description VARCHAR(300) COMMENT '链接描述',
    sort_order INT DEFAULT 0 COMMENT '排序权重，数字越大越靠前',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='友情链接表';

-- =============================================
-- 初始化友情链接数据
-- =============================================

INSERT INTO friendly_links (name, url, description, sort_order) VALUES
('智慧校园网', 'https://www.smartcampus.com', '专业的智慧校园解决方案提供商', 100),
('人脸识别技术联盟', 'https://www.facetech.org', '人脸识别技术行业联盟官网', 95),
('智能设备网', 'https://www.smartdevice.cn', '智能设备行业门户网站', 90),
('一卡通系统网', 'https://www.cardtech.com', '一卡通系统技术交流平台', 85),
('安防产业网', 'https://www.security.com.cn', '安防行业专业门户', 80),
('物联网世界', 'https://www.iotworld.com.cn', '物联网技术与应用门户', 75),
('工信部', 'https://www.miit.gov.cn', '中华人民共和国工业和信息化部', 70),
('深圳市政府', 'https://www.sz.gov.cn', '深圳市人民政府官网', 65),
('中国安防网', 'https://www.cps.com.cn', '中国安防行业网', 60),
('慧聪安防网', 'https://security.hc360.com', '慧聪安防行业门户', 55);

-- =============================================
-- 友情链接管理相关视图
-- =============================================

-- 活跃友情链接视图
CREATE VIEW active_friendly_links AS
SELECT
    id,
    name,
    url,
    description,
    sort_order,
    created_at
FROM friendly_links
WHERE is_active = TRUE
ORDER BY sort_order DESC, created_at DESC;

-- =============================================
-- 基本查询示例
-- =============================================

-- 获取所有活跃的友情链接
-- SELECT * FROM active_friendly_links;

-- 获取前10个友情链接
-- SELECT * FROM active_friendly_links LIMIT 10;

-- 根据名称搜索友情链接
-- SELECT * FROM friendly_links WHERE name LIKE '%关键词%' AND is_active = TRUE;
