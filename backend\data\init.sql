-- Create database
CREATE DATABASE IF NOT EXISTS youkate_db;

-- Use database
USE youkate_db;

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500) NOT NULL,
    price VARCHAR(50) NOT NULL,
    image_url VARCHAR(500),
    detail_images TEXT,
    category VARCHAR(50) NOT NULL,
    badge VARCHAR(50),
    features TEXT,
    specifications TEXT,
    theme_color VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create news table
CREATE TABLE IF NOT EXISTS news (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    summary VARCHAR(500),
    content TEXT NOT NULL,
    image_url VARCHAR(500),
    category VARCHAR(50) NOT NULL,
    author VA<PERSON>HA<PERSON>(100) DEFAULT '优卡特',
    view_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_published BOOLEAN DEFAULT TRUE,
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create company_info table
CREATE TABLE IF NOT EXISTS company_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    address VARCHAR(200),
    phone VARCHAR(50),
    email VARCHAR(100),
    website VARCHAR(100),
    business_hours VARCHAR(100),
    mission TEXT,
    vision TEXT,
    values TEXT,
    founded_year INT,
    employee_count INT,
    logo_url VARCHAR(500),
    coordinates VARCHAR(100),
    social_media TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert sample data
-- Products
INSERT INTO products (name, description, price, category, badge, theme_color) VALUES 
('智能停车管理系统', '基于物联网技术的智能停车解决方案，支持车位检测、自动计费、移动支付等功能', '面议', '智能交通', '热销', '#3B82F6'),
('智能门禁系统', '集人脸识别、刷卡、密码等多种方式于一体的智能门禁管理系统', '面议', '安防系统', '推荐', '#10B981'),
('智能照明系统', '可根据环境光线和人员活动自动调节亮度的节能照明系统', '面议', '智能建筑', '新品', '#8B5CF6'),
('环境监测系统', '实时监测空气质量、温湿度、噪音等环境参数的智能系统', '面议', '环境监测', '推荐', '#F59E0B'),
('能源管理系统', '对企业用电、用水等能源消耗进行实时监控和分析的系统', '面议', '能源管理', '热销', '#EF4444'),
('智能消防系统', '集成烟雾探测、自动报警、应急广播等功能的智能消防解决方案', '面议', '安防系统', '推荐', '#EC4899');

-- News
INSERT INTO news (title, summary, content, category, is_featured) VALUES 
('优卡特荣获2023年度智慧城市建设优秀解决方案奖', '在近日举办的智慧城市建设峰会上，优卡特凭借其创新的智能交通解决方案荣获优秀解决方案奖', '在近日举办的智慧城市建设峰会上，优卡特凭借其创新的智能交通解决方案荣获优秀解决方案奖。这一荣誉充分体现了公司在智慧城市建设领域的技术实力和创新能力。公司将继续致力于为客户提供更优质的产品和服务，推动智慧城市建设的发展。', 'company', TRUE),
('优卡特推出新一代智能停车管理系统', '优卡特正式发布新一代智能停车管理系统，该系统采用了最新的物联网技术和人工智能算法', '优卡特正式发布新一代智能停车管理系统，该系统采用了最新的物联网技术和人工智能算法，能够实现车位的精准检测、车辆的自动识别和计费等功能。新系统还支持多种支付方式，包括移动支付、ETC等，大大提升了用户的停车体验。该系统的推出将进一步巩固优卡特在智能交通领域的领先地位。', 'product', TRUE);

-- Company Info
INSERT INTO company_info (name, description, address, phone, email, website, business_hours, mission, vision, values, founded_year, employee_count, coordinates) VALUES 
('深圳市优卡特科技有限公司', '优卡特科技是一家专注于智慧城市建设的高新技术企业，致力于为客户提供全方位的智能化解决方案。公司拥有一支专业的技术团队和丰富的行业经验，在智能交通、安防系统、智能建筑等领域具有领先的技术优势。', '广东省深圳市南山区科技园南区R2-B栋3楼', '************', '<EMAIL>', 'www.youkate.com', '周一至周五 9:00-18:00', '用科技创造更美好的城市生活', '成为智慧城市解决方案的领军企业', '创新、诚信、卓越、共赢', 2015, 100, '22.5431,113.9432');