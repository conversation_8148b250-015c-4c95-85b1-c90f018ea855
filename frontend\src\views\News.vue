<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">新闻中心</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">了解优卡特最新动态与行业资讯</p>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div>
      <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
    </section>

    <!-- 新闻分类 -->
    <section class="py-12 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="flex flex-wrap justify-center gap-4" data-aos="fade-right">
          <button
            class="category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300"
            :class="activeCategory === 'all' ? 'bg-blue-600 text-white' : 'bg-white text-gray-600 hover:text-blue-600'"
            @click="setCategory('all')"
          >
            全部新闻
          </button>
          <router-link
            to="/news/company"
            class="category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"
          >
            企业新闻
          </router-link>
          <router-link
            to="/news/industry"
            class="category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"
          >
            行业资讯
          </router-link>
        </div>
      </div>
    </section>

    <!-- 主要新闻展示 -->
    <section class="pt-8 pb-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- 主要新闻 -->
          <div class="lg:col-span-2">
            <div class="grid gap-8">
              <!-- 头条新闻 -->
              <a
                href="#"
                @click.prevent="openNewsDetail('featured-news-1')"
                v-if="searchKeyword.trim() === '' && (activeCategory === 'all' || activeCategory === 'company')"
                class="featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer"
                data-aos="fade-up"
              >
                <div class="relative">
                  <img src="https://placehold.co/800x400" alt="优卡特发布新一代人脸识别终端" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500">
                  <div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold">
                    头条新闻
                  </div>
                  <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                    <div class="flex items-center mb-2">
                      <span class="bg-blue-100 text-blue-600 px-3 py-1 rounded text-sm">企业新闻</span>
                      <span class="text-white text-sm ml-3">2023-12-15</span>
                    </div>
                    <h2 class="text-2xl font-bold text-white mb-2 group-hover:text-blue-200 transition-colors duration-300">优卡特发布新一代人脸识别终端</h2>
                    <p class="text-gray-200">公司新一代人脸识别终端采用深度学习算法，识别速度和准确率大幅提升，为客户提供更优质的服务体验...</p>
                  </div>
                </div>
              </a>

              <!-- 新闻列表 -->
              <div class="space-y-6">
                <!-- 有搜索结果时显示新闻列表 -->
                <div v-if="displayedNews.length > 0">
                  <a
                    href="#"
                    @click.prevent="openNewsDetail(news.id)"
                    v-for="news in displayedNews"
                    :key="news.id"
                    class="news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block"
                  data-aos="fade-up"
                >
                  <img :src="news.image" :alt="news.title" class="news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300">
                  <div class="p-4 flex-1">
                    <div class="flex items-center mb-2">
                      <span :class="news.categoryClass" class="px-2 py-1 rounded text-xs">{{ news.categoryName }}</span>
                      <span class="text-gray-500 text-xs ml-2">{{ news.date }}</span>
                    </div>
                    <h4 class="text-lg font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition duration-300 cursor-pointer">
                      {{ news.title }}
                    </h4>
                    <p class="text-gray-600 text-sm">{{ news.excerpt }}</p>
                  </div>
                </a>
                </div>

                <!-- 无搜索结果时显示 -->
                <div v-else-if="searchKeyword.trim() !== ''" class="text-center py-16">
                  <div class="max-w-md mx-auto">
                    <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">未找到相关新闻</h3>
                    <p class="text-gray-500 mb-4">
                      没有找到包含 "<span class="font-medium text-blue-600">{{ searchKeyword }}</span>" 的新闻内容
                    </p>
                    <button @click="clearSearch" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                      <i class="fas fa-times mr-2"></i>清除搜索
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 侧边栏 -->
          <div class="lg:col-span-1">
            <!-- 搜索框 -->
            <div class="bg-white rounded-xl p-6 shadow-lg mb-8" data-aos="fade-up">
              <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-search text-blue-500 mr-2"></i>
                搜索资讯
              </h3>
              <div class="relative">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400"></i>
                <input
                  type="text"
                  placeholder="搜索资讯..."
                  v-model="searchKeyword"
                  @keyup.enter="searchNews"
                  class="pl-10 pr-4 py-3 w-full bg-blue-50 border border-blue-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-400 focus:bg-white transition-all duration-200 placeholder-blue-400"
                >
              </div>
            </div>

            <div class="space-y-8">
              <!-- 热门新闻 -->
              <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100" data-aos="fade-left">
                <h3 class="text-xl font-bold text-gray-800 mb-4 border-b border-gray-200 pb-2">
                  <i class="fas fa-fire text-red-500 mr-2"></i>热门新闻
                </h3>
                <div class="space-y-4">
                  <div class="flex items-start space-x-3 group cursor-pointer">
                    <img src="https://placehold.co/80x60" alt="新闻缩略图" class="w-16 h-12 object-cover rounded group-hover:scale-105 transition-transform duration-300">
                    <div class="flex-1">
                      <h4 class="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition duration-300 line-clamp-2">
                        优卡特与某知名大学达成战略合作
                      </h4>
                      <p class="text-xs text-gray-500 mt-1">2023-12-03</p>
                    </div>
                  </div>
                  
                  <div class="flex items-start space-x-3 group cursor-pointer">
                    <img src="https://placehold.co/80x60" alt="新闻缩略图" class="w-16 h-12 object-cover rounded group-hover:scale-105 transition-transform duration-300">
                    <div class="flex-1">
                      <h4 class="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition duration-300 line-clamp-2">
                        智能消费终端市场前景分析
                      </h4>
                      <p class="text-xs text-gray-500 mt-1">2023-12-01</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 联系我们 -->
              <div class="bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl p-6 text-white" data-aos="fade-left" data-aos-delay="200">
                <h3 class="text-xl font-bold mb-4">
                  <i class="fas fa-phone mr-2"></i>联系我们
                </h3>
                <div class="space-y-3 text-sm">
                  <div class="flex items-center">
                    <i class="fas fa-map-marker-alt w-4 mr-3"></i>
                    <span>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-phone w-4 mr-3"></i>
                    <a href="tel:159-8667-2052" class="text-white hover:text-blue-200 transition duration-300">159-8667-2052</a>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-envelope w-4 mr-3"></i>
                    <a href="mailto:<EMAIL>" class="text-white hover:text-blue-200 transition duration-300"><EMAIL></a>
                  </div>
                </div>
                <router-link to="/contact" class="inline-block mt-4 px-4 py-2 bg-white text-blue-600 rounded-lg font-semibold hover:bg-gray-100 transition duration-300">
                  了解更多
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 分页组件 -->
    <Pagination
      v-if="totalFilteredItems > 0"
      :current-page="currentPage"
      :total-items="totalFilteredItems"
      :items-per-page="itemsPerPage"
      :theme-color="'blue'"
      @page-change="handlePageChange"
      @items-per-page-change="handleItemsPerPageChange"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import AOS from 'aos'
import Pagination from '../components/common/Pagination.vue'

export default {
  name: 'News',
  components: {
    Pagination
  },
  setup() {
    const activeCategory = ref('all')

    // 分页相关状态
    const currentPage = ref(1)
    const itemsPerPage = ref(10)
    const searchKeyword = ref('')

    // 整合所有新闻数据
    const allNews = ref([
      // 企业新闻
      {
        id: 1,
        title: '优卡特发布新一代人脸识别终端',
        excerpt: '公司新一代人脸识别终端采用深度学习算法，识别速度和准确率大幅提升，为客户提供更优质的服务体验。',
        category: 'company',
        categoryName: '企业新闻',
        categoryClass: 'bg-blue-100 text-blue-600',
        date: '2024-01-15',
        image: 'https://placehold.co/200x150'
      },
      {
        id: 2,
        title: '优卡特参加2024智慧校园建设展览会',
        excerpt: '本次展会上，优卡特展示了最新的智慧校园一卡通解决方案，引起了广泛关注和好评。',
        category: 'company',
        categoryName: '企业新闻',
        categoryClass: 'bg-blue-100 text-blue-600',
        date: '2024-01-10',
        image: 'https://placehold.co/200x150'
      },
      {
        id: 3,
        title: '公司获得ISO9001质量管理体系认证',
        excerpt: '经过严格的审核，优卡特成功获得ISO9001质量管理体系认证，标志着公司在质量管理方面达到国际先进水平。',
        category: 'company',
        categoryName: '企业新闻',
        categoryClass: 'bg-blue-100 text-blue-600',
        date: '2024-01-05',
        image: 'https://placehold.co/200x150'
      },
      // 行业资讯
      {
        id: 4,
        title: '2024年智能一卡通市场分析报告',
        excerpt: '根据最新市场调研数据，2024年智能一卡通市场规模预计将达到500亿元，同比增长15%。',
        category: 'industry',
        categoryName: '行业资讯',
        categoryClass: 'bg-green-100 text-green-600',
        date: '2024-01-12',
        image: 'https://placehold.co/200x150'
      },
      {
        id: 5,
        title: '人脸识别技术在校园安全中的应用',
        excerpt: '随着人工智能技术的快速发展，人脸识别技术在校园安全管理中发挥着越来越重要的作用。',
        category: 'industry',
        categoryName: '行业资讯',
        categoryClass: 'bg-green-100 text-green-600',
        date: '2024-01-08',
        image: 'https://placehold.co/200x150'
      },
      {
        id: 6,
        title: '数据安全法实施细则解读',
        excerpt: '新版数据安全法实施细则对智能设备数据采集、存储、使用提出了更严格的要求。',
        category: 'industry',
        categoryName: '行业资讯',
        categoryClass: 'bg-green-100 text-green-600',
        date: '2024-01-03',
        image: 'https://placehold.co/200x150'
      },
      // 技术支持
      {
        id: 7,
        title: '一卡通系统常见问题解答',
        excerpt: '本文整理了用户在使用一卡通系统过程中经常遇到的问题及解决方案。',
        category: 'technical',
        categoryName: '技术支持',
        categoryClass: 'bg-purple-100 text-purple-600',
        date: '2024-01-01',
        image: 'https://placehold.co/200x150'
      },
      {
        id: 8,
        title: '设备维护保养指南',
        excerpt: '为确保设备正常运行，延长使用寿命，请按照本指南进行定期维护保养。',
        category: 'technical',
        categoryName: '技术支持',
        categoryClass: 'bg-purple-100 text-purple-600',
        date: '2023-12-28',
        image: 'https://placehold.co/200x150'
      }
    ])

    // 过滤和搜索新闻
    const filteredAndSearchedNews = computed(() => {
      let news = allNews.value

      // 搜索筛选
      if (searchKeyword.value.trim() !== '') {
        const keyword = searchKeyword.value.toLowerCase()
        news = news.filter(item => 
          item.title.toLowerCase().includes(keyword) ||
          item.excerpt.toLowerCase().includes(keyword) ||
          item.categoryName.toLowerCase().includes(keyword)
        )
      }

      // 分类筛选
      if (activeCategory.value !== 'all') {
        news = news.filter(item => item.category === activeCategory.value)
      }

      return news
    })

    // 分页显示的新闻
    const displayedNews = computed(() => {
      const startIndex = (currentPage.value - 1) * itemsPerPage.value
      const endIndex = startIndex + itemsPerPage.value
      return filteredAndSearchedNews.value.slice(startIndex, endIndex)
    })

    // 更新总数
    const totalFilteredItems = computed(() => filteredAndSearchedNews.value.length)

    const setCategory = (category) => {
      activeCategory.value = category
      currentPage.value = 1 // 重置到第一页
    }

    const searchNews = () => {
      currentPage.value = 1 // 搜索时重置到第一页
    }

    const clearSearch = () => {
      searchKeyword.value = ''
      currentPage.value = 1 // 清除搜索时重置到第一页
    }

    // 分页处理函数
    const handlePageChange = (page) => {
      currentPage.value = page
    }

    const handleItemsPerPageChange = (newItemsPerPage) => {
      itemsPerPage.value = newItemsPerPage
      currentPage.value = 1 // 重置到第一页
    }

    const openNewsDetail = (newsId) => {
      // 这里可以跳转到新闻详情页面
      console.log('打开新闻详情:', newsId)
      // 示例：跳转到新闻详情页
      // this.$router.push(`/news/detail/${newsId}`)
      alert(`点击了新闻: ${newsId}`)
    }

    onMounted(() => {
      AOS.init({
        duration: 400,
        once: true
      })
    })

    return {
      activeCategory,
      searchKeyword,
      allNews,
      filteredAndSearchedNews,
      displayedNews,
      totalFilteredItems,
      setCategory,
      searchNews,
      clearSearch,
      openNewsDetail,
      // 分页相关
      currentPage,
      itemsPerPage,
      totalItems: totalFilteredItems,
      totalPages: computed(() => Math.ceil(totalFilteredItems.value / itemsPerPage.value)),
      handlePageChange,
      handleItemsPerPageChange
    }
  }
}
</script>