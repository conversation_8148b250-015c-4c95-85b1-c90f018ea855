<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">智能电表</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">精准电量计量监控</p>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div>
      <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
    </section>

    <!-- 产品详情内容 -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- 左侧产品分类导航 -->
          <div class="lg:col-span-1">
            <ProductNavigation
              :current-category="'smart-meter'"
              :current-product="'electric-meter'"
            />
          </div>

          <!-- 右侧产品详情内容 -->
          <div class="lg:col-span-3">
            <div class="text-center py-20">
              <div class="max-w-md mx-auto">
                <div class="mb-8">
                  <i class="fas fa-bolt text-6xl text-gray-300 mb-4"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-600 mb-4">产品详情即将上线</h3>
                <p class="text-gray-500">智能电表详细信息正在完善中，敬请期待...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import AOS from 'aos'
import ProductNavigation from '@/components/ProductNavigation.vue'

export default {
  name: 'ElectricMeter',
  components: {
    ProductNavigation
  },
  setup() {
    const route = useRoute()
    const currentProduct = ref('electric-meter')

    onMounted(() => {
      AOS.init({
        duration: 1000,
        once: true
      })
    })

    return {
      currentProduct
    }
  }
}
</script>

<style>
/* 产品卡片样式 */
.product-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #f3f4f6;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.15);
  border-color: #3b82f6;
}

.product-card .product-image-container {
  position: relative;
  overflow: hidden;
}

.product-card:hover .product-image {
  transform: scale(1.1);
  filter: brightness(1.1) contrast(1.1);
}

.product-card .image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  color: white;
  padding: 15px;
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card .product-image-container:hover .image-overlay {
  transform: translateY(0);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-card {
    margin-bottom: 1.5rem;
  }

  .product-card:hover {
    transform: translateY(-5px);
  }
}
</style>