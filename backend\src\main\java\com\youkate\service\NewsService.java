package com.youkate.service;

import com.youkate.entity.News;
import com.youkate.repository.NewsRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 新闻服务类
 */
@Service
@RequiredArgsConstructor
@Transactional
public class NewsService {
    
    private final NewsRepository newsRepository;
    
    /**
     * 获取所有新闻
     */
    public List<News> getAllNews() {
        return newsRepository.findByIsPublishedTrueOrderByPublishedAtDesc();
    }
    
    /**
     * 根据ID获取新闻
     */
    public Optional<News> getNewsById(Long id) {
        Optional<News> news = newsRepository.findById(id);
        // 增加浏览次数
        news.ifPresent(n -> {
            n.setViewCount(n.getViewCount() + 1);
            newsRepository.save(n);
        });
        return news;
    }
    
    /**
     * 根据分类获取新闻
     */
    public List<News> getNewsByCategory(String category) {
        return newsRepository.findByCategoryAndIsPublishedTrueOrderByPublishedAtDesc(category);
    }
    
    /**
     * 搜索新闻
     */
    public List<News> searchNews(String keyword) {
        return newsRepository.findByContentContaining(keyword);
    }
    
    /**
     * 获取推荐新闻
     */
    public List<News> getFeaturedNews() {
        return newsRepository.findByIsFeaturedTrueAndIsPublishedTrueOrderByPublishedAtDesc();
    }
    
    /**
     * 获取最新新闻
     */
    public List<News> getLatestNews(int limit) {
        List<News> allNews = newsRepository.findLatestNews();
        return allNews.size() > limit ? allNews.subList(0, limit) : allNews;
    }
    
    /**
     * 创建新闻
     */
    public News createNews(News news) {
        return newsRepository.save(news);
    }
    
    /**
     * 更新新闻
     */
    public News updateNews(Long id, News newsDetails) {
        return newsRepository.findById(id)
            .map(news -> {
                news.setTitle(newsDetails.getTitle());
                news.setSummary(newsDetails.getSummary());
                news.setContent(newsDetails.getContent());
                news.setImageUrl(newsDetails.getImageUrl());
                news.setCategory(newsDetails.getCategory());
                news.setAuthor(newsDetails.getAuthor());
                news.setIsFeatured(newsDetails.getIsFeatured());
                news.setIsPublished(newsDetails.getIsPublished());
                news.setPublishedAt(newsDetails.getPublishedAt());
                return newsRepository.save(news);
            })
            .orElseThrow(() -> new RuntimeException("新闻不存在: " + id));
    }
    
    /**
     * 删除新闻
     */
    public void deleteNews(Long id) {
        newsRepository.deleteById(id);
    }
}
