<template>
  <footer class="bg-gray-900 text-white pt-16 pb-8">
    <div class="container-custom px-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-2 md:gap-3 mb-8">
        <!-- 公司信息 -->
        <div class="company-info-section">
          <!-- Logo区域 -->
          <div class="logo-container text-center mb-6">
            <router-link to="/" class="inline-block">
              <img src="/images/products/szjocat.jpg" alt="优卡特logo，白色版本用于深色背景，显示公司名称和标志性图形设计" class="mx-auto h-14 md:h-16 w-auto max-w-none object-contain hover:opacity-80 transition duration-300">
            </router-link>
          </div>

          <!-- 社交媒体区域 -->
          <div class="social-media-section">
            <h5 class="text-sm font-semibold text-gray-300 mb-6 text-center">关注我们</h5>
            <div class="example-2 flex justify-center items-center">
              <!-- 微信图标 -->
              <div class="icon-content mx-4 relative wechat-enhanced">
                <div class="tooltip-qr absolute -top-40 left-1/2 transform -translate-x-1/2 opacity-0 invisible transition-all duration-300 z-50">
                  <div class="bg-white rounded-lg shadow-xl p-3 border-2 border-green-500 max-w-none">
                    <img src="/images/products/jocat.jpg" alt="微信二维码" class="w-20 h-32 rounded object-contain bg-white"
                         onerror="console.log('微信图片加载失败:', this.src); this.src='https://placehold.co/80x80/22c55e/FFFFFF?text=微信二维码'"
                         onload="console.log('微信图片加载成功:', this.src)">
                  </div>
                </div>
                <a href="#" data-social="wechat" class="relative overflow-hidden flex justify-center items-center w-12 h-12 rounded-full text-gray-600 bg-white transition-all duration-300 hover:text-white hover:shadow-lg group">
                  <i class="fab fa-weixin text-2xl relative z-10"></i>
                  <div class="filled absolute bottom-0 left-0 w-full h-0 bg-green-500 transition-all duration-300"></div>
                </a>

                <!-- 微信二维码弹出框 -->
                <div class="qr-popup absolute left-full top-1/2 transform -translate-y-1/2 ml-6 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-40">
                  <div class="bg-white rounded-lg shadow-2xl p-4 border-2 border-green-500 relative min-w-max">
                    <!-- 左侧箭头 -->
                    <div class="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-white"></div>
                    <div class="absolute right-full top-1/2 transform -translate-y-1/2 mr-0.5 w-0 h-0 border-t-3 border-b-3 border-r-3 border-t-transparent border-b-transparent border-r-green-500"></div>

                    <!-- 内容 -->
                    <div class="text-center">
                      <img src="/images/products/jocat.jpg" alt="微信二维码" class="w-24 h-36 mx-auto mb-3 rounded object-contain bg-white"
                           onerror="console.log('微信大图片加载失败:', this.src); this.src='https://placehold.co/80x80/22c55e/FFFFFF?text=微信'"
                           onload="console.log('微信大图片加载成功:', this.src)">
                      <p class="text-gray-800 text-xs font-semibold whitespace-nowrap">扫码关注我们的微信</p>
                      <p class="text-gray-600 text-xs">@优卡特科技</p>
                    </div>

                    <!-- 装饰 -->
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-ping"></div>
                  </div>
                </div>
              </div>

              <!-- 微博图标 -->
              <div class="icon-content mx-4 relative weibo-enhanced">
                <div class="tooltip-qr absolute -top-40 left-1/2 transform -translate-x-1/2 opacity-0 invisible transition-all duration-300 z-50">
                  <div class="bg-white rounded-lg shadow-xl p-3 border-2 border-red-500 max-w-none">
                    <img src="/images/products/weibo.jpg" alt="微博二维码" class="w-20 h-32 rounded object-contain bg-white"
                         onerror="console.log('图片加载失败:', this.src); this.src='https://placehold.co/80x80/ef4444/FFFFFF?text=微博二维码'"
                         onload="console.log('图片加载成功:', this.src)">
                  </div>
                </div>
                <a href="#" data-social="weibo" class="relative overflow-hidden flex justify-center items-center w-12 h-12 rounded-full text-gray-600 bg-white transition-all duration-300 hover:text-white hover:shadow-lg group">
                  <i class="fab fa-weibo text-2xl relative z-10"></i>
                  <div class="filled absolute bottom-0 left-0 w-full h-0 bg-red-500 transition-all duration-300"></div>
                </a>

                <!-- 二维码弹出框 -->
                <div class="qr-popup absolute left-full top-1/2 transform -translate-y-1/2 ml-6 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-40">
                  <div class="bg-white rounded-lg shadow-2xl p-4 border-2 border-red-500 relative min-w-max">
                    <!-- 左侧箭头 -->
                    <div class="absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-white"></div>
                    <div class="absolute right-full top-1/2 transform -translate-y-1/2 mr-0.5 w-0 h-0 border-t-3 border-b-3 border-r-3 border-t-transparent border-b-transparent border-r-red-500"></div>

                    <!-- 内容 -->
                    <div class="text-center">
                      <img src="/images/products/weibo.jpg" alt="微博二维码" class="w-24 h-36 mx-auto mb-3 rounded object-contain bg-white"
                           onerror="console.log('大二维码加载失败:', this.src); this.src='https://placehold.co/80x80/FF6B6B/FFFFFF?text=微博'"
                           onload="console.log('大二维码加载成功:', this.src)">
                      <p class="text-gray-800 text-xs font-semibold whitespace-nowrap">扫码关注我们的微博</p>
                      <p class="text-gray-600 text-xs">@优卡特科技</p>
                    </div>

                    <!-- 装饰 -->
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速链接 -->
        <div>
          <h4 class="text-lg font-bold mb-4">快速链接</h4>
          <ul class="space-y-2">
            <li><router-link to="/" class="text-gray-400 hover:text-white transition duration-300">首页</router-link></li>
            <li><router-link to="/about" class="text-gray-400 hover:text-white transition duration-300">关于我们</router-link></li>
            <li><router-link to="/products" class="text-gray-400 hover:text-white transition duration-300">产品中心</router-link></li>
            <li><router-link to="/solutions" class="text-gray-400 hover:text-white transition duration-300">解决方案</router-link></li>
            <li><router-link to="/news" class="text-gray-400 hover:text-white transition duration-300">新闻中心</router-link></li>
            <li><router-link to="/contact" class="text-gray-400 hover:text-white transition duration-300">联系我们</router-link></li>
          </ul>
        </div>

        <!-- 产品中心 -->
        <div class="lg:col-span-2 relative">
          <h4 class="text-lg font-bold mb-4">产品中心</h4>
          <div class="relative h-auto">
            <!-- 左侧6个系列 -->
            <div class="absolute left-0 top-0">
              <ul class="space-y-2">
                <li><router-link to="/products/smart-consumer" class="text-gray-400 hover:text-white transition duration-300">智能消费机系列</router-link></li>
                <li><router-link to="/products/ai-platform" class="text-gray-400 hover:text-white transition duration-300">AI智能算台系列</router-link></li>
                <li><router-link to="/products/water-control" class="text-gray-400 hover:text-white transition duration-300">智能水控机系列</router-link></li>
                <li><router-link to="/products/electric-control" class="text-gray-400 hover:text-white transition duration-300">智能电控机系列</router-link></li>
                <li><router-link to="/products/smart-meter" class="text-gray-400 hover:text-white transition duration-300">智能水表/电表系列</router-link></li>
                <li><router-link to="/products/shared-washer" class="text-gray-400 hover:text-white transition duration-300">共享洗衣机系列</router-link></li>
              </ul>
            </div>
            <!-- 右侧6个系列 -->
            <div class="absolute left-48 top-0">
              <ul class="space-y-2">
                <li><router-link to="/products/shared-dryer" class="text-gray-400 hover:text-white transition duration-300">共享吹风机系列</router-link></li>
                <li><router-link to="/products/access-attendance" class="text-gray-400 hover:text-white transition duration-300">门禁/考勤系列</router-link></li>
                <li><router-link to="/products/shared-video-phone" class="text-gray-400 hover:text-white transition duration-300">共享视频电话机系列</router-link></li>
                <li><router-link to="/products/visitor-machine" class="text-gray-400 hover:text-white transition duration-300">访客机系列</router-link></li>
                <li><router-link to="/products/smart-self-service" class="text-gray-400 hover:text-white transition duration-300">智能自助机系列</router-link></li>
                <li><router-link to="/products/palm-vein" class="text-gray-400 hover:text-white transition duration-300">掌静脉系列</router-link></li>
              </ul>
            </div>
            <!-- 占位元素确保容器高度 -->
            <div class="invisible">
              <ul class="space-y-2">
                <li>占位</li>
                <li>占位</li>
                <li>占位</li>
                <li>占位</li>
                <li>占位</li>
                <li>占位</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 联系信息 -->
        <div>
          <h4 class="text-lg font-bold mb-4">联系信息</h4>
          <ul class="space-y-2">
            <li class="flex items-start">
              <i class="fas fa-map-marker-alt mt-1 mr-3 text-gray-400"></i>
              <span class="text-gray-400">深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</span>
            </li>
            <li class="flex items-start">
              <i class="fas fa-phone-alt mt-1 mr-3 text-gray-400"></i>
              <a href="tel:159-8667-2052" class="text-gray-400 hover:text-white transition duration-300">159-8667-2052</a>
            </li>
            <li class="flex items-start">
              <i class="fas fa-envelope mt-1 mr-3 text-gray-400"></i>
              <a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white transition duration-300"><EMAIL></a>
            </li>
            <li class="flex items-start">
              <i class="fas fa-clock mt-1 mr-3 text-gray-400"></i>
              <span class="text-gray-400">周一至周六: 8:55 - 18:00</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 友情链接区块 -->
      <div class="mt-12 pt-8 border-t border-gray-800">
        <h4 class="text-lg font-bold mb-6 text-center">友情链接</h4>
        <div class="flex flex-wrap justify-center items-center gap-x-6 gap-y-3">
          <a
            v-for="link in friendlyLinks"
            :key="link.id"
            :href="link.url"
            target="_blank"
            rel="noopener noreferrer"
            :title="link.description"
            class="friendly-link text-gray-400 hover:text-blue-400 transition-all duration-300 text-sm font-medium relative group"
          >
            {{ link.name }}
            <!-- 悬停下划线效果 -->
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-400 transition-all duration-300 group-hover:w-full"></span>
          </a>
        </div>
        <!-- 友情链接为空时的提示 -->
        <div v-if="friendlyLinks.length === 0" class="text-center text-gray-500 text-sm">
          暂无友情链接
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="border-t border-gray-800 pt-8 mt-8">
        <div class="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-4">
          <p class="text-gray-400 text-center">© 2025-2035 -人脸消费机- 深圳市优卡特实业有限公司 版权所有</p>
          <span class="text-gray-400 hidden md:inline">|</span>
          <a href="https://beian.miit.gov.cn/#/Integrated/index"
             target="_blank"
             rel="noopener noreferrer"
             class="text-gray-400 hover:text-white transition duration-300 text-center">
            粤ICP备20000796号
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
import { ref, onMounted } from 'vue'

export default {
  name: 'Footer',
  setup() {
    const friendlyLinks = ref([])

    // 获取友情链接数据
    const fetchFriendlyLinks = async () => {
      try {
        // 这里可以替换为实际的API调用
        // const response = await fetch('/api/friendly-links')
        // const data = await response.json()
        // friendlyLinks.value = data

        // 目前使用静态数据，后续可以替换为API调用
        friendlyLinks.value = [
          {
            id: 1,
            name: '智慧校园网',
            url: 'https://www.smartcampus.com',
            description: '专业的智慧校园解决方案提供商'
          },
          {
            id: 2,
            name: '人脸识别技术联盟',
            url: 'https://www.facetech.org',
            description: '人脸识别技术行业联盟官网'
          },
          {
            id: 3,
            name: '智能设备网',
            url: 'https://www.smartdevice.cn',
            description: '智能设备行业门户网站'
          },
          {
            id: 4,
            name: '一卡通系统网',
            url: 'https://www.cardtech.com',
            description: '一卡通系统技术交流平台'
          },
          {
            id: 5,
            name: '安防产业网',
            url: 'https://www.security.com.cn',
            description: '安防行业专业门户'
          },
          {
            id: 6,
            name: '物联网世界',
            url: 'https://www.iotworld.com.cn',
            description: '物联网技术与应用门户'
          }
        ]
      } catch (err) {
        console.error('获取友情链接失败:', err)
        friendlyLinks.value = []
      }
    }

    // 组件挂载时获取数据
    onMounted(() => {
      fetchFriendlyLinks()
    })

    return {
      friendlyLinks
    }
  }
}
</script>

<style scoped>
/* 公司信息区域样式 */
.company-info-section {
  max-width: 280px;
  margin: 0 auto;
}

.logo-container img {
  max-width: 200px;
  height: auto;
}

/* 社交媒体区域 */
.social-media-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Uiverse.io 样式实现 */
.example-2 {
  display: flex;
  justify-content: center;
  align-items: center;
}

.example-2 .icon-content {
  margin: 0 10px;
  position: relative;
}

.example-2 .icon-content .tooltip-qr {
  position: absolute;
  top: -160px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
}

.example-2 .icon-content:hover .tooltip-qr {
  opacity: 1;
  visibility: visible;
  top: -165px;
  transform: translateX(-50%) translateY(-5px);
}

/* 确保竖直图片容器有足够空间 */
.tooltip-qr .bg-white {
  min-width: fit-content;
  width: auto;
}

.tooltip-qr img {
  max-width: none;
  width: auto;
  height: auto;
  max-height: 128px;
}

/* 微信和微博tooltip都使用相同的样式 */
.wechat-enhanced .tooltip-qr {
  position: absolute;
  top: -160px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  white-space: nowrap;
}

.wechat-enhanced:hover .tooltip-qr {
  opacity: 1;
  visibility: visible;
  top: -165px;
  transform: translateX(-50%) translateY(-5px);
}

.example-2 .icon-content a {
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: #4d4d4d;
  background-color: #fff;
  transition: all 0.3s ease-in-out;
}

.example-2 .icon-content a:hover {
  box-shadow: 3px 2px 45px 0px rgb(0 0 0 / 12%);
  color: white;
}

.example-2 .icon-content a i {
  position: relative;
  z-index: 1;
  width: 30px;
  height: 30px;
}

.example-2 .icon-content a .filled {
  position: absolute;
  top: auto;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: #000;
  transition: all 0.3s ease-in-out;
}

.example-2 .icon-content a:hover .filled {
  height: 100%;
}

/* 微信样式 */
.example-2 .icon-content a[data-social="wechat"] .filled {
  background-color: #22c55e;
}

/* 微博样式 */
.example-2 .icon-content a[data-social="weibo"] .filled {
  background-color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  /* 在中等屏幕上，二维码向上显示 */
  .weibo-enhanced .qr-popup,
  .wechat-enhanced .qr-popup {
    left: 50%;
    top: auto;
    bottom: 100%;
    transform: translateX(-50%);
    margin-left: 0;
    margin-bottom: 1rem;
  }

  .weibo-enhanced .qr-popup .absolute.right-full,
  .wechat-enhanced .qr-popup .absolute.right-full {
    right: auto;
    left: 50%;
    top: 100%;
    bottom: auto;
    transform: translateX(-50%);
    border-right: none;
    border-top: none;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid white;
  }

  .weibo-enhanced .qr-popup .absolute.right-full + .absolute {
    right: auto;
    left: 50%;
    top: 100%;
    bottom: auto;
    transform: translateX(-50%);
    margin-right: 0;
    margin-top: 0.5px;
    border-right: none;
    border-top: none;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-bottom: 3px solid #ef4444;
  }

  .wechat-enhanced .qr-popup .absolute.right-full + .absolute {
    right: auto;
    left: 50%;
    top: 100%;
    bottom: auto;
    transform: translateX(-50%);
    margin-right: 0;
    margin-top: 0.5px;
    border-right: none;
    border-top: none;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-bottom: 3px solid #22c55e;
  }
}

@media (max-width: 768px) {
  .company-info-section {
    max-width: 100%;
  }

  .example-2 {
    flex-direction: column;
    gap: 1rem;
  }

  .example-2 .icon-content {
    margin: 0;
  }

  /* 移动端二维码向右显示 */
  .weibo-enhanced .qr-popup {
    left: 100%;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);
    margin-left: 1rem;
    margin-bottom: 0;
  }

  .wechat-enhanced .qr-popup {
    right: 100%;
    left: auto;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);
    margin-right: 1rem;
    margin-left: 0;
    margin-bottom: 0;
  }

  /* 微信tooltip在移动端的位置调整 */
  .wechat-enhanced .tooltip-qr {
    top: -140px;
  }

  .wechat-enhanced:hover .tooltip-qr {
    top: -145px;
  }

  .weibo-enhanced .qr-popup .absolute.right-full {
    right: 100%;
    left: auto;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);
    border-left: none;
    border-bottom: none;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-right: 4px solid white;
  }

  .wechat-enhanced .qr-popup .absolute.right-full {
    left: 100%;
    right: auto;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);
    border-right: none;
    border-bottom: none;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 4px solid white;
  }

  .weibo-enhanced .qr-popup .absolute.right-full + .absolute {
    right: 100%;
    left: auto;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);
    margin-right: 0.5px;
    margin-top: 0;
    border-left: none;
    border-bottom: none;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-right: 3px solid #ef4444;
  }

  .wechat-enhanced .qr-popup .absolute.right-full + .absolute {
    left: 100%;
    right: auto;
    top: 50%;
    bottom: auto;
    transform: translateY(-50%);
    margin-left: 0.5px;
    margin-top: 0;
    border-right: none;
    border-bottom: none;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-left: 3px solid #22c55e;
  }
}

@media (max-width: 480px) {
  .social-media-section {
    padding: 16px;
  }

  .logo-container img {
    max-width: 160px;
  }

  .example-2 .icon-content a {
    width: 40px;
    height: 40px;
  }

  .qr-popup .bg-white {
    padding: 0.75rem;
  }

  .qr-popup img {
    width: 4rem;
    height: 4rem;
  }

  /* Logo样式优化 */
  .logo-container img {
    max-height: 3.5rem;
  }
}

/* 友情链接样式 */
.friendly-link {
  position: relative;
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
}

.friendly-link:hover {
  color: #60a5fa !important;
  transform: translateY(-1px);
  text-shadow: 0 0 8px rgba(96, 165, 250, 0.3);
}

.friendly-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(96, 165, 250, 0.1), transparent);
  border-radius: 0.25rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.friendly-link:hover::before {
  opacity: 1;
}

/* 响应式友情链接 */
@media (max-width: 768px) {
  .friendly-link {
    font-size: 0.875rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 480px) {
  .friendly-link {
    font-size: 0.8rem;
    padding: 0.15rem 0.3rem;
  }
}
</style>
