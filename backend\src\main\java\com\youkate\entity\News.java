package com.youkate.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 新闻实体类
 */
@Entity
@Table(name = "news")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class News {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "新闻标题不能为空")
    @Column(nullable = false, length = 200)
    private String title;
    
    @Column(length = 500)
    private String summary; // 新闻摘要
    
    @NotBlank(message = "新闻内容不能为空")
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;
    
    @Column(name = "image_url", length = 500)
    private String imageUrl;
    
    @NotBlank(message = "新闻分类不能为空")
    @Column(nullable = false, length = 50)
    private String category; // company, industry, support
    
    @Column(length = 100)
    private String author = "优卡特"; // 作者
    
    @Column(name = "view_count")
    private Integer viewCount = 0; // 浏览次数
    
    @Column(name = "is_featured")
    private Boolean isFeatured = false; // 是否推荐
    
    @Column(name = "is_published")
    private Boolean isPublished = true; // 是否发布
    
    @Column(name = "published_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishedAt;
    
    @Column(name = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (publishedAt == null) {
            publishedAt = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
