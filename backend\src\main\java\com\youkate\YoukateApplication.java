package com.youkate;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.CrossOrigin;

/**
 * 优卡特官网后端应用主类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:8081"})
public class YoukateApplication {

    public static void main(String[] args) {
        SpringApplication.run(YoukateApplication.class, args);
        System.out.println("=================================");
        System.out.println("优卡特官网后端服务启动成功！");
        System.out.println("API地址: http://localhost:8080/api");
        System.out.println("H2控制台: http://localhost:8080/api/h2-console");
        System.out.println("=================================");
    }
}
