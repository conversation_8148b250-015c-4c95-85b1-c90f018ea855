package com.youkate.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 团队成员数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamMemberDto {
    
    /**
     * 成员ID
     */
    private Long id;
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 职位
     */
    private String position;
    
    /**
     * 部门
     */
    private String department;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 工作经验（年）
     */
    private Integer experience;
    
    /**
     * 专业技能
     */
    private List<String> skills;
    
    /**
     * 教育背景
     */
    private String education;
    
    /**
     * 联系邮箱
     */
    private String email;
    
    /**
     * LinkedIn链接
     */
    private String linkedinUrl;
    
    /**
     * 微信号
     */
    private String wechat;
    
    /**
     * 是否显示在团队页面
     */
    private Boolean isVisible;
    
    /**
     * 排序顺序
     */
    private Integer sortOrder;
    
    /**
     * 加入公司时间
     */
    private String joinDate;
    
    /**
     * 主要成就
     */
    private List<String> achievements;
}
