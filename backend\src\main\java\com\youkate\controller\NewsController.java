package com.youkate.controller;

import com.youkate.entity.News;
import com.youkate.service.NewsService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 新闻控制器
 */
@RestController
@RequestMapping("/news")
@RequiredArgsConstructor
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:8081"})
public class NewsController {
    
    private final NewsService newsService;
    
    /**
     * 获取所有新闻
     */
    @GetMapping
    public ResponseEntity<List<News>> getAllNews() {
        List<News> news = newsService.getAllNews();
        return ResponseEntity.ok(news);
    }
    
    /**
     * 根据ID获取新闻
     */
    @GetMapping("/{id}")
    public ResponseEntity<News> getNewsById(@PathVariable Long id) {
        return newsService.getNewsById(id)
            .map(ResponseEntity::ok)
            .orElse(ResponseEntity.notFound().build());
    }
    
    /**
     * 根据分类获取新闻
     */
    @GetMapping("/category/{category}")
    public ResponseEntity<List<News>> getNewsByCategory(@PathVariable String category) {
        List<News> news = newsService.getNewsByCategory(category);
        return ResponseEntity.ok(news);
    }
    
    /**
     * 搜索新闻
     */
    @GetMapping("/search")
    public ResponseEntity<List<News>> searchNews(@RequestParam String keyword) {
        List<News> news = newsService.searchNews(keyword);
        return ResponseEntity.ok(news);
    }
    
    /**
     * 获取推荐新闻
     */
    @GetMapping("/featured")
    public ResponseEntity<List<News>> getFeaturedNews() {
        List<News> news = newsService.getFeaturedNews();
        return ResponseEntity.ok(news);
    }
    
    /**
     * 获取最新新闻
     */
    @GetMapping("/latest")
    public ResponseEntity<List<News>> getLatestNews(@RequestParam(defaultValue = "6") int limit) {
        List<News> news = newsService.getLatestNews(limit);
        return ResponseEntity.ok(news);
    }
    
    /**
     * 创建新闻
     */
    @PostMapping
    public ResponseEntity<News> createNews(@Valid @RequestBody News news) {
        News createdNews = newsService.createNews(news);
        return ResponseEntity.ok(createdNews);
    }
    
    /**
     * 更新新闻
     */
    @PutMapping("/{id}")
    public ResponseEntity<News> updateNews(@PathVariable Long id, @Valid @RequestBody News news) {
        try {
            News updatedNews = newsService.updateNews(id, news);
            return ResponseEntity.ok(updatedNews);
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 删除新闻
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteNews(@PathVariable Long id) {
        try {
            newsService.deleteNews(id);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }
}
