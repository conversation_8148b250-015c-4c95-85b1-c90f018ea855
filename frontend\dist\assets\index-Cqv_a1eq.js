const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProductDetail-Bi-_IzTU.js","assets/vendor-qI2TI6m6.js","assets/utils-D81Uom5a.js","assets/ProductDetail-BhFvBYyB.css"])))=>i.map(i=>d[i]);
import{d as $t,c as p,a as e,b as f,F as R,r as W,e as n,w as c,f as $,g as m,o as F,u as bt,h as u,n as M,i,t as v,j as D,v as U,k as kt,l as A,m as h,p as dt,q as Q,s as T,x as G,y as H,z as X,A as Z,B as Ct,C as Pt,D as jt,E as ht,T as pt,G as Ft,H as It}from"./vendor-qI2TI6m6.js";import{a as Nt,A as N}from"./utils-D81Uom5a.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const g of document.querySelectorAll('link[rel="modulepreload"]'))a(g);new MutationObserver(g=>{for(const x of g)if(x.type==="childList")for(const s of x.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&a(s)}).observe(document,{childList:!0,subtree:!0});function r(g){const x={};return g.integrity&&(x.integrity=g.integrity),g.referrerPolicy&&(x.referrerPolicy=g.referrerPolicy),g.crossOrigin==="use-credentials"?x.credentials="include":g.crossOrigin==="anonymous"?x.credentials="omit":x.credentials="same-origin",x}function a(g){if(g.ep)return;g.ep=!0;const x=r(g);fetch(g.href,x)}})();const St="modulepreload",At=function(o){return"/"+o},xt={},Mt=function(t,r,a){let g=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),l=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));g=Promise.allSettled(r.map(d=>{if(d=At(d),d in xt)return;xt[d]=!0;const w=d.endsWith(".css"),j=w?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${d}"]${j}`))return;const C=document.createElement("link");if(C.rel=w?"stylesheet":St,w||(C.as="script"),C.crossOrigin="",C.href=d,l&&C.setAttribute("nonce",l),document.head.appendChild(C),w)return new Promise((y,K)=>{C.addEventListener("load",y),C.addEventListener("error",()=>K(new Error(`Unable to preload CSS for ${d}`)))})}))}function x(s){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=s,window.dispatchEvent(l),!l.defaultPrevented)throw s}return g.then(s=>{for(const l of s||[])l.status==="rejected"&&x(l.reason);return t().catch(x)})},V=Nt.create({baseURL:"/api",timeout:1e4,headers:{"Content-Type":"application/json"}});V.interceptors.request.use(o=>o,o=>Promise.reject(o));V.interceptors.response.use(o=>o.data,o=>Promise.reject(o));const J={getAll:()=>V.get("/news"),getById:o=>V.get(`/news/${o}`),getByCategory:o=>V.get(`/news/category/${o}`),search:o=>V.get(`/news/search?keyword=${o}`),getFeatured:()=>V.get("/news/featured"),getLatest:(o=6)=>V.get(`/news/latest?limit=${o}`),create:o=>V.post("/news",o),update:(o,t)=>V.put(`/news/${o}`,t),delete:o=>V.delete(`/news/${o}`)},yt=$t("news",{state:()=>({news:[],currentNews:null,featuredNews:[],latestNews:[],loading:!1,error:null}),getters:{getNewsByCategory:o=>t=>o.news.filter(r=>r.category===t),companyNews:o=>o.news.filter(t=>t.category==="company"),industryNews:o=>o.news.filter(t=>t.category==="industry"),supportNews:o=>o.news.filter(t=>t.category==="support"),totalNews:o=>o.news.length,hasNews:o=>o.news.length>0},actions:{async fetchNews(){this.loading=!0,this.error=null;try{const o=await J.getAll();this.news=o}catch(o){this.error="获取新闻列表失败",console.error("获取新闻失败:",o)}finally{this.loading=!1}},async fetchNewsById(o){this.loading=!0,this.error=null;try{const t=await J.getById(o);return this.currentNews=t,t}catch(t){return this.error="获取新闻详情失败",console.error("获取新闻详情失败:",t),null}finally{this.loading=!1}},async fetchNewsByCategory(o){this.loading=!0,this.error=null;try{return await J.getByCategory(o)}catch(t){return this.error="获取分类新闻失败",console.error("获取分类新闻失败:",t),[]}finally{this.loading=!1}},async searchNews(o){this.loading=!0,this.error=null;try{return await J.search(o)}catch(t){return this.error="搜索新闻失败",console.error("搜索新闻失败:",t),[]}finally{this.loading=!1}},async fetchFeaturedNews(){this.loading=!0,this.error=null;try{const o=await J.getFeatured();this.featuredNews=o}catch(o){this.error="获取推荐新闻失败",console.error("获取推荐新闻失败:",o)}finally{this.loading=!1}},async fetchLatestNews(o=6){this.loading=!0,this.error=null;try{const t=await J.getLatest(o);this.latestNews=t}catch(t){this.error="获取最新新闻失败",console.error("获取最新新闻失败:",t)}finally{this.loading=!1}},async createNews(o){this.loading=!0,this.error=null;try{const t=await J.create(o);return this.news.unshift(t),t}catch(t){return this.error="创建新闻失败",console.error("创建新闻失败:",t),null}finally{this.loading=!1}},async updateNews(o,t){this.loading=!0,this.error=null;try{const r=await J.update(o,t),a=this.news.findIndex(g=>g.id===o);return a!==-1&&(this.news[a]=r),r}catch(r){return this.error="更新新闻失败",console.error("更新新闻失败:",r),null}finally{this.loading=!1}},async deleteNews(o){this.loading=!0,this.error=null;try{return await J.delete(o),this.news=this.news.filter(t=>t.id!==o),!0}catch(t){return this.error="删除新闻失败",console.error("删除新闻失败:",t),!1}finally{this.loading=!1}},clearError(){this.error=null},setCurrentNews(o){this.currentNews=o}}}),_=(o,t)=>{const r=o.__vccOpts||o;for(const[a,g]of t)r[a]=g;return r},Kt={name:"Home",setup(){const o=bt(),t=yt(),r=m(0),a=m([{id:1,title:"智能一卡通解决方案",subtitle:"为您的企业、学校提供全方位智能管理服务",link:""},{id:2,title:"人脸识别消费终端",subtitle:"科技创新，让消费更便捷、更安全",link:""},{id:3,title:"企业智能管理系统",subtitle:"提升企业管理效率，打造智慧办公环境",link:""}]),g=m(null),x=m(!1),s=m([]),l=()=>{if(g.value){const S=-r.value*(100/a.value.length);g.value.style.transform=`translateX(${S}%)`}},d=()=>{r.value=(r.value+1)%a.value.length,l()},w=()=>{r.value=(r.value-1+a.value.length)%a.value.length,l()},j=S=>{r.value=S,l()},C=()=>{setInterval(d,5e3)},y=S=>{const Y=a.value[S];Y&&Y.link&&(Y.link.startsWith("http")?window.open(Y.link,"_blank"):o.push(Y.link))},K=S=>{o.push(`/products/${S}`)},k=S=>{o.push(`/news/${S}`)},b=S=>{console.log("跳转到解决方案:",S);const P={enterprise:"/solutions/enterprise-park",campus:"/solutions/smart-campus",healthcare:"/solutions/medical-institution"}[S];P?o.push(P):console.warn("未找到对应的解决方案路由:",S)},z=S=>S?new Date(S).toLocaleDateString("zh-CN"):"",E=()=>{const S=document.querySelectorAll(".stats-number"),Y=(I,B,L,q,st="")=>{let ot=null;const lt=rt=>{ot||(ot=rt);const it=Math.min((rt-ot)/q,1),ct=1-Math.pow(1-it,4),ut=Math.floor(ct*(L-B)+B);I.textContent=ut.toLocaleString()+st,it<1?window.requestAnimationFrame(lt):I.textContent=L.toLocaleString()+st};window.requestAnimationFrame(lt)},P=new IntersectionObserver(I=>{I.forEach(B=>{if(B.isIntersecting){const L=B.target,q=L.dataset.target;q==="2003"?Y(L,1990,2003,2e3):q==="20+"?Y(L,0,20,1500,"+"):q==="10000+"?Y(L,0,1e4,2500,"+"):q==="50+"&&Y(L,0,50,1800,"+"),P.unobserve(L)}})},{threshold:.3});S.forEach(I=>{P.observe(I)})},at=async()=>{x.value=!0;try{if(t.fetchLatestNews)if(await t.fetchLatestNews(6),t.latestNews&&t.latestNews.length>0)s.value=t.latestNews;else throw new Error("No news data from store");else throw new Error("News store not available")}catch{s.value=[{id:1,title:"优卡特智能一卡通系统助力智慧校园建设",summary:"我公司最新推出的智能一卡通系统在多所高校成功部署，为校园数字化转型提供强有力支持。",category:"公司新闻",publishDate:new Date().toISOString(),views:156,imageUrl:"https://placehold.co/400x250/0066CC/FFFFFF?text=智慧校园"},{id:2,title:"人脸识别技术在消费场景中的创新应用",summary:"随着人工智能技术的发展，人脸识别在消费领域的应用越来越广泛，为用户带来更便捷的体验。",category:"行业动态",publishDate:new Date(Date.now()-864e5).toISOString(),views:89,imageUrl:"https://placehold.co/400x250/0088FF/FFFFFF?text=人脸识别"},{id:3,title:"2024年智能终端设备市场趋势分析",summary:"本文深入分析了智能终端设备市场的发展趋势，为行业发展提供参考。",category:"技术支持",publishDate:new Date(Date.now()-1728e5).toISOString(),views:234,imageUrl:"https://placehold.co/400x250/00AAFF/FFFFFF?text=市场趋势"}]}finally{x.value=!1}};return F(()=>{N.init({duration:400,once:!0}),l(),C(),at(),setTimeout(()=>{E()},500)}),{currentSlide:r,slides:a,carouselInner:g,nextSlide:d,prevSlide:w,goToSlide:j,handleSlideClick:y,goToProduct:K,goToNews:k,goToSolution:b,formatDate:z,newsLoading:x,latestNews:s}}},Tt={class:"home"},Dt={class:"carousel-fullscreen"},zt={class:"carousel-inner",ref:"carouselInner"},Rt={class:"carousel-indicators"},Wt=["onClick"],Et={class:"py-20 bg-white"},Lt={class:"container mx-auto px-4"},Yt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Bt={class:"text-center mt-12","data-aos":"fade-up"},qt={class:"py-20 bg-gray-50"},Gt={class:"container mx-auto px-4"},Ht={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Ot={class:"text-center mt-12","data-aos":"fade-up"},Ut={class:"py-20 bg-white"},Vt={class:"container mx-auto px-4"},Qt={key:0,class:"flex justify-center"},Xt={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Jt=["onClick"],Zt={class:"news-image-container"},te=["src","alt"],ee={class:"news-category"},ae={class:"news-content"},se={class:"news-title"},oe={class:"news-summary"},le={class:"news-meta"},de={class:"news-date"},re={class:"text-center mt-12","data-aos":"fade-up"};function ie(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",Tt,[e("div",Dt,[e("div",zt,[e("div",{class:"carousel-item",onClick:t[0]||(t[0]=l=>a.handleSlideClick(0))},t[11]||(t[11]=[e("img",{src:"https://placehold.co/1920x1080/0066CC/FFFFFF?text=智能一卡通解决方案",alt:"智能一卡通解决方案"},null,-1),e("div",{class:"carousel-caption"},[e("h2",{class:"text-3xl md:text-5xl lg:text-6xl font-bold mb-4"},"智能一卡通解决方案"),e("p",{class:"text-xl md:text-2xl"},"为您的企业、学校提供全方位智能管理服务")],-1)])),e("div",{class:"carousel-item",onClick:t[1]||(t[1]=l=>a.handleSlideClick(1))},t[12]||(t[12]=[e("img",{src:"https://placehold.co/1920x1080/0088FF/FFFFFF?text=人脸识别消费终端",alt:"人脸识别消费终端"},null,-1),e("div",{class:"carousel-caption"},[e("h2",{class:"text-3xl md:text-5xl lg:text-6xl font-bold mb-4"},"人脸识别消费终端"),e("p",{class:"text-xl md:text-2xl"},"科技创新，让消费更便捷、更安全")],-1)])),e("div",{class:"carousel-item",onClick:t[2]||(t[2]=l=>a.handleSlideClick(2))},t[13]||(t[13]=[e("img",{src:"https://placehold.co/1920x1080/00AAFF/FFFFFF?text=企业智能管理系统",alt:"企业智能管理系统"},null,-1),e("div",{class:"carousel-caption"},[e("h2",{class:"text-3xl md:text-5xl lg:text-6xl font-bold mb-4"},"企业智能管理系统"),e("p",{class:"text-xl md:text-2xl"},"提升企业管理效率，打造智慧办公环境")],-1)]))],512),e("button",{class:"carousel-control prev",onClick:t[3]||(t[3]=(...l)=>a.prevSlide&&a.prevSlide(...l))},t[14]||(t[14]=[e("i",{class:"fas fa-chevron-left"},null,-1)])),e("button",{class:"carousel-control next",onClick:t[4]||(t[4]=(...l)=>a.nextSlide&&a.nextSlide(...l))},t[15]||(t[15]=[e("i",{class:"fas fa-chevron-right"},null,-1)])),e("div",Rt,[(u(!0),p(R,null,W(a.slides,(l,d)=>(u(),p("button",{key:d,class:M(["carousel-indicator",{active:a.currentSlide===d}]),onClick:w=>a.goToSlide(d)},null,10,Wt))),128))])]),t[30]||(t[30]=f('<section class="py-20 bg-white" data-v-9c182adc><div class="container mx-auto px-4" data-v-9c182adc><div class="text-center mb-16" data-aos="fade-up" data-v-9c182adc><h2 class="text-4xl md:text-5xl font-bold mb-6 text-gray-800" data-v-9c182adc>中国领先的一卡通科技公司</h2></div><div class="max-w-6xl mx-auto" data-v-9c182adc><div class="text-center mb-16" data-aos="fade-up" data-aos-delay="200" data-v-9c182adc><p class="text-lg md:text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto" data-v-9c182adc> 深圳市优卡特实业有限公司成立于2003年，是一家专注于智能一卡通系统解决方案的高新技术企业。公司总部位于中国广东深圳，以智能一卡通消费终端设备为核心，集设计、研发、生产、销售、服务于一体。经过20年的发展，优卡特已成为中国一卡通行业的领军企业，为超过10000家企事业单位、学校提供了专业的智能管理解决方案，帮助客户实现数字化转型升级。 </p></div><div class="grid grid-cols-2 md:grid-cols-4 gap-8" data-aos="fade-up" data-aos-delay="400" data-v-9c182adc><div class="text-center stats-card" data-v-9c182adc><div class="stats-number" data-target="2003" data-v-9c182adc>1990</div><div class="stats-label" data-v-9c182adc>成立年份</div></div><div class="text-center stats-card" data-v-9c182adc><div class="stats-number" data-target="20+" data-v-9c182adc>0+</div><div class="stats-label" data-v-9c182adc>年行业经验</div></div><div class="text-center stats-card" data-v-9c182adc><div class="stats-number" data-target="10000+" data-v-9c182adc>0+</div><div class="stats-label" data-v-9c182adc>服务客户</div></div><div class="text-center stats-card" data-v-9c182adc><div class="stats-number" data-target="50+" data-v-9c182adc>0+</div><div class="stats-label" data-v-9c182adc>技术专利</div></div></div></div></div></section>',1)),e("section",Et,[e("div",Lt,[t[20]||(t[20]=e("div",{class:"text-center mb-16","data-aos":"fade-up"},[e("h2",{class:"text-4xl md:text-5xl font-bold mb-4 text-gray-800"},"解决方案"),e("p",{class:"text-xl text-gray-600 max-w-3xl mx-auto"}," 为不同行业提供专业的智能一卡通解决方案 ")],-1)),e("div",Yt,[e("div",{class:"product-card","data-aos":"fade-up",onClick:t[5]||(t[5]=l=>a.goToSolution("enterprise"))},t[16]||(t[16]=[f('<div class="product-image-container" data-v-9c182adc><img src="https://placehold.co/400x300/0066CC/FFFFFF?text=企业园区解决方案" alt="企业园区解决方案" class="product-image" data-v-9c182adc></div><div class="product-content" data-v-9c182adc><div class="product-badge" data-v-9c182adc>热门</div><h3 class="product-title" data-v-9c182adc>企业园区解决方案</h3><p class="product-description" data-v-9c182adc>为企业园区提供员工考勤、门禁管理、食堂消费等一体化智能管理服务。提升企业管理效率，降低运营成本。</p><div class="product-features" data-v-9c182adc><span class="feature-tag" data-v-9c182adc>员工管理</span><span class="feature-tag" data-v-9c182adc>门禁控制</span><span class="feature-tag" data-v-9c182adc>消费管理</span></div><div class="product-footer" data-v-9c182adc><button class="product-btn" data-v-9c182adc>了解更多</button></div></div>',2)])),e("div",{class:"product-card","data-aos":"fade-up","data-aos-delay":"200",onClick:t[6]||(t[6]=l=>a.goToSolution("campus"))},t[17]||(t[17]=[f('<div class="product-image-container" data-v-9c182adc><img src="https://placehold.co/400x300/0088FF/FFFFFF?text=智慧校园解决方案" alt="智慧校园解决方案" class="product-image" data-v-9c182adc></div><div class="product-content" data-v-9c182adc><div class="product-badge" data-v-9c182adc>推荐</div><h3 class="product-title" data-v-9c182adc>智慧校园解决方案</h3><p class="product-description" data-v-9c182adc>为学校提供学生考勤、图书借阅、食堂消费、宿舍管理等全方位校园智能化服务。打造安全便捷的校园环境。</p><div class="product-features" data-v-9c182adc><span class="feature-tag" data-v-9c182adc>学生管理</span><span class="feature-tag" data-v-9c182adc>图书系统</span><span class="feature-tag" data-v-9c182adc>校园安全</span></div><div class="product-footer" data-v-9c182adc><button class="product-btn" data-v-9c182adc>了解更多</button></div></div>',2)])),e("div",{class:"product-card","data-aos":"fade-up","data-aos-delay":"400",onClick:t[7]||(t[7]=l=>a.goToSolution("healthcare"))},t[18]||(t[18]=[f('<div class="product-image-container" data-v-9c182adc><img src="https://placehold.co/400x300/00AAFF/FFFFFF?text=医疗机构解决方案" alt="医疗机构解决方案" class="product-image" data-v-9c182adc></div><div class="product-content" data-v-9c182adc><div class="product-badge" data-v-9c182adc>专业</div><h3 class="product-title" data-v-9c182adc>医疗机构解决方案</h3><p class="product-description" data-v-9c182adc>为医院提供患者身份识别、医疗费用结算、药品管理等智能化医疗服务。提升医疗服务质量和效率。</p><div class="product-features" data-v-9c182adc><span class="feature-tag" data-v-9c182adc>患者识别</span><span class="feature-tag" data-v-9c182adc>费用结算</span><span class="feature-tag" data-v-9c182adc>药品管理</span></div><div class="product-footer" data-v-9c182adc><button class="product-btn" data-v-9c182adc>了解更多</button></div></div>',2)]))]),e("div",Bt,[n(s,{to:"/solutions",class:"btn-primary"},{default:c(()=>t[19]||(t[19]=[i(" 查看全部解决方案 "),e("i",{class:"fas fa-arrow-right ml-2"},null,-1)])),_:1,__:[19]})])])]),e("section",qt,[e("div",Gt,[t[25]||(t[25]=e("div",{class:"text-center mb-16","data-aos":"fade-up"},[e("h2",{class:"text-4xl md:text-5xl font-bold mb-4 text-gray-800"},"核心产品"),e("p",{class:"text-xl text-gray-600 max-w-3xl mx-auto"}," 我们提供全系列智能一卡通设备，满足不同场景的应用需求 ")],-1)),e("div",Ht,[e("div",{class:"product-card","data-aos":"fade-up",onClick:t[8]||(t[8]=l=>a.goToProduct("android-face"))},t[21]||(t[21]=[f('<div class="product-image-container" data-v-9c182adc><img src="https://placehold.co/400x300/FFFFFF/333333?text=安卓人脸消费机" alt="安卓人脸消费机" class="product-image" data-v-9c182adc></div><div class="product-content" data-v-9c182adc><div class="product-badge" data-v-9c182adc>热销</div><h3 class="product-title" data-v-9c182adc>安卓人脸消费机</h3><p class="product-description" data-v-9c182adc>采用先进的人脸识别技术，支持离线识别，识别速度快，准确率高。搭载安卓操作系统，界面友好，操作简单。</p><div class="product-features" data-v-9c182adc><span class="feature-tag" data-v-9c182adc>人脸识别</span><span class="feature-tag" data-v-9c182adc>安卓系统</span><span class="feature-tag" data-v-9c182adc>离线识别</span></div><div class="product-footer" data-v-9c182adc><button class="product-btn" data-v-9c182adc>了解更多</button></div></div>',2)])),e("div",{class:"product-card","data-aos":"fade-up","data-aos-delay":"200",onClick:t[9]||(t[9]=l=>a.goToProduct("arm-terminal"))},t[22]||(t[22]=[f('<div class="product-image-container" data-v-9c182adc><img src="https://placehold.co/400x300/FFFFFF/333333?text=ARM消费机" alt="ARM消费机" class="product-image" data-v-9c182adc></div><div class="product-content" data-v-9c182adc><div class="product-badge" data-v-9c182adc>推荐</div><h3 class="product-title" data-v-9c182adc>ARM消费机</h3><p class="product-description" data-v-9c182adc>基于ARM架构的高性能消费终端，支持多种支付方式，稳定可靠，适用于各种消费场景。</p><div class="product-features" data-v-9c182adc><span class="feature-tag" data-v-9c182adc>ARM架构</span><span class="feature-tag" data-v-9c182adc>多种支付</span><span class="feature-tag" data-v-9c182adc>稳定可靠</span></div><div class="product-footer" data-v-9c182adc><button class="product-btn" data-v-9c182adc>了解更多</button></div></div>',2)])),e("div",{class:"product-card","data-aos":"fade-up","data-aos-delay":"400",onClick:t[10]||(t[10]=l=>a.goToProduct("face-temp-gate"))},t[23]||(t[23]=[f('<div class="product-image-container" data-v-9c182adc><img src="https://placehold.co/400x300/FFFFFF/333333?text=人脸测温通道闸机" alt="人脸测温通道闸机" class="product-image" data-v-9c182adc></div><div class="product-content" data-v-9c182adc><div class="product-badge" data-v-9c182adc>新品</div><h3 class="product-title" data-v-9c182adc>人脸测温通道闸机</h3><p class="product-description" data-v-9c182adc>集成人脸识别和体温检测功能的智能通道闸机，适用于学校、企业、医院等场所的安全管理。</p><div class="product-features" data-v-9c182adc><span class="feature-tag" data-v-9c182adc>人脸识别</span><span class="feature-tag" data-v-9c182adc>体温检测</span><span class="feature-tag" data-v-9c182adc>通道管理</span></div><div class="product-footer" data-v-9c182adc><button class="product-btn" data-v-9c182adc>了解更多</button></div></div>',2)]))]),e("div",Ot,[n(s,{to:"/products",class:"btn-primary"},{default:c(()=>t[24]||(t[24]=[i(" 查看全部产品 "),e("i",{class:"fas fa-arrow-right ml-2"},null,-1)])),_:1,__:[24]})])])]),e("section",Ut,[e("div",Vt,[t[29]||(t[29]=e("div",{class:"text-center mb-16","data-aos":"fade-up"},[e("h2",{class:"text-4xl md:text-5xl font-bold mb-4 text-gray-800"},"新闻资讯"),e("p",{class:"text-xl text-gray-600 max-w-3xl mx-auto"}," 了解行业动态，掌握最新技术趋势 ")],-1)),a.newsLoading?(u(),p("div",Qt,t[26]||(t[26]=[e("div",{class:"loading-spinner"},null,-1)]))):(u(),p("div",Xt,[(u(!0),p(R,null,W(a.latestNews,l=>{var d;return u(),p("div",{key:l.id,class:"news-card","data-aos":"fade-up",onClick:w=>a.goToNews(l.id)},[e("div",Zt,[e("img",{src:l.imageUrl||"https://placehold.co/400x250/0066CC/FFFFFF?text=新闻图片",alt:l.title,class:"news-image"},null,8,te),e("div",ee,v(l.category||"公司新闻"),1)]),e("div",ae,[e("h3",se,v(l.title),1),e("p",oe,v(l.summary||((d=l.content)==null?void 0:d.substring(0,100))+"..."),1),e("div",le,[e("span",de,[t[27]||(t[27]=e("i",{class:"fas fa-calendar mr-1"},null,-1)),i(" "+v(a.formatDate(l.publishDate)),1)])])])],8,Jt)}),128))])),e("div",re,[n(s,{to:"/news",class:"btn-primary"},{default:c(()=>t[28]||(t[28]=[i(" 查看更多新闻 "),e("i",{class:"fas fa-arrow-right ml-2"},null,-1)])),_:1,__:[28]})])])])])}const ne=_(Kt,[["render",ie],["__scopeId","data-v-9c182adc"]]),ce={name:"AboutMain",setup(){const o=m(null),t=m({foundYear:2003,experience:0,customers:0,patents:0}),r=()=>{const a=new IntersectionObserver(g=>{g.forEach(x=>{if(x.isIntersecting){let s=0,l=0,d=0;const w=setInterval(()=>{s+=20/50,s>=20&&(s=20,clearInterval(w)),t.value.experience=Math.floor(s)},30),j=setInterval(()=>{l+=1e4/50,l>=1e4&&(l=1e4,clearInterval(j)),t.value.customers=Math.floor(l)},30),C=setInterval(()=>{d+=50/50,d>=50&&(d=50,clearInterval(C)),t.value.patents=Math.floor(d)},30);a.unobserve(x.target)}})},{threshold:.5});o.value&&a.observe(o.value)};return F(()=>{r()}),{aboutStatsSection:o,aboutStats:t}}},ue={class:"py-20 bg-white"},pe={class:"container mx-auto px-4"},ge={class:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"},xe={"data-aos":"fade-right"},fe={class:"grid grid-cols-2 gap-6 mt-8",ref:"aboutStatsSection"},ve={class:"text-center"},me={class:"text-4xl font-bold text-blue-600 mb-2"},be={class:"text-center"},he={class:"text-4xl font-bold text-blue-600 mb-2"},ye={class:"text-center"},we={class:"text-4xl font-bold text-blue-600 mb-2"},_e={class:"text-center"},$e={class:"text-4xl font-bold text-blue-600 mb-2"};function ke(o,t,r,a,g,x){return u(),p("div",null,[t[8]||(t[8]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">公司简介</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">了解优卡特的发展历程与企业文化</p></div></div></section>',1)),e("section",ue,[e("div",pe,[e("div",ge,[e("div",xe,[t[4]||(t[4]=e("h3",{class:"text-5xl font-bold text-gray-800 mb-6"},"中国领先的一卡通科技公司",-1)),t[5]||(t[5]=e("p",{class:"text-gray-600 mb-6 text-lg"},"深圳市优卡特实业有限公司成立于2003年，是一家专注于智能一卡通系统解决方案的高新技术企业。公司总部位于中国广东深圳，以智能一卡通消费终端设备为核心，集设计、研发、生产、销售、服务于一体。",-1)),t[6]||(t[6]=e("p",{class:"text-gray-600 mb-6 text-lg"},"经过20年的发展，优卡特已成为中国一卡通行业的领军企业，为超过10000家企事业单位、学校提供了专业的智能管理解决方案，帮助客户实现数字化转型升级。",-1)),e("div",fe,[e("div",ve,[e("div",me,v(a.aboutStats.foundYear),1),t[0]||(t[0]=e("p",{class:"text-gray-600"},"成立年份",-1))]),e("div",be,[e("div",he,v(a.aboutStats.experience)+"+",1),t[1]||(t[1]=e("p",{class:"text-gray-600"},"年行业经验",-1))]),e("div",ye,[e("div",we,v(a.aboutStats.customers)+"+",1),t[2]||(t[2]=e("p",{class:"text-gray-600"},"服务客户",-1))]),e("div",_e,[e("div",$e,v(a.aboutStats.patents)+"+",1),t[3]||(t[3]=e("p",{class:"text-gray-600"},"技术专利",-1))])],512)]),t[7]||(t[7]=e("div",{"data-aos":"fade-left"},[e("img",{src:"https://placehold.co/600x400",alt:"优卡特公司总部大楼",class:"w-full rounded-lg shadow-lg"})],-1))])])]),t[9]||(t[9]=f('<section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start"><div data-aos="fade-right" class="lg:pl-12"><h2 class="text-5xl font-bold text-gray-800 mb-8">核心业务</h2><p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium"> 专注智能一卡通设备研发制造，提供全方位系统集成与技术支持服务。 </p><div class="space-y-6"><div class="flex items-start space-x-4"><div class="text-blue-600 text-2xl mt-1"><i class="fas fa-microchip"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">设备研发制造</h3><p class="text-gray-600">专业研发生产智能消费机、门禁设备、人脸识别终端等一卡通核心设备，拥有完整的产品线和自主知识产权。</p></div></div><div class="flex items-start space-x-4"><div class="text-blue-600 text-2xl mt-1"><i class="fas fa-cogs"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">系统集成服务</h3><p class="text-gray-600">提供完整的一卡通系统集成方案，包括软件开发、系统部署、项目实施等全流程服务。</p></div></div><div class="flex items-start space-x-4"><div class="text-blue-600 text-2xl mt-1"><i class="fas fa-headset"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">技术支持服务</h3><p class="text-gray-600">7×24小时技术支持，专业的售后服务团队，确保客户系统稳定运行，提供持续的技术保障。</p></div></div></div></div><div data-aos="fade-left"><img src="https://placehold.co/600x500/0066CC/FFFFFF?text=核心业务展示" alt="核心业务展示" class="w-full h-auto rounded-2xl shadow-lg"></div></div></div></section><section class="py-20 bg-gray-50"><div class="container mx-auto px-4"><div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start"><div data-aos="fade-right" class="order-2 lg:order-1"><img src="https://placehold.co/600x500/0088FF/FFFFFF?text=企业资质认证" alt="企业资质认证" class="w-full h-auto rounded-2xl shadow-lg"></div><div data-aos="fade-left" class="order-1 lg:order-2 lg:pr-12"><h2 class="text-5xl font-bold text-gray-800 mb-8">企业资质</h2><p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium"> 拥有完善的资质认证体系，确保产品质量和服务标准达到国际先进水平。 </p><div class="space-y-6"><div class="flex items-start space-x-4"><div class="text-blue-600 text-2xl mt-1"><i class="fas fa-certificate"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">高新技术企业</h3><p class="text-gray-600">获得国家级高新技术企业认证，具备强大的技术创新能力和研发实力。</p></div></div><div class="flex items-start space-x-4"><div class="text-green-600 text-2xl mt-1"><i class="fas fa-award"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">ISO9001认证</h3><p class="text-gray-600">通过ISO9001质量管理体系认证，建立了完善的质量控制和管理流程。</p></div></div><div class="flex items-start space-x-4"><div class="text-purple-600 text-2xl mt-1"><i class="fas fa-shield-alt"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">3C认证</h3><p class="text-gray-600">获得中国强制性产品认证，确保产品符合国家安全标准。</p></div></div><div class="flex items-start space-x-4"><div class="text-red-600 text-2xl mt-1"><i class="fas fa-check-circle"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">CE认证</h3><p class="text-gray-600">通过欧盟产品安全认证，产品符合欧洲市场准入标准。</p></div></div></div></div></div></div></section><section class="py-20 bg-gray-50"><div class="container mx-auto px-4"><div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start"><div data-aos="fade-right" class="lg:pl-12"><h2 class="text-5xl font-bold text-gray-800 mb-8">企业使命</h2><p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium"> 用创新的产品和服务提升行业消费管理能力，让智能科技服务于人类美好生活。 </p><p class="text-xl text-gray-600 leading-relaxed"> 我们致力于通过先进的人脸识别技术和智能一卡通解决方案， 为教育、企业、政府等各个领域提供更加便捷、安全、高效的管理体验， 推动社会数字化转型，创造更美好的智能生活。 </p></div><div data-aos="fade-left"><img src="https://placehold.co/600x500" alt="企业使命愿景" class="w-full h-auto rounded-2xl shadow-lg"></div></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start"><div data-aos="fade-right" class="order-2 lg:order-1"><img src="https://placehold.co/600x500/00AAFF/FFFFFF?text=企业文化建设" alt="企业文化建设" class="w-full h-auto rounded-2xl shadow-lg"></div><div data-aos="fade-left" class="order-1 lg:order-2 lg:pr-12"><h2 class="text-5xl font-bold text-gray-800 mb-8">企业文化</h2><p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium"> 营造积极向上的企业文化氛围，构建和谐共赢的工作环境。 </p><div class="space-y-6"><div class="flex items-start space-x-4"><div class="text-blue-600 text-2xl mt-1"><i class="fas fa-rocket"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">创新驱动</h3><p class="text-gray-600">鼓励员工创新思维，营造开放包容的创新环境，让每个人都能发挥创造力，推动企业持续发展。</p></div></div><div class="flex items-start space-x-4"><div class="text-green-600 text-2xl mt-1"><i class="fas fa-hands-helping"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">团队协作</h3><p class="text-gray-600">倡导团队合作精神，相互支持，共同成长，打造高效协作的工作团队，实现共同目标。</p></div></div><div class="flex items-start space-x-4"><div class="text-purple-600 text-2xl mt-1"><i class="fas fa-graduation-cap"></i></div><div><h3 class="text-xl font-bold text-gray-800 mb-2">学习成长</h3><p class="text-gray-600">提供完善的培训体系和职业发展通道，帮助员工实现个人价值和职业目标，与企业共同成长。</p></div></div></div></div></div></div></section><section class="py-20 bg-gray-50"><div class="container mx-auto px-4"><div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start"><div data-aos="fade-right" class="lg:pl-12"><h2 class="text-5xl font-bold text-gray-800 mb-8">发展愿景</h2><p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium"> 成为全球领先的智能一卡通设备供应商，引领行业技术发展方向。 </p><p class="text-xl text-gray-600 leading-relaxed"> 我们的愿景是在未来十年内，成为全球智能一卡通行业的技术领导者， 通过持续的技术创新和产品升级，为全球客户提供最先进的智能化解决方案， 推动整个行业向更加智能化、数字化的方向发展。 </p></div><div data-aos="fade-left"><img src="https://placehold.co/600x500" alt="企业愿景展望" class="w-full h-auto rounded-2xl shadow-lg"></div></div></div></section>',5))])}const Ce=_(ce,[["render",ke]]),Pe={name:"Products",setup(){const o=m("all"),t=async r=>{o.value=r,await kt(),N.refresh()};return F(()=>{N.init({duration:400,once:!1,offset:50,easing:"ease-out"})}),{activeFilter:o,setFilter:t}}},je={class:"py-20 bg-white"},Fe={class:"container mx-auto px-4"},Ie={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Ne={class:"product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl","data-aos":"fade-up","data-aos-duration":"400"},Se={class:"product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl","data-aos":"fade-up","data-aos-duration":"400","data-aos-delay":"100"},Ae={class:"product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl","data-aos":"fade-up","data-aos-duration":"400","data-aos-delay":"200"},Me={class:"product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl","data-aos":"fade-up","data-aos-duration":"400","data-aos-delay":"300"},Ke={class:"product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl","data-aos":"fade-up","data-aos-duration":"400","data-aos-delay":"400"},Te={class:"product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl","data-aos":"fade-up","data-aos-duration":"400","data-aos-delay":"100"},De={class:"product-card bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 relative group transition-all duration-300 hover:transform hover:-translate-y-2 hover:shadow-2xl","data-aos":"fade-up","data-aos-duration":"400","data-aos-delay":"100"};function ze(o,t,r,a,g,x){return u(),p("div",null,[t[35]||(t[35]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">产品中心</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能一卡通设备，引领科技创新</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",je,[e("div",Fe,[e("div",Ie,[D(e("div",Ne,[t[8]||(t[8]=e("div",{class:"product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"},null,-1)),t[9]||(t[9]=e("div",{class:"product-card__badge absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300"},"热销",-1)),e("div",{class:"product-card__image relative overflow-hidden cursor-pointer",onClick:t[0]||(t[0]=s=>o.$router.push("/products/android-face"))},t[7]||(t[7]=[e("img",{src:"https://placehold.co/400x300",alt:"安卓人脸消费机",class:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"},null,-1),e("div",{class:"product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"},null,-1)])),t[10]||(t[10]=f('<div class="product-card__content p-6"><h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">安卓人脸消费机</h3><p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">基于安卓系统的智能人脸识别消费终端，支持人脸识别、刷卡消费，识别速度快，准确率高。</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><span class="text-sm text-gray-500"><i class="fas fa-user mr-1"></i>人脸识别</span><span class="text-sm text-gray-500"><i class="fas fa-mobile-alt mr-1"></i>安卓系统</span></div></div></div>',1))],512),[[U,a.activeFilter==="all"||a.activeFilter==="terminal"]]),D(e("div",Se,[t[12]||(t[12]=e("div",{class:"product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"},null,-1)),t[13]||(t[13]=e("div",{class:"product-card__badge absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300"},"推荐",-1)),e("div",{class:"product-card__image relative overflow-hidden cursor-pointer",onClick:t[1]||(t[1]=s=>o.$router.push("/products/arm-terminal"))},t[11]||(t[11]=[e("img",{src:"https://placehold.co/400x300",alt:"ARM消费机",class:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"},null,-1),e("div",{class:"product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"},null,-1)])),t[14]||(t[14]=f('<div class="product-card__content p-6"><h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">ARM消费机</h3><p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">采用高性能ARM处理器的智能消费终端，支持IC卡、CPU卡等多种卡片，运行稳定可靠。</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><span class="text-sm text-gray-500"><i class="fas fa-microchip mr-1"></i>ARM处理器</span><span class="text-sm text-gray-500"><i class="fas fa-credit-card mr-1"></i>多卡支持</span></div></div></div>',1))],512),[[U,a.activeFilter==="all"||a.activeFilter==="terminal"]]),D(e("div",Ae,[t[16]||(t[16]=e("div",{class:"product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"},null,-1)),t[17]||(t[17]=e("div",{class:"product-card__badge absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300"},"新品",-1)),e("div",{class:"product-card__image relative overflow-hidden cursor-pointer",onClick:t[2]||(t[2]=s=>o.$router.push("/products/face-temp-gate"))},t[15]||(t[15]=[e("img",{src:"https://placehold.co/400x300",alt:"人脸测温通道闸机",class:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"},null,-1),e("div",{class:"product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"},null,-1)])),t[18]||(t[18]=f('<div class="product-card__content p-6"><h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">人脸测温通道闸机</h3><p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">集成人脸识别与体温检测功能的智能通道闸机，实现无接触式身份验证和健康监测。</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><span class="text-sm text-gray-500"><i class="fas fa-thermometer-half mr-1"></i>体温检测</span><span class="text-sm text-gray-500"><i class="fas fa-shield-alt mr-1"></i>安全防护</span></div></div></div>',1))],512),[[U,a.activeFilter==="all"||a.activeFilter==="access"]]),D(e("div",Me,[t[20]||(t[20]=e("div",{class:"product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"},null,-1)),t[21]||(t[21]=e("div",{class:"product-card__badge absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300"},"智能",-1)),e("div",{class:"product-card__image relative overflow-hidden cursor-pointer",onClick:t[3]||(t[3]=s=>o.$router.push("/products/water-electric"))},t[19]||(t[19]=[e("img",{src:"https://placehold.co/400x300",alt:"水/电控机",class:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"},null,-1),e("div",{class:"product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"},null,-1)])),t[22]||(t[22]=f('<div class="product-card__content p-6"><h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">水/电控机</h3><p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能水电控制系统，支持刷卡计费，节能环保，适用于学校宿舍、公寓等场所。</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><span class="text-sm text-gray-500"><i class="fas fa-tint mr-1"></i>水电控制</span><span class="text-sm text-gray-500"><i class="fas fa-leaf mr-1"></i>节能环保</span></div></div></div>',1))],512),[[U,a.activeFilter==="all"||a.activeFilter==="access"]]),D(e("div",Ke,[t[24]||(t[24]=e("div",{class:"product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"},null,-1)),t[25]||(t[25]=e("div",{class:"product-card__badge absolute top-4 left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300"},"安全",-1)),e("div",{class:"product-card__image relative overflow-hidden cursor-pointer",onClick:t[4]||(t[4]=s=>o.$router.push("/products/access-control"))},t[23]||(t[23]=[e("img",{src:"https://placehold.co/400x300",alt:"门禁机",class:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"},null,-1),e("div",{class:"product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"},null,-1)])),t[26]||(t[26]=f('<div class="product-card__content p-6"><h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">门禁机</h3><p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">多种识别方式，安全可靠，支持刷卡、密码、指纹等多种开门方式，保障出入安全。</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><span class="text-sm text-gray-500"><i class="fas fa-door-open mr-1"></i>门禁控制</span><span class="text-sm text-gray-500"><i class="fas fa-fingerprint mr-1"></i>多种识别</span></div></div></div>',1))],512),[[U,a.activeFilter==="all"||a.activeFilter==="access"]]),D(e("div",Te,[t[28]||(t[28]=e("div",{class:"product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"},null,-1)),t[29]||(t[29]=e("div",{class:"product-card__badge absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300"},"共享",-1)),e("div",{class:"product-card__image relative overflow-hidden cursor-pointer",onClick:t[5]||(t[5]=s=>o.$router.push("/products/campus-shared"))},t[27]||(t[27]=[e("img",{src:"https://placehold.co/400x300",alt:"校园共享设备",class:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"},null,-1),e("div",{class:"product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"},null,-1)])),t[30]||(t[30]=f('<div class="product-card__content p-6"><h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">校园共享设备</h3><p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">智能共享设备管理系统，支持洗衣机、吹风机等设备的智能控制和计费管理。</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><span class="text-sm text-gray-500"><i class="fas fa-graduation-cap mr-1"></i>校园应用</span><span class="text-sm text-gray-500"><i class="fas fa-share-alt mr-1"></i>共享管理</span></div></div></div>',1))],512),[[U,a.activeFilter==="all"||a.activeFilter==="control"]]),D(e("div",De,[t[32]||(t[32]=e("div",{class:"product-card__glow absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-20 transition-opacity duration-500 rounded-xl"},null,-1)),t[33]||(t[33]=e("div",{class:"product-card__badge absolute top-4 left-4 bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-bold z-10 transform scale-0 group-hover:scale-100 transition-transform duration-300"},"云端",-1)),e("div",{class:"product-card__image relative overflow-hidden cursor-pointer",onClick:t[6]||(t[6]=s=>o.$router.push("/products/face-cloud"))},t[31]||(t[31]=[e("img",{src:"https://placehold.co/400x300",alt:"脸爱云平台",class:"w-full h-64 object-cover transition-transform duration-500 group-hover:scale-105"},null,-1),e("div",{class:"product-overlay absolute inset-0 bg-gradient-to-t from-blue-600 to-transparent opacity-0 group-hover:opacity-90 transition-all duration-500"},null,-1)])),t[34]||(t[34]=f('<div class="product-card__content p-6"><h3 class="product-card__title text-2xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors duration-300">脸爱云平台</h3><p class="product-card__description text-gray-600 mb-4 opacity-80 group-hover:opacity-100 transition-opacity duration-300">云端智能管理平台，提供设备监控、数据分析、远程管理等一站式服务解决方案。</p><div class="flex items-center justify-between"><div class="flex items-center space-x-4"><span class="text-sm text-gray-500"><i class="fas fa-cloud mr-1"></i>云端管理</span><span class="text-sm text-gray-500"><i class="fas fa-chart-bar mr-1"></i>数据分析</span></div></div></div>',1))],512),[[U,a.activeFilter==="all"||a.activeFilter==="platform"]])])])])])}const Re=_(Pe,[["render",ze]]),We={name:"Solutions",setup(){F(()=>{N.init({duration:400,once:!0})})}},Ee={class:"py-20 bg-white"},Le={class:"container mx-auto px-4"},Ye={class:"space-y-8"};function Be(o,t,r,a,g,x){return u(),p("div",null,[t[12]||(t[12]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">解决方案</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">为不同行业提供专业的一卡通解决方案</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",Ee,[e("div",Le,[e("div",Ye,[e("div",{class:"solution-item flex flex-col md:flex-row items-center bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 group",onClick:t[0]||(t[0]=s=>o.$router.push("/solutions/smart-campus")),"data-aos":"fade-up"},t[6]||(t[6]=[f('<div class="md:w-1/3 p-6 flex justify-center"><div class="w-64 h-64 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center text-white text-2xl font-bold"> 智慧校园 </div></div><div class="md:w-2/3 p-8 text-center md:text-left relative flex flex-col justify-center items-start"><h3 class="text-3xl font-bold text-gray-800 mb-4">智慧校园解决方案</h3><p class="text-xl text-gray-600 mb-4 leading-relaxed">集成消费、门禁、考勤、图书借阅等功能，打造数字化校园生活</p><div class="absolute right-8 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-2"><svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path></svg></div></div>',2)])),e("div",{class:"solution-item flex flex-col md:flex-row items-center bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 group",onClick:t[1]||(t[1]=s=>o.$router.push("/solutions/enterprise-park")),"data-aos":"fade-up","data-aos-delay":"200"},t[7]||(t[7]=[f('<div class="md:w-1/3 p-6 flex justify-center"><div class="w-64 h-64 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center text-white text-2xl font-bold"> 企业园区 </div></div><div class="md:w-2/3 p-8 text-center md:text-left relative flex flex-col justify-center items-start"><h3 class="text-3xl font-bold text-gray-800 mb-4">企业园区解决方案</h3><p class="text-xl text-gray-600 mb-4 leading-relaxed">提升企业管理效率，降低运营成本，打造智能化办公环境</p><div class="absolute right-8 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-2"><svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path></svg></div></div>',2)])),e("div",{class:"solution-item flex flex-col md:flex-row items-center bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 group",onClick:t[2]||(t[2]=s=>o.$router.push("/solutions/medical-institution")),"data-aos":"fade-up","data-aos-delay":"400"},t[8]||(t[8]=[f('<div class="md:w-1/3 p-6 flex justify-center"><div class="w-64 h-64 bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center text-white text-2xl font-bold"> 医疗机构 </div></div><div class="md:w-2/3 p-8 text-center md:text-left relative flex flex-col justify-center items-start"><h3 class="text-3xl font-bold text-gray-800 mb-4">医疗机构解决方案</h3><p class="text-xl text-gray-600 mb-4 leading-relaxed">改善医院服务流程，提高管理效率，保障医疗安全</p><div class="absolute right-8 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-2"><svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path></svg></div></div>',2)])),e("div",{class:"solution-item flex flex-col md:flex-row items-center bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 group",onClick:t[3]||(t[3]=s=>o.$router.push("/solutions/government-agency")),"data-aos":"fade-up","data-aos-delay":"600"},t[9]||(t[9]=[f('<div class="md:w-1/3 p-6 flex justify-center"><div class="w-64 h-64 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center text-white text-2xl font-bold"> 政府机关 </div></div><div class="md:w-2/3 p-8 text-center md:text-left relative flex flex-col justify-center items-start"><h3 class="text-3xl font-bold text-gray-800 mb-4">政府机关解决方案</h3><p class="text-xl text-gray-600 mb-4 leading-relaxed">提升政务服务效率，加强安全管理，实现数字化办公</p><div class="absolute right-8 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-2"><svg class="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path></svg></div></div>',2)])),e("div",{class:"solution-item flex flex-col md:flex-row items-center bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 group",onClick:t[4]||(t[4]=s=>o.$router.push("/solutions/factory-park")),"data-aos":"fade-up","data-aos-delay":"800"},t[10]||(t[10]=[f('<div class="md:w-1/3 p-6 flex justify-center"><div class="w-64 h-64 bg-gradient-to-br from-orange-400 to-orange-600 rounded-lg flex items-center justify-center text-white text-2xl font-bold"> 工厂园区 </div></div><div class="md:w-2/3 p-8 text-center md:text-left relative flex flex-col justify-center items-start"><h3 class="text-3xl font-bold text-gray-800 mb-4">工厂园区解决方案</h3><p class="text-xl text-gray-600 mb-4 leading-relaxed">加强生产安全管理，提升工作效率，优化资源配置</p><div class="absolute right-8 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-2"><svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path></svg></div></div>',2)])),e("div",{class:"solution-item flex flex-col md:flex-row items-center bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 group",onClick:t[5]||(t[5]=s=>o.$router.push("/solutions/smart-hotel")),"data-aos":"fade-up","data-aos-delay":"1000"},t[11]||(t[11]=[f('<div class="md:w-1/3 p-6 flex justify-center"><div class="w-64 h-64 bg-gradient-to-br from-pink-400 to-pink-600 rounded-lg flex items-center justify-center text-white text-2xl font-bold"> 智慧酒店 </div></div><div class="md:w-2/3 p-8 text-center md:text-left relative flex flex-col justify-center items-start"><h3 class="text-3xl font-bold text-gray-800 mb-4">智慧酒店解决方案</h3><p class="text-xl text-gray-600 mb-4 leading-relaxed">提升客户体验，优化酒店管理，降低运营成本</p><div class="absolute right-8 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 group-hover:translate-x-2"><svg class="w-8 h-8 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path></svg></div></div>',2)]))])])])])}const qe=_(We,[["render",Be]]),Ge={name:"Pagination",props:{currentPage:{type:Number,default:1},totalItems:{type:Number,required:!0},itemsPerPage:{type:Number,default:10},themeColor:{type:String,default:"blue"}},emits:["page-change","items-per-page-change"],setup(o,{emit:t}){const r=A(()=>Math.ceil(o.totalItems/o.itemsPerPage)),a=A(()=>{const y=[],K=r.value,k=o.currentPage;if(K<=7)for(let b=1;b<=K;b++)y.push(b);else if(k<=4)for(let b=2;b<=5;b++)y.push(b);else if(k>=K-3)for(let b=K-4;b<=K-1;b++)y.push(b);else for(let b=k-1;b<=k+1;b++)y.push(b);return y}),g=A(()=>r.value>1&&!a.value.includes(1)),x=A(()=>r.value>1&&!a.value.includes(r.value)),s=A(()=>g.value&&a.value[0]>2),l=A(()=>x.value&&a.value[a.value.length-1]<r.value-1),d=y=>{y>=1&&y<=r.value&&y!==o.currentPage&&t("page-change",y)};return{totalPages:r,visiblePages:a,showFirstPage:g,showLastPage:x,showStartEllipsis:s,showEndEllipsis:l,goToPage:d,goToPreviousPage:()=>{o.currentPage>1&&d(o.currentPage-1)},goToNextPage:()=>{o.currentPage<r.value&&d(o.currentPage+1)},changeItemsPerPage:y=>{t("items-per-page-change",parseInt(y))}}}},He={class:"pagination-container py-12 bg-gray-50","data-aos":"fade-up"},Oe={class:"container mx-auto px-4"},Ue={class:"flex flex-col sm:flex-row items-center justify-between gap-4"},Ve={class:"text-gray-600 text-sm"},Qe={class:"flex items-center gap-2"},Xe=["disabled"],Je={class:"flex items-center gap-1"},Ze={key:1,class:"px-2 text-gray-400"},ta=["onClick"],ea={key:2,class:"px-2 text-gray-400"},aa=["disabled"],sa={class:"flex items-center gap-2 text-sm text-gray-600"},oa=["value"];function la(o,t,r,a,g,x){return u(),p("div",He,[e("div",Oe,[e("div",Ue,[e("div",Ve," 共 "+v(r.totalItems)+" 条新闻，第 "+v(r.currentPage)+" 页 / 共 "+v(a.totalPages)+" 页 ",1),e("div",Qe,[e("button",{onClick:t[0]||(t[0]=(...s)=>a.goToPreviousPage&&a.goToPreviousPage(...s)),disabled:r.currentPage<=1,class:M(["pagination-btn px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2",r.currentPage<=1?"bg-gray-200 text-gray-400 cursor-not-allowed":`bg-white text-${r.themeColor}-600 border border-${r.themeColor}-200 hover:bg-${r.themeColor}-50 hover:border-${r.themeColor}-400 hover:shadow-md`])},t[5]||(t[5]=[e("i",{class:"fas fa-chevron-left text-sm"},null,-1),e("span",{class:"hidden sm:inline"},"上一页",-1)]),10,Xe),e("div",Je,[a.showFirstPage?(u(),p("button",{key:0,onClick:t[1]||(t[1]=s=>a.goToPage(1)),class:M(["page-number w-10 h-10 rounded-lg font-medium transition-all duration-300",r.currentPage===1?`bg-${r.themeColor}-600 text-white shadow-lg`:`bg-white text-${r.themeColor}-600 border border-${r.themeColor}-200 hover:bg-${r.themeColor}-50`])}," 1 ",2)):h("",!0),a.showStartEllipsis?(u(),p("span",Ze,"...")):h("",!0),(u(!0),p(R,null,W(a.visiblePages,s=>(u(),p("button",{key:s,onClick:l=>a.goToPage(s),class:M(["page-number w-10 h-10 rounded-lg font-medium transition-all duration-300",r.currentPage===s?`bg-${r.themeColor}-600 text-white shadow-lg`:`bg-white text-${r.themeColor}-600 border border-${r.themeColor}-200 hover:bg-${r.themeColor}-50`])},v(s),11,ta))),128)),a.showEndEllipsis?(u(),p("span",ea,"...")):h("",!0),a.showLastPage?(u(),p("button",{key:3,onClick:t[2]||(t[2]=s=>a.goToPage(a.totalPages)),class:M(["page-number w-10 h-10 rounded-lg font-medium transition-all duration-300",r.currentPage===a.totalPages?`bg-${r.themeColor}-600 text-white shadow-lg`:`bg-white text-${r.themeColor}-600 border border-${r.themeColor}-200 hover:bg-${r.themeColor}-50`])},v(a.totalPages),3)):h("",!0)]),e("button",{onClick:t[3]||(t[3]=(...s)=>a.goToNextPage&&a.goToNextPage(...s)),disabled:r.currentPage>=a.totalPages,class:M(["pagination-btn px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center gap-2",r.currentPage>=a.totalPages?"bg-gray-200 text-gray-400 cursor-not-allowed":`bg-white text-${r.themeColor}-600 border border-${r.themeColor}-200 hover:bg-${r.themeColor}-50 hover:border-${r.themeColor}-400 hover:shadow-md`])},t[6]||(t[6]=[e("span",{class:"hidden sm:inline"},"下一页",-1),e("i",{class:"fas fa-chevron-right text-sm"},null,-1)]),10,aa)]),e("div",sa,[t[8]||(t[8]=e("span",null,"每页显示",-1)),e("select",{value:r.itemsPerPage,onChange:t[4]||(t[4]=s=>a.changeItemsPerPage(s.target.value)),class:M(`border border-${r.themeColor}-200 rounded-lg px-3 py-1 bg-white text-${r.themeColor}-600 focus:outline-none focus:ring-2 focus:ring-${r.themeColor}-500 focus:border-${r.themeColor}-400`)},t[7]||(t[7]=[e("option",{value:"5"},"5条",-1),e("option",{value:"10"},"10条",-1),e("option",{value:"15"},"15条",-1),e("option",{value:"20"},"20条",-1)]),42,oa)])])])])}const tt=_(Ge,[["render",la],["__scopeId","data-v-5ac942c6"]]);function et(o,t=10){const r=m(1),a=m(t),g=m(""),x=A(()=>g.value.trim()?o.value.filter(b=>b.title.toLowerCase().includes(g.value.toLowerCase())||b.content.toLowerCase().includes(g.value.toLowerCase())):o.value),s=A(()=>x.value.length),l=A(()=>Math.ceil(s.value/a.value)),d=A(()=>{const b=(r.value-1)*a.value,z=b+a.value;return x.value.slice(b,z)}),w=A(()=>r.value>1),j=A(()=>r.value<l.value),C=b=>{b>=1&&b<=l.value&&(r.value=b,window.scrollTo({top:0,behavior:"smooth"}))},y=b=>{a.value=b,r.value=1},K=b=>{g.value=b,r.value=1},k=()=>{g.value="",r.value=1};return dt(g,()=>{r.value=1}),dt(l,b=>{r.value>b&&b>0&&(r.value=b)}),{currentPage:r,itemsPerPage:a,searchKeyword:g,filteredItems:x,paginatedItems:d,totalItems:s,totalPages:l,hasPreviousPage:w,hasNextPage:j,handlePageChange:C,handleItemsPerPageChange:y,handleSearch:K,clearSearch:k}}const da={name:"News",components:{Pagination:tt},setup(){const o=m("all"),t=m([{id:1,title:"优卡特发布新一代人脸识别终端",excerpt:"公司新一代人脸识别终端采用深度学习算法，识别速度和准确率大幅提升，为客户提供更优质的服务体验。",category:"company",categoryName:"企业新闻",categoryClass:"bg-blue-100 text-blue-600",date:"2024-01-15",image:"https://placehold.co/200x150"},{id:2,title:"优卡特参加2024智慧校园建设展览会",excerpt:"本次展会上，优卡特展示了最新的智慧校园一卡通解决方案，引起了广泛关注和好评。",category:"company",categoryName:"企业新闻",categoryClass:"bg-blue-100 text-blue-600",date:"2024-01-10",image:"https://placehold.co/200x150"},{id:3,title:"公司获得ISO9001质量管理体系认证",excerpt:"经过严格的审核，优卡特成功获得ISO9001质量管理体系认证，标志着公司在质量管理方面达到国际先进水平。",category:"company",categoryName:"企业新闻",categoryClass:"bg-blue-100 text-blue-600",date:"2024-01-05",image:"https://placehold.co/200x150"},{id:4,title:"2024年智能一卡通市场分析报告",excerpt:"根据最新市场调研数据，2024年智能一卡通市场规模预计将达到500亿元，同比增长15%。",category:"industry",categoryName:"行业资讯",categoryClass:"bg-green-100 text-green-600",date:"2024-01-12",image:"https://placehold.co/200x150"},{id:5,title:"人脸识别技术在校园安全中的应用",excerpt:"随着人工智能技术的快速发展，人脸识别技术在校园安全管理中发挥着越来越重要的作用。",category:"industry",categoryName:"行业资讯",categoryClass:"bg-green-100 text-green-600",date:"2024-01-08",image:"https://placehold.co/200x150"},{id:6,title:"数据安全法实施细则解读",excerpt:"新版数据安全法实施细则对智能设备数据采集、存储、使用提出了更严格的要求。",category:"industry",categoryName:"行业资讯",categoryClass:"bg-green-100 text-green-600",date:"2024-01-03",image:"https://placehold.co/200x150"},{id:7,title:"一卡通系统常见问题解答",excerpt:"本文整理了用户在使用一卡通系统过程中经常遇到的问题及解决方案。",category:"technical",categoryName:"技术支持",categoryClass:"bg-purple-100 text-purple-600",date:"2024-01-01",image:"https://placehold.co/200x150"},{id:8,title:"设备维护保养指南",excerpt:"为确保设备正常运行，延长使用寿命，请按照本指南进行定期维护保养。",category:"technical",categoryName:"技术支持",categoryClass:"bg-purple-100 text-purple-600",date:"2023-12-28",image:"https://placehold.co/200x150"}]),{currentPage:r,itemsPerPage:a,searchKeyword:g,paginatedItems:x,totalItems:s,totalPages:l,handlePageChange:d,handleItemsPerPageChange:w,handleSearch:j,clearSearch:C}=et(t,10),y=A(()=>{let E=x.value;return o.value!=="all"&&(E=E.filter(at=>at.category===o.value)),E}),K=E=>{o.value=E},k=()=>{j(g.value)},b=()=>{C()},z=E=>{console.log("打开新闻详情:",E),alert(`点击了新闻: ${E}`)};return F(()=>{N.init({duration:400,once:!0})}),{activeCategory:o,searchKeyword:g,allNews:t,filteredNews:y,paginatedNews:x,setCategory:K,searchNews:k,clearSearch:b,openNewsDetail:z,currentPage:r,itemsPerPage:a,totalItems:s,totalPages:l,handlePageChange:d,handleItemsPerPageChange:w}}},ra={class:"py-12 bg-gray-50"},ia={class:"container mx-auto px-4"},na={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},ca={class:"pt-8 pb-20 bg-white"},ua={class:"container mx-auto px-4"},pa={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},ga={class:"lg:col-span-2"},xa={class:"grid gap-8"},fa={class:"space-y-6"},va={key:0},ma=["onClick"],ba=["src","alt"],ha={class:"p-4 flex-1"},ya={class:"flex items-center mb-2"},wa={class:"text-gray-500 text-xs ml-2"},_a={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition duration-300 cursor-pointer"},$a={class:"text-gray-600 text-sm"},ka={key:1,class:"text-center py-16"},Ca={class:"max-w-md mx-auto"},Pa={class:"text-gray-500 mb-4"},ja={class:"font-medium text-blue-600"},Fa={class:"lg:col-span-1"},Ia={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Na={class:"relative"},Sa={class:"space-y-8"},Aa={class:"bg-gradient-to-br from-blue-600 to-blue-800 rounded-xl p-6 text-white","data-aos":"fade-left","data-aos-delay":"200"};function Ma(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[18]||(t[18]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">新闻中心</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">了解优卡特最新动态与行业资讯</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",ra,[e("div",ia,[e("div",na,[e("button",{class:M(["category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300",a.activeCategory==="all"?"bg-blue-600 text-white":"bg-white text-gray-600 hover:text-blue-600"]),onClick:t[0]||(t[0]=d=>a.setCategory("all"))}," 全部新闻 ",2),n(s,{to:"/news/company",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[5]||(t[5]=[i(" 企业新闻 ")])),_:1,__:[5]}),n(s,{to:"/news/industry",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[6]||(t[6]=[i(" 行业资讯 ")])),_:1,__:[6]})])])]),e("section",ca,[e("div",ua,[e("div",pa,[e("div",ga,[e("div",xa,[a.searchKeyword.trim()===""&&(a.activeCategory==="all"||a.activeCategory==="company")?(u(),p("a",{key:0,href:"#",onClick:t[1]||(t[1]=T(d=>a.openNewsDetail("featured-news-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[7]||(t[7]=[f('<div class="relative"><img src="https://placehold.co/800x400" alt="优卡特发布新一代人脸识别终端" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500"><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold"> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6"><div class="flex items-center mb-2"><span class="bg-blue-100 text-blue-600 px-3 py-1 rounded text-sm">企业新闻</span><span class="text-white text-sm ml-3">2023-12-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-blue-200 transition-colors duration-300">优卡特发布新一代人脸识别终端</h2><p class="text-gray-200">公司新一代人脸识别终端采用深度学习算法，识别速度和准确率大幅提升，为客户提供更优质的服务体验...</p></div></div>',1)]))):h("",!0),e("div",fa,[a.paginatedNews.length>0?(u(),p("div",va,[(u(!0),p(R,null,W(a.paginatedNews,d=>D((u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:d.image,alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,ba),e("div",ha,[e("div",ya,[e("span",{class:M([d.categoryClass,"px-2 py-1 rounded text-xs"])},v(d.categoryName),3),e("span",wa,v(d.date),1)]),e("h4",_a,v(d.title),1),e("p",$a,v(d.excerpt),1)])],8,ma)),[[U,a.activeCategory==="all"||a.activeCategory===d.category]])),128))])):a.searchKeyword.trim()!==""?(u(),p("div",ka,[e("div",Ca,[t[11]||(t[11]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[12]||(t[12]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",Pa,[t[8]||(t[8]=i(' 没有找到包含 "')),e("span",ja,v(a.searchKeyword),1),t[9]||(t[9]=i('" 的新闻内容 '))]),e("button",{onClick:t[2]||(t[2]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300"},t[10]||(t[10]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",Fa,[e("div",Ia,[t[14]||(t[14]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-blue-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",Na,[t[13]||(t[13]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索资讯...","onUpdate:modelValue":t[3]||(t[3]=d=>a.searchKeyword=d),onKeyup:t[4]||(t[4]=H((...d)=>a.searchNews&&a.searchNews(...d),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-blue-50 border border-blue-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-400 focus:bg-white transition-all duration-200 placeholder-blue-400"},null,544),[[G,a.searchKeyword]])])]),e("div",Sa,[t[17]||(t[17]=f('<div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100" data-aos="fade-left"><h3 class="text-xl font-bold text-gray-800 mb-4 border-b border-gray-200 pb-2"><i class="fas fa-fire text-red-500 mr-2"></i>热门新闻 </h3><div class="space-y-4"><div class="flex items-start space-x-3 group cursor-pointer"><img src="https://placehold.co/80x60" alt="新闻缩略图" class="w-16 h-12 object-cover rounded group-hover:scale-105 transition-transform duration-300"><div class="flex-1"><h4 class="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition duration-300 line-clamp-2"> 优卡特与某知名大学达成战略合作 </h4><p class="text-xs text-gray-500 mt-1">2023-12-03</p></div></div><div class="flex items-start space-x-3 group cursor-pointer"><img src="https://placehold.co/80x60" alt="新闻缩略图" class="w-16 h-12 object-cover rounded group-hover:scale-105 transition-transform duration-300"><div class="flex-1"><h4 class="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition duration-300 line-clamp-2"> 智能消费终端市场前景分析 </h4><p class="text-xs text-gray-500 mt-1">2023-12-01</p></div></div></div></div>',1)),e("div",Aa,[t[16]||(t[16]=f('<h3 class="text-xl font-bold mb-4"><i class="fas fa-phone mr-2"></i>联系我们 </h3><div class="space-y-3 text-sm"><div class="flex items-center"><i class="fas fa-map-marker-alt w-4 mr-3"></i><span>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</span></div><div class="flex items-center"><i class="fas fa-phone w-4 mr-3"></i><a href="tel:159-8667-2052" class="text-white hover:text-blue-200 transition duration-300">159-8667-2052</a></div><div class="flex items-center"><i class="fas fa-envelope w-4 mr-3"></i><a href="mailto:<EMAIL>" class="text-white hover:text-blue-200 transition duration-300"><EMAIL></a></div></div>',2)),n(s,{to:"/contact",class:"inline-block mt-4 px-4 py-2 bg-white text-blue-600 rounded-lg font-semibold hover:bg-gray-100 transition duration-300"},{default:c(()=>t[15]||(t[15]=[i(" 了解更多 ")])),_:1,__:[15]})])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"blue",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0)])}const Ka=_(da,[["render",Ma]]),Ta={name:"NewsCard",props:{news:{type:Object,required:!0}},setup(o){const t=A(()=>({company:"企业新闻",industry:"行业资讯",support:"技术支持"})[o.news.category]||"其他"),r=A(()=>({company:"bg-blue-100 text-blue-800",industry:"bg-green-100 text-green-800",support:"bg-red-100 text-red-800"})[o.news.category]||"bg-gray-100 text-gray-800");return{categoryName:t,categoryClass:r,formatDate:g=>g?new Date(g).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}):""}}},Da={class:"news-card","data-aos":"fade-up"},za=["src","alt"],Ra={class:"p-6"},Wa={class:"flex items-center mb-2"},Ea={class:"text-gray-500 text-sm ml-auto"},La={class:"text-xl font-bold mb-2 line-clamp-2"},Ya={class:"text-gray-600 mb-4 line-clamp-3"},Ba={class:"flex items-center justify-between"},qa={class:"flex items-center text-gray-500 text-sm"};function Ga(o,t,r,a,g,x){const s=$("router-link");return u(),p("article",Da,[e("img",{src:r.news.imageUrl||"https://placehold.co/600x400",alt:r.news.title,class:"w-full h-48 object-cover"},null,8,za),e("div",Ra,[e("div",Wa,[e("span",{class:M(["px-2 py-1 rounded text-sm",a.categoryClass])},v(a.categoryName),3),e("span",Ea,v(a.formatDate(r.news.publishedAt)),1)]),e("h3",La,v(r.news.title),1),e("p",Ya,v(r.news.summary||r.news.content.substring(0,100)+"..."),1),e("div",Ba,[n(s,{to:`/news/${r.news.id}`,class:"text-blue-600 hover:text-blue-800 font-medium"},{default:c(()=>t[0]||(t[0]=[i(" 阅读更多 → ")])),_:1,__:[0]},8,["to"]),e("div",qa,[t[1]||(t[1]=e("i",{class:"fas fa-eye mr-1"},null,-1)),e("span",null,v(r.news.viewCount||0),1)])])])])}const Ha=_(Ta,[["render",Ga],["__scopeId","data-v-6ad980e0"]]),Oa={name:"NewsDetail",components:{NewsCard:Ha},setup(){const o=X(),t=yt(),r=m(!1),a=m(null),g=m([]),x=A(()=>{var b;return{company:"企业新闻",industry:"行业资讯",support:"技术支持"}[(b=a.value)==null?void 0:b.category]||"其他"}),s=A(()=>{var b;return{company:"bg-blue-100 text-blue-800",industry:"bg-green-100 text-green-800",support:"bg-red-100 text-red-800"}[(b=a.value)==null?void 0:b.category]||"bg-gray-100 text-gray-800"}),l=()=>{var b;return{company:"company",industry:"industry",support:"support"}[(b=a.value)==null?void 0:b.category]||""},d=k=>k?new Date(k).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"",w=k=>k?k.split(`
`).filter(b=>b.trim()).map(b=>`<p class="mb-4">${b.trim()}</p>`).join(""):"",j=()=>{alert("微信分享功能开发中...")},C=()=>{const k=encodeURIComponent(window.location.href),b=encodeURIComponent(a.value.title),z=`https://service.weibo.com/share/share.php?url=${k}&title=${b}`;window.open(z,"_blank")},y=async()=>{try{await navigator.clipboard.writeText(window.location.href),alert("链接已复制到剪贴板")}catch(k){console.error("复制失败:",k),alert("复制失败，请手动复制链接")}},K=async k=>{r.value=!0;try{const b=await t.fetchNewsById(k);if(a.value=b,b!=null&&b.category){const z=await t.fetchNewsByCategory(b.category);g.value=z.filter(E=>E.id!==b.id).slice(0,3)}}catch(b){console.error("加载新闻详情失败:",b),a.value=null}finally{r.value=!1}};return dt(()=>o.params.id,k=>{k&&K(parseInt(k))},{immediate:!0}),F(()=>{const k=o.params.id;k&&K(parseInt(k))}),{loading:r,news:a,relatedNews:g,categoryName:x,categoryClass:s,getCategoryPath:l,formatDate:d,formatContent:w,shareToWeChat:j,shareToWeibo:C,copyLink:y}}},Ua={class:"news-detail"},Va={key:0,class:"min-h-screen flex items-center justify-center"},Qa={key:1,class:"min-h-screen flex items-center justify-center"},Xa={class:"text-center"},Ja={key:2},Za={class:"pt-24 pb-12 bg-white"},ts={class:"container-custom"},es={class:"max-w-4xl mx-auto"},as={class:"mb-8","data-aos":"fade-up"},ss={class:"flex items-center space-x-2 text-sm text-gray-500"},os={class:"text-gray-700"},ls={class:"text-4xl md:text-5xl font-bold mb-6","data-aos":"fade-up"},ds={class:"flex flex-wrap items-center gap-6 mb-8 text-gray-600","data-aos":"fade-up","data-aos-delay":"200"},rs={class:"flex items-center"},is={class:"flex items-center"},ns={class:"flex items-center"},cs={class:"flex items-center"},us={key:0,class:"bg-blue-50 border-l-4 border-blue-500 p-6 mb-8","data-aos":"fade-up","data-aos-delay":"300"},ps={class:"text-lg text-gray-700 leading-relaxed"},gs={class:"py-12 bg-white"},xs={class:"container-custom"},fs={class:"max-w-4xl mx-auto"},vs={key:0,class:"mb-8","data-aos":"fade-up"},ms=["src","alt"],bs={class:"prose prose-lg max-w-none","data-aos":"fade-up","data-aos-delay":"200"},hs=["innerHTML"],ys={class:"mt-12 pt-8 border-t border-gray-200","data-aos":"fade-up"},ws={class:"flex space-x-4"},_s={class:"py-20 bg-gray-50"},$s={class:"container-custom"},ks={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"};function Cs(o,t,r,a,g,x){const s=$("router-link"),l=$("NewsCard");return u(),p("div",Ua,[a.loading?(u(),p("div",Va,t[3]||(t[3]=[e("div",{class:"loading"},null,-1)]))):a.news?(u(),p("div",Ja,[e("section",Za,[e("div",ts,[e("div",es,[e("nav",as,[e("ol",ss,[e("li",null,[n(s,{to:"/",class:"hover:text-blue-600"},{default:c(()=>t[8]||(t[8]=[i("首页")])),_:1,__:[8]})]),t[10]||(t[10]=e("li",null,[e("i",{class:"fas fa-chevron-right mx-2"})],-1)),e("li",null,[n(s,{to:"/news",class:"hover:text-blue-600"},{default:c(()=>t[9]||(t[9]=[i("新闻中心")])),_:1,__:[9]})]),t[11]||(t[11]=e("li",null,[e("i",{class:"fas fa-chevron-right mx-2"})],-1)),e("li",null,[n(s,{to:`/news/${a.getCategoryPath()}`,class:"hover:text-blue-600"},{default:c(()=>[i(v(a.categoryName),1)]),_:1},8,["to"])]),t[12]||(t[12]=e("li",null,[e("i",{class:"fas fa-chevron-right mx-2"})],-1)),e("li",os,v(a.news.title),1)])]),e("h1",ls,v(a.news.title),1),e("div",ds,[e("div",rs,[t[13]||(t[13]=e("i",{class:"fas fa-calendar-alt mr-2"},null,-1)),e("span",null,v(a.formatDate(a.news.publishedAt)),1)]),e("div",is,[t[14]||(t[14]=e("i",{class:"fas fa-user mr-2"},null,-1)),e("span",null,v(a.news.author||"优卡特"),1)]),e("div",ns,[t[15]||(t[15]=e("i",{class:"fas fa-tag mr-2"},null,-1)),e("span",{class:M(["px-2 py-1 rounded text-sm",a.categoryClass])},v(a.categoryName),3)]),e("div",cs,[t[16]||(t[16]=e("i",{class:"fas fa-eye mr-2"},null,-1)),e("span",null,v(a.news.viewCount||0)+" 次浏览",1)])]),a.news.summary?(u(),p("div",us,[e("p",ps,v(a.news.summary),1)])):h("",!0)])])]),e("section",gs,[e("div",xs,[e("div",fs,[a.news.imageUrl?(u(),p("div",vs,[e("img",{src:a.news.imageUrl,alt:a.news.title,class:"w-full h-auto rounded-2xl shadow-lg"},null,8,ms)])):h("",!0),e("div",bs,[e("div",{innerHTML:a.formatContent(a.news.content)},null,8,hs)]),e("div",ys,[t[20]||(t[20]=e("h3",{class:"text-lg font-semibold mb-4"},"分享这篇文章",-1)),e("div",ws,[e("button",{onClick:t[0]||(t[0]=(...d)=>a.shareToWeChat&&a.shareToWeChat(...d)),class:"bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition duration-300"},t[17]||(t[17]=[e("i",{class:"fab fa-weixin mr-2"},null,-1),i(" 微信 ")])),e("button",{onClick:t[1]||(t[1]=(...d)=>a.shareToWeibo&&a.shareToWeibo(...d)),class:"bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition duration-300"},t[18]||(t[18]=[e("i",{class:"fab fa-weibo mr-2"},null,-1),i(" 微博 ")])),e("button",{onClick:t[2]||(t[2]=(...d)=>a.copyLink&&a.copyLink(...d)),class:"bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition duration-300"},t[19]||(t[19]=[e("i",{class:"fas fa-link mr-2"},null,-1),i(" 复制链接 ")]))])])])])]),e("section",_s,[e("div",$s,[t[21]||(t[21]=e("div",{class:"text-center mb-12","data-aos":"fade-up"},[e("h2",{class:"text-3xl font-bold mb-4"},"相关新闻"),e("p",{class:"text-lg text-gray-600"},"您可能还感兴趣的其他新闻")],-1)),a.relatedNews.length>0?(u(),p("div",ks,[(u(!0),p(R,null,W(a.relatedNews,d=>(u(),Q(l,{key:d.id,news:d},null,8,["news"]))),128))])):h("",!0)])])])):(u(),p("div",Qa,[e("div",Xa,[t[5]||(t[5]=e("i",{class:"fas fa-newspaper text-6xl text-gray-400 mb-4"},null,-1)),t[6]||(t[6]=e("h2",{class:"text-2xl font-bold text-gray-600 mb-2"},"新闻不存在",-1)),t[7]||(t[7]=e("p",{class:"text-gray-500 mb-6"},"抱歉，您访问的新闻页面不存在",-1)),n(s,{to:"/news",class:"btn-primary"},{default:c(()=>t[4]||(t[4]=[e("i",{class:"fas fa-arrow-left mr-2"},null,-1),i(" 返回新闻中心 ")])),_:1,__:[4]})])]))])}const Ps=_(Oa,[["render",Cs],["__scopeId","data-v-40a40d2a"]]),js={name:"Contact",setup(){F(()=>{N.init({duration:400,once:!0})})}};function Fs(o,t,r,a,g,x){return u(),p("div",null,t[0]||(t[0]=[f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">联系我们</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">我们期待与您的合作，随时为您提供专业服务</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto"><div class="contact-card bg-white p-8 rounded-xl border border-gray-100 text-center group hover:shadow-2xl hover:border-blue-200 transition-all duration-500" data-aos="fade-up"><div class="contact-icon bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-600 group-hover:scale-110 transition-all duration-500"><i class="fas fa-phone text-blue-600 text-3xl group-hover:text-white transition-colors duration-500"></i></div><h3 class="text-xl font-bold text-gray-800 mb-3">电话咨询</h3><p class="text-gray-600 mb-4">周一至周六 8:55-18:00</p><div class="space-y-2"><a href="tel:159-8667-2052" class="text-blue-600 font-semibold hover:text-blue-800 transition duration-300">159-8667-2052</a></div></div><div class="contact-card bg-white p-8 rounded-xl border border-gray-100 text-center group hover:shadow-2xl hover:border-green-200 transition-all duration-500" data-aos="fade-up" data-aos-delay="200"><div class="contact-icon bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-600 group-hover:scale-110 transition-all duration-500"><i class="fas fa-envelope text-green-600 text-3xl group-hover:text-white transition-colors duration-500"></i></div><h3 class="text-xl font-bold text-gray-800 mb-3">邮箱联系</h3><p class="text-gray-600 mb-4">24小时在线</p><div class="space-y-2"><a href="mailto:<EMAIL>" class="text-green-600 font-semibold hover:text-green-800 transition duration-300"><EMAIL></a></div></div><div class="contact-card bg-white p-8 rounded-xl border border-gray-100 text-center group hover:shadow-2xl hover:border-orange-200 transition-all duration-500" data-aos="fade-up" data-aos-delay="400"><div class="contact-icon bg-orange-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-orange-600 group-hover:scale-110 transition-all duration-500"><i class="fas fa-map-marker-alt text-orange-600 text-3xl group-hover:text-white transition-colors duration-500"></i></div><h3 class="text-xl font-bold text-gray-800 mb-3">公司地址</h3><p class="text-gray-600 mb-4">欢迎实地考察</p><p class="text-orange-600 font-semibold">深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="text-center mb-16"><h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">公司位置</h2><p class="text-gray-600 mt-4 text-lg" data-aos="fade-up" data-aos-delay="400">深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="bg-gray-200 rounded-2xl overflow-hidden shadow-xl" data-aos="fade-up" data-aos-delay="600"><div class="h-96 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center"><div class="text-center"><i class="fas fa-map-marked-alt text-6xl text-blue-600 mb-4"></i><h3 class="text-2xl font-bold text-gray-800 mb-2">地图位置</h3><p class="text-gray-600">深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p><p class="text-sm text-gray-500 mt-2">坐标：114.058232, 22.681899</p></div></div></div></div></section>',3)]))}const Is=_(js,[["render",Fs]]),Ns={name:"PageHeader",props:{title:{type:String,required:!0},description:{type:String,default:""},theme:{type:String,default:"blue",validator:o=>["blue","green","red","purple","yellow","sky"].includes(o)},showExtraDecorations:{type:Boolean,default:!1}},setup(o){return{themeClass:A(()=>{const r={blue:"bg-gradient-to-r from-blue-600 to-blue-800",green:"bg-gradient-to-r from-green-600 to-emerald-600",red:"bg-gradient-to-r from-red-600 to-pink-600",purple:"bg-gradient-to-r from-purple-700 to-violet-600",yellow:"bg-gradient-to-r from-yellow-500 to-orange-500",sky:"bg-gradient-to-r from-sky-500 to-blue-600"};return r[o.theme]||r.blue})}}},Ss={class:"container-custom relative z-10"},As={class:"text-center"},Ms={class:"text-4xl md:text-6xl font-bold mb-4 animate-bounce-in","data-aos":"fade-up"},Ks={key:0,class:"text-xl md:text-2xl animate-slide-up","data-aos":"fade-up","data-aos-delay":"200"},Ts={key:0,class:"absolute top-1/2 left-1/4 w-12 h-12 bg-white opacity-5 rounded-full animate-pulse-slow"},Ds={key:1,class:"absolute top-1/4 right-1/3 w-8 h-8 bg-white opacity-10 rounded-full animate-wiggle"};function zs(o,t,r,a,g,x){return u(),p("section",{class:M(["page-header",a.themeClass])},[t[0]||(t[0]=e("div",{class:"absolute inset-0 bg-black opacity-10"},null,-1)),e("div",Ss,[e("div",As,[e("h1",Ms,v(r.title),1),r.description?(u(),p("p",Ks,v(r.description),1)):h("",!0)])]),t[1]||(t[1]=e("div",{class:"absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-float"},null,-1)),t[2]||(t[2]=e("div",{class:"absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-float",style:{"animation-delay":"1s"}},null,-1)),r.showExtraDecorations?(u(),p("div",Ts)):h("",!0),r.showExtraDecorations?(u(),p("div",Ds)):h("",!0)],2)}const Rs=_(Ns,[["render",zs]]),Ws={name:"AboutNavigation",setup(){const o=X();return{navigationItems:[{name:"公司简介",path:"/about/profile",icon:"fas fa-building"},{name:"企业介绍",path:"/about/introduction",icon:"fas fa-info-circle"},{name:"发展历程",path:"/about/history",icon:"fas fa-history"},{name:"企业团队",path:"/about/team",icon:"fas fa-users"},{name:"企业荣誉",path:"/about/honors",icon:"fas fa-award"},{name:"招贤纳士",path:"/about/careers",icon:"fas fa-briefcase"}],isActive:a=>o.path===a}}},Es={class:"py-12 bg-gray-50"},Ls={class:"container mx-auto px-4"},Ys={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-up"};function Bs(o,t,r,a,g,x){const s=$("router-link");return u(),p("section",Es,[e("div",Ls,[e("div",Ys,[(u(!0),p(R,null,W(a.navigationItems,l=>(u(),Q(s,{key:l.path,to:l.path,class:M(["filter-btn px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300",a.isActive(l.path)?"bg-blue-600 text-white":"bg-white text-gray-600 hover:text-blue-600"])},{default:c(()=>[e("i",{class:M([l.icon,"mr-2"])},null,2),i(" "+v(l.name),1)]),_:2},1032,["to","class"]))),128))])])])}const nt=_(Ws,[["render",Bs]]),qs={name:"DevelopmentHistory",components:{PageHeader:Rs,AboutNavigation:nt}},Gs={class:"development-history"};function Hs(o,t,r,a,g,x){return u(),p("div",Gs,t[0]||(t[0]=[f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">发展历程</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">见证优卡特20年的成长足迹</p></div></div></section><section class="py-20 bg-white"><div class="container-custom"><div class="text-center mb-16" data-aos="fade-up"><h2 class="text-4xl font-bold mb-4">20年发展历程</h2><p class="text-xl text-gray-600 max-w-3xl mx-auto"> 从2003年成立至今，优卡特走过了20年的发展历程，每一个重要节点都见证着我们的成长与进步 </p></div><div class="relative"><div class="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-blue-200 hidden lg:block"></div><div class="space-y-12"><div class="flex flex-col lg:flex-row items-center" data-aos="fade-right"><div class="w-full lg:w-1/2 lg:pr-8 lg:text-right mb-8 lg:mb-0"><div class="bg-white p-8 rounded-2xl shadow-lg border-l-4 border-blue-600"><div class="text-3xl font-bold text-blue-600 mb-2">2003年</div><h3 class="text-2xl font-bold mb-4">公司成立</h3><p class="text-gray-600 leading-relaxed"> 深圳市优卡特实业有限公司正式成立，开始专注于一卡通设备的研发与生产。 初期团队仅有10人，在深圳一个小型厂房开始了创业之路。 </p><div class="mt-4 flex flex-wrap gap-2"><span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">公司成立</span><span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">团队组建</span></div></div></div><div class="w-12 h-12 bg-blue-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block"></div><div class="w-full lg:w-1/2 lg:pl-8"><img src="https://placehold.co/400x300" alt="2003年公司成立" class="w-full h-auto rounded-xl shadow-lg"></div></div><div class="flex flex-col lg:flex-row items-center" data-aos="fade-left"><div class="w-full lg:w-1/2 lg:pr-8 order-2 lg:order-1"><img src="https://placehold.co/400x300" alt="2005年首款产品发布" class="w-full h-auto rounded-xl shadow-lg"></div><div class="w-12 h-12 bg-green-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block order-2"></div><div class="w-full lg:w-1/2 lg:pl-8 mb-8 lg:mb-0 order-1 lg:order-3"><div class="bg-white p-8 rounded-2xl shadow-lg border-l-4 border-green-600"><div class="text-3xl font-bold text-green-600 mb-2">2005年</div><h3 class="text-2xl font-bold mb-4">首款产品发布</h3><p class="text-gray-600 leading-relaxed"> 成功研发并发布第一款IC卡消费机，标志着优卡特正式进入一卡通设备市场。 产品一经推出就获得了市场的认可，为公司后续发展奠定了基础。 </p><div class="mt-4 flex flex-wrap gap-2"><span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">产品发布</span><span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">市场认可</span></div></div></div></div><div class="flex flex-col lg:flex-row items-center" data-aos="fade-right"><div class="w-full lg:w-1/2 lg:pr-8 lg:text-right mb-8 lg:mb-0"><div class="bg-white p-8 rounded-2xl shadow-lg border-l-4 border-purple-600"><div class="text-3xl font-bold text-purple-600 mb-2">2008年</div><h3 class="text-2xl font-bold mb-4">技术突破</h3><p class="text-gray-600 leading-relaxed"> 成功研发第一代人脸识别消费机，获得多项技术专利。 这一技术突破使优卡特在行业中确立了技术领先地位。 </p><div class="mt-4 flex flex-wrap gap-2 lg:justify-end"><span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">人脸识别</span><span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">技术专利</span></div></div></div><div class="w-12 h-12 bg-purple-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block"></div><div class="w-full lg:w-1/2 lg:pl-8"><img src="https://placehold.co/400x300" alt="2008年技术突破" class="w-full h-auto rounded-xl shadow-lg"></div></div><div class="flex flex-col lg:flex-row items-center" data-aos="fade-left"><div class="w-full lg:w-1/2 lg:pr-8 order-2 lg:order-1"><img src="https://placehold.co/400x300" alt="2012年规模扩张" class="w-full h-auto rounded-xl shadow-lg"></div><div class="w-12 h-12 bg-orange-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block order-2"></div><div class="w-full lg:w-1/2 lg:pl-8 mb-8 lg:mb-0 order-1 lg:order-3"><div class="bg-white p-8 rounded-2xl shadow-lg border-l-4 border-orange-600"><div class="text-3xl font-bold text-orange-600 mb-2">2012年</div><h3 class="text-2xl font-bold mb-4">规模扩张</h3><p class="text-gray-600 leading-relaxed"> 公司迁入新的生产基地，员工规模扩大到100人。 产品线不断丰富，开始涉足门禁、考勤等多个领域。 </p><div class="mt-4 flex flex-wrap gap-2"><span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">规模扩张</span><span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">产品多元化</span></div></div></div></div><div class="flex flex-col lg:flex-row items-center" data-aos="fade-right"><div class="w-full lg:w-1/2 lg:pr-8 lg:text-right mb-8 lg:mb-0"><div class="bg-white p-8 rounded-2xl shadow-lg border-l-4 border-red-600"><div class="text-3xl font-bold text-red-600 mb-2">2015年</div><h3 class="text-2xl font-bold mb-4">市场拓展</h3><p class="text-gray-600 leading-relaxed"> 产品销售网络覆盖全国，开始进军海外市场。 与多家知名企业建立战略合作关系，品牌影响力不断提升。 </p><div class="mt-4 flex flex-wrap gap-2 lg:justify-end"><span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm">全国覆盖</span><span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">海外市场</span></div></div></div><div class="w-12 h-12 bg-red-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block"></div><div class="w-full lg:w-1/2 lg:pl-8"><img src="https://placehold.co/400x300" alt="2015年市场拓展" class="w-full h-auto rounded-xl shadow-lg"></div></div><div class="flex flex-col lg:flex-row items-center" data-aos="fade-left"><div class="w-full lg:w-1/2 lg:pr-8 order-2 lg:order-1"><img src="https://placehold.co/400x300" alt="2018年智能升级" class="w-full h-auto rounded-xl shadow-lg"></div><div class="w-12 h-12 bg-indigo-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block order-2"></div><div class="w-full lg:w-1/2 lg:pl-8 mb-8 lg:mb-0 order-1 lg:order-3"><div class="bg-white p-8 rounded-2xl shadow-lg border-l-4 border-indigo-600"><div class="text-3xl font-bold text-indigo-600 mb-2">2018年</div><h3 class="text-2xl font-bold mb-4">智能升级</h3><p class="text-gray-600 leading-relaxed"> 全面拥抱人工智能技术，推出新一代智能设备。 产品集成了更先进的算法和更强大的处理能力。 </p><div class="mt-4 flex flex-wrap gap-2"><span class="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm">人工智能</span><span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">智能升级</span></div></div></div></div><div class="flex flex-col lg:flex-row items-center" data-aos="fade-right"><div class="w-full lg:w-1/2 lg:pr-8 lg:text-right mb-8 lg:mb-0"><div class="bg-white p-8 rounded-2xl shadow-lg border-l-4 border-cyan-600"><div class="text-3xl font-bold text-cyan-600 mb-2">2020年</div><h3 class="text-2xl font-bold mb-4">云平台发布</h3><p class="text-gray-600 leading-relaxed"> 推出脸爱云平台，实现设备智能化管理和大数据分析。 为客户提供更加完整的解决方案和服务体验。 </p><div class="mt-4 flex flex-wrap gap-2 lg:justify-end"><span class="bg-cyan-100 text-cyan-800 px-3 py-1 rounded-full text-sm">云平台</span><span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">大数据</span></div></div></div><div class="w-12 h-12 bg-cyan-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block"></div><div class="w-full lg:w-1/2 lg:pl-8"><img src="https://placehold.co/400x300" alt="2020年云平台发布" class="w-full h-auto rounded-xl shadow-lg"></div></div><div class="flex flex-col lg:flex-row items-center" data-aos="fade-left"><div class="w-full lg:w-1/2 lg:pr-8 order-2 lg:order-1"><img src="https://placehold.co/400x300" alt="2023年持续创新" class="w-full h-auto rounded-xl shadow-lg"></div><div class="w-12 h-12 bg-yellow-600 rounded-full border-4 border-white shadow-lg z-10 hidden lg:block order-2"></div><div class="w-full lg:w-1/2 lg:pl-8 mb-8 lg:mb-0 order-1 lg:order-3"><div class="bg-white p-8 rounded-2xl shadow-lg border-l-4 border-yellow-600"><div class="text-3xl font-bold text-yellow-600 mb-2">2023年</div><h3 class="text-2xl font-bold mb-4">持续创新</h3><p class="text-gray-600 leading-relaxed"> 荣获行业创新奖，继续引领智能一卡通技术发展。 员工规模达到200人，服务客户超过1000家。 </p><div class="mt-4 flex flex-wrap gap-2"><span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">行业创新奖</span><span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">1000+客户</span></div></div></div></div></div></div></div></section><section class="py-20 bg-blue-600 text-white"><div class="container-custom"><div class="text-center mb-16" data-aos="fade-up"><h2 class="text-4xl font-bold mb-4">20年发展成果</h2><p class="text-xl text-blue-100">用数字见证优卡特的成长历程</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div class="text-center" data-aos="fade-up" data-aos-delay="100"><div class="text-5xl font-bold mb-2">20</div><div class="text-blue-100">年发展历程</div></div><div class="text-center" data-aos="fade-up" data-aos-delay="200"><div class="text-5xl font-bold mb-2">200+</div><div class="text-blue-100">专业员工</div></div><div class="text-center" data-aos="fade-up" data-aos-delay="300"><div class="text-5xl font-bold mb-2">1000+</div><div class="text-blue-100">合作客户</div></div><div class="text-center" data-aos="fade-up" data-aos-delay="400"><div class="text-5xl font-bold mb-2">30+</div><div class="text-blue-100">技术专利</div></div></div></div></section>',3)]))}const Os=_(qs,[["render",Hs]]),Us={name:"TeamPage",components:{AboutNavigation:nt}};function Vs(o,t,r,a,g,x){return u(),p("div",null,t[0]||(t[0]=[f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">企业团队</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">认识优卡特的核心管理团队与技术专家</p></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><div class="team-card bg-white rounded-lg overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500" data-aos="fade-up"><div class="relative overflow-hidden"><img src="https://placehold.co/400x400/3B82F6/FFFFFF?text=业务部团队" alt="业务部团队" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500"></div><div class="p-6"><h3 class="text-xl font-bold text-gray-800 mb-2">业务部</h3><p class="text-blue-600 font-semibold mb-3">市场开拓与销售管理</p><p class="text-gray-600 text-sm">负责市场开拓、客户关系维护、销售管理等工作，建立了覆盖全国的销售网络，为公司业务拓展提供强有力的支撑。</p></div></div><div class="team-card bg-white rounded-lg overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500" data-aos="fade-up" data-aos-delay="200"><div class="relative overflow-hidden"><img src="https://placehold.co/400x400/10B981/FFFFFF?text=研发部团队" alt="研发部团队" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500"></div><div class="p-6"><h3 class="text-xl font-bold text-gray-800 mb-2">研发部</h3><p class="text-green-600 font-semibold mb-3">产品创新与技术研发</p><p class="text-gray-600 text-sm">专注于产品创新和技术研发，负责新产品设计、核心算法开发、技术方案制定，推动公司技术创新和产品升级。</p></div></div><div class="team-card bg-white rounded-lg overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500" data-aos="fade-up" data-aos-delay="400"><div class="relative overflow-hidden"><img src="https://placehold.co/400x400/8B5CF6/FFFFFF?text=技术部团队" alt="技术部团队" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500"></div><div class="p-6"><h3 class="text-xl font-bold text-gray-800 mb-2">技术部</h3><p class="text-purple-600 font-semibold mb-3">系统运维与技术支持</p><p class="text-gray-600 text-sm">负责系统运维、技术支持、产品实施等工作，确保产品稳定运行，为客户提供专业的技术服务和解决方案。</p></div></div></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="text-center mb-16"><h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">团队文化</h2><div class="w-24 h-1 bg-blue-600 mx-auto" data-aos="fade-up" data-aos-delay="200"></div></div><div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-start"><div data-aos="fade-right" class="flex flex-col"><h3 class="text-3xl font-bold text-gray-800 mb-4 leading-tight">协作·创新·成长</h3><p class="text-gray-600 mb-4 text-lg leading-relaxed"> 我们相信每一位团队成员都是公司最宝贵的财富。在优卡特，我们营造开放包容的工作环境，鼓励创新思维，支持个人成长。 </p><p class="text-gray-600 mb-6 text-lg leading-relaxed"> 通过定期的技术分享、团队建设活动和培训计划，我们不断提升团队凝聚力和专业能力，共同创造优异的成果。 </p><div class="grid grid-cols-2 gap-6"><div class="bg-blue-50 p-4 rounded-lg"><h4 class="text-lg font-bold text-blue-600 mb-2">200+</h4><p class="text-gray-600">团队成员</p></div><div class="bg-green-50 p-4 rounded-lg"><h4 class="text-lg font-bold text-green-600 mb-2">85%</h4><p class="text-gray-600">本科以上学历</p></div><div class="bg-orange-50 p-4 rounded-lg"><h4 class="text-lg font-bold text-orange-600 mb-2">60%</h4><p class="text-gray-600">技术研发人员</p></div><div class="bg-purple-50 p-4 rounded-lg"><h4 class="text-lg font-bold text-purple-600 mb-2">5年</h4><p class="text-gray-600">平均工作经验</p></div></div></div><div data-aos="fade-left" class="flex flex-col items-start"><img src="https://placehold.co/600x400" alt="团队合作办公场景" class="w-full rounded-lg shadow-lg mb-6"><img src="https://placehold.co/600x300" alt="团队建设活动" class="w-full rounded-lg shadow-lg"></div></div></div></section>',3)]))}const Qs=_(Us,[["render",Vs]]),Xs={name:"HonorsPage",components:{AboutNavigation:nt},setup(){const o=m(null),t=m({patents:0,awards:0,certifications:0}),r=()=>{const a=new IntersectionObserver(g=>{g.forEach(x=>{if(x.isIntersecting){let s=0,l=0,d=0;const w=setInterval(()=>{s+=50/50,s>=50&&(s=50,clearInterval(w)),t.value.patents=Math.floor(s)},30),j=setInterval(()=>{l+=20/50,l>=20&&(l=20,clearInterval(j)),t.value.awards=Math.floor(l)},30),C=setInterval(()=>{d+=15/50,d>=15&&(d=15,clearInterval(C)),t.value.certifications=Math.floor(d)},30);a.unobserve(x.target)}})},{threshold:.5});o.value&&a.observe(o.value)};return F(()=>{r()}),{honorsStatsSection:o,honorsStats:t}}},Js={class:"py-20 bg-white",ref:"honorsStatsSection"},Zs={class:"container mx-auto px-4"},to={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"},eo={class:"text-center","data-aos":"fade-up"},ao={class:"text-4xl font-bold text-blue-600 mb-2"},so={class:"text-center","data-aos":"fade-up","data-aos-delay":"200"},oo={class:"text-4xl font-bold text-blue-600 mb-2"},lo={class:"text-center","data-aos":"fade-up","data-aos-delay":"400"},ro={class:"text-4xl font-bold text-blue-600 mb-2"};function io(o,t,r,a,g,x){return u(),p("div",null,[t[7]||(t[7]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">企业荣誉</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">展示优卡特获得的各项认证、奖项与资质</p></div></div></section><section class="py-20 bg-gray-50"><div class="container mx-auto px-4"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><div class="certificate-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up"><div class="text-center"><img src="https://placehold.co/200x150" alt="ISO9001质量管理体系认证" class="mx-auto mb-4 rounded"><h3 class="text-xl font-bold text-gray-800 mb-2">ISO9001认证</h3><p class="text-gray-600">质量管理体系认证，确保产品质量稳定可靠</p></div></div><div class="certificate-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="200"><div class="text-center"><img src="https://placehold.co/200x150" alt="高新技术企业认证" class="mx-auto mb-4 rounded"><h3 class="text-xl font-bold text-gray-800 mb-2">高新技术企业</h3><p class="text-gray-600">国家级高新技术企业认证，技术实力获得权威认可</p></div></div><div class="certificate-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="400"><div class="text-center"><img src="https://placehold.co/200x150" alt="3C认证" class="mx-auto mb-4 rounded"><h3 class="text-xl font-bold text-gray-800 mb-2">3C认证</h3><p class="text-gray-600">中国强制性产品认证，产品安全性得到保障</p></div></div><div class="certificate-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up"><div class="text-center"><img src="https://placehold.co/200x150" alt="CE认证" class="mx-auto mb-4 rounded"><h3 class="text-xl font-bold text-gray-800 mb-2">CE认证</h3><p class="text-gray-600">欧盟安全认证标志，产品符合欧盟安全标准</p></div></div><div class="certificate-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="200"><div class="text-center"><img src="https://placehold.co/200x150" alt="FCC认证" class="mx-auto mb-4 rounded"><h3 class="text-xl font-bold text-gray-800 mb-2">FCC认证</h3><p class="text-gray-600">美国联邦通信委员会认证，电磁兼容性达标</p></div></div><div class="certificate-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="400"><div class="text-center"><img src="https://placehold.co/200x150" alt="软件著作权" class="mx-auto mb-4 rounded"><h3 class="text-xl font-bold text-gray-800 mb-2">软件著作权</h3><p class="text-gray-600">多项软件著作权证书，保护知识产权</p></div></div></div></div></section>',2)),e("section",Js,[e("div",Zs,[e("div",to,[e("div",eo,[t[0]||(t[0]=e("div",{class:"bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4"},[e("i",{class:"fas fa-certificate text-blue-600 text-3xl"})],-1)),e("div",ao,v(a.honorsStats.patents)+"+",1),t[1]||(t[1]=e("p",{class:"text-xl text-gray-600"},"技术专利",-1))]),e("div",so,[t[2]||(t[2]=e("div",{class:"bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4"},[e("i",{class:"fas fa-award text-blue-600 text-3xl"})],-1)),e("div",oo,v(a.honorsStats.awards)+"+",1),t[3]||(t[3]=e("p",{class:"text-xl text-gray-600"},"行业奖项",-1))]),e("div",lo,[t[4]||(t[4]=e("div",{class:"bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4"},[e("i",{class:"fas fa-shield-alt text-blue-600 text-3xl"})],-1)),e("div",ro,v(a.honorsStats.certifications)+"+",1),t[5]||(t[5]=e("p",{class:"text-xl text-gray-600"},"资质认证",-1))]),t[6]||(t[6]=f('<div class="text-center" data-aos="fade-up" data-aos-delay="600"><div class="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4"><i class="fas fa-star text-blue-600 text-3xl"></i></div><div class="text-4xl font-bold text-blue-600 mb-2">AAA</div><p class="text-xl text-gray-600">信用等级</p></div>',1))])])],512),t[8]||(t[8]=f('<section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="text-center mb-16"><h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">行业奖项</h2><div class="w-24 h-1 bg-blue-600 mx-auto" data-aos="fade-up" data-aos-delay="200"></div><p class="text-gray-600 mt-4 text-lg" data-aos="fade-up" data-aos-delay="400">获得行业权威机构颁发的多项荣誉奖项</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8"><div class="award-card bg-gradient-to-r from-blue-50 to-blue-100 p-8 rounded-lg border-l-4 border-blue-600" data-aos="fade-up"><div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center"><i class="fas fa-trophy text-white text-2xl"></i></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-800 mb-2">2023年度科技创新奖</h3><p class="text-gray-600 mb-2">中国智能一卡通行业协会颁发</p><p class="text-sm text-gray-500">表彰在人脸识别技术创新方面的突出贡献</p></div></div></div><div class="award-card bg-gradient-to-r from-green-50 to-green-100 p-8 rounded-lg border-l-4 border-green-600" data-aos="fade-up" data-aos-delay="200"><div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center"><i class="fas fa-medal text-white text-2xl"></i></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-800 mb-2">优秀企业奖</h3><p class="text-gray-600 mb-2">深圳市高新技术产业协会颁发</p><p class="text-sm text-gray-500">在高新技术产业发展中表现突出</p></div></div></div><div class="award-card bg-gradient-to-r from-orange-50 to-orange-100 p-8 rounded-lg border-l-4 border-orange-600" data-aos="fade-up"><div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center"><i class="fas fa-star text-white text-2xl"></i></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-800 mb-2">最佳产品设计奖</h3><p class="text-gray-600 mb-2">中国工业设计协会颁发</p><p class="text-sm text-gray-500">产品设计获得专业认可和用户好评</p></div></div></div><div class="award-card bg-gradient-to-r from-purple-50 to-purple-100 p-8 rounded-lg border-l-4 border-purple-600" data-aos="fade-up" data-aos-delay="200"><div class="flex items-start space-x-4"><div class="flex-shrink-0"><div class="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center"><i class="fas fa-crown text-white text-2xl"></i></div></div><div class="flex-1"><h3 class="text-xl font-bold text-gray-800 mb-2">行业领军企业</h3><p class="text-gray-600 mb-2">中国安防行业协会认定</p><p class="text-sm text-gray-500">在智能安防领域的领导地位得到认可</p></div></div></div></div></div></section><section class="py-20 bg-gray-50"><div class="container mx-auto px-4"><div class="text-center mb-16"><h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">技术专利</h2><div class="w-24 h-1 bg-blue-600 mx-auto" data-aos="fade-up" data-aos-delay="200"></div><p class="text-gray-600 mt-4 text-lg" data-aos="fade-up" data-aos-delay="400">拥有多项自主知识产权，技术创新能力持续提升</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><div class="patent-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up"><div class="text-center"><div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4"><i class="fas fa-lightbulb text-blue-600 text-2xl"></i></div><h3 class="text-lg font-bold text-gray-800 mb-2">发明专利</h3><div class="text-3xl font-bold text-blue-600 mb-2">25项</div><p class="text-gray-600 text-sm">核心技术发明专利</p></div></div><div class="patent-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="200"><div class="text-center"><div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"><i class="fas fa-cog text-green-600 text-2xl"></i></div><h3 class="text-lg font-bold text-gray-800 mb-2">实用新型</h3><div class="text-3xl font-bold text-green-600 mb-2">18项</div><p class="text-gray-600 text-sm">实用新型专利</p></div></div><div class="patent-card bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay="400"><div class="text-center"><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4"><i class="fas fa-palette text-orange-600 text-2xl"></i></div><h3 class="text-lg font-bold text-gray-800 mb-2">外观设计</h3><div class="text-3xl font-bold text-orange-600 mb-2">12项</div><p class="text-gray-600 text-sm">外观设计专利</p></div></div></div></div></section>',2))])}const no=_(Xs,[["render",io]]),co={name:"CareersPage",components:{AboutNavigation:nt}},uo={class:"py-20 bg-gray-50"},po={class:"container mx-auto px-4"},go={class:"grid grid-cols-1 md:grid-cols-2 gap-8"},xo={class:"job-card bg-white p-8 rounded-lg border border-gray-100 hover:shadow-lg transition-all duration-300","data-aos":"fade-up"},fo={class:"flex justify-between items-center"},vo={class:"job-card bg-white p-8 rounded-lg border border-gray-100 hover:shadow-lg transition-all duration-300","data-aos":"fade-up","data-aos-delay":"200"},mo={class:"flex justify-between items-center"},bo={class:"job-card bg-white p-8 rounded-lg border border-gray-100 hover:shadow-lg transition-all duration-300","data-aos":"fade-up"},ho={class:"flex justify-between items-center"},yo={class:"job-card bg-white p-8 rounded-lg border border-gray-100 hover:shadow-lg transition-all duration-300","data-aos":"fade-up","data-aos-delay":"200"},wo={class:"flex justify-between items-center"};function _o(o,t,r,a,g,x){return u(),p("div",null,[t[13]||(t[13]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">招贤纳士</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">加入优卡特，共创美好未来</p></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="text-center mb-16"><h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">为什么选择优卡特</h2><div class="w-24 h-1 bg-blue-600 mx-auto" data-aos="fade-up" data-aos-delay="200"></div><p class="text-gray-600 mt-4 text-lg" data-aos="fade-up" data-aos-delay="400">在这里，您将获得更多成长机会和发展空间</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"><div class="benefit-card text-center" data-aos="fade-up"><div class="bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6"><i class="fas fa-rocket text-blue-600 text-3xl"></i></div><h3 class="text-xl font-bold text-gray-800 mb-4">发展前景</h3><p class="text-gray-600">行业领军企业，广阔的职业发展空间和晋升机会</p></div><div class="benefit-card text-center" data-aos="fade-up" data-aos-delay="200"><div class="bg-green-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6"><i class="fas fa-users text-green-600 text-3xl"></i></div><h3 class="text-xl font-bold text-gray-800 mb-4">团队氛围</h3><p class="text-gray-600">开放包容的工作环境，优秀的团队合作氛围</p></div><div class="benefit-card text-center" data-aos="fade-up" data-aos-delay="400"><div class="bg-orange-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6"><i class="fas fa-graduation-cap text-orange-600 text-3xl"></i></div><h3 class="text-xl font-bold text-gray-800 mb-4">学习成长</h3><p class="text-gray-600">完善的培训体系，持续的学习和技能提升机会</p></div><div class="benefit-card text-center" data-aos="fade-up" data-aos-delay="600"><div class="bg-purple-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6"><i class="fas fa-gift text-purple-600 text-3xl"></i></div><h3 class="text-xl font-bold text-gray-800 mb-4">福利待遇</h3><p class="text-gray-600">具有竞争力的薪酬和完善的福利保障体系</p></div></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start"><div data-aos="fade-right"><h2 class="text-5xl font-bold text-gray-800 mb-8">协作·创新·成长</h2><p class="text-2xl text-gray-700 mb-8 leading-relaxed font-medium"> 我们相信每一位团队成员都是公司最宝贵的财富。在优卡特，我们营造开放包容的工作环境，鼓励创新思维，支持个人成长。 </p><p class="text-xl text-gray-600 mb-12 leading-relaxed"> 通过定期的技术分享、团队建设活动和培训计划，我们不断提升团队凝聚力和专业能力，共同创造优异的成果。 </p><div class="grid grid-cols-2 gap-8"><div class="text-center"><div class="text-4xl font-bold text-blue-600 mb-2">200+</div><p class="text-gray-600">团队成员</p></div><div class="text-center"><div class="text-4xl font-bold text-blue-600 mb-2">85%</div><p class="text-gray-600">本科以上学历</p></div><div class="text-center"><div class="text-4xl font-bold text-blue-600 mb-2">60%</div><p class="text-gray-600">技术研发人员</p></div><div class="text-center"><div class="text-4xl font-bold text-blue-600 mb-2">5年</div><p class="text-gray-600">平均工作经验</p></div></div></div><div data-aos="fade-left"><img src="https://placehold.co/600x500" alt="团队文化" class="w-full h-auto rounded-2xl shadow-lg"></div></div></div></section>',3)),e("section",uo,[e("div",po,[t[12]||(t[12]=e("div",{class:"text-center mb-16"},[e("h2",{class:"text-4xl font-bold text-gray-800 mb-4","data-aos":"fade-up"},"热招职位"),e("div",{class:"w-24 h-1 bg-blue-600 mx-auto","data-aos":"fade-up","data-aos-delay":"200"}),e("p",{class:"text-gray-600 mt-4 text-lg","data-aos":"fade-up","data-aos-delay":"400"},"我们正在寻找优秀的人才加入我们的团队")],-1)),e("div",go,[e("div",xo,[t[5]||(t[5]=f('<div class="flex justify-between items-start mb-4"><div><h3 class="text-xl font-bold text-gray-800 mb-2">高级软件工程师</h3><p class="text-blue-600 font-semibold">技术部门</p></div><span class="bg-red-100 text-red-600 px-3 py-1 rounded-full text-sm font-semibold">急招</span></div><div class="mb-4"><p class="text-gray-600 mb-2"><i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>深圳市龙华区</p><p class="text-gray-600 mb-2"><i class="fas fa-clock text-blue-600 mr-2"></i>全职</p><p class="text-gray-600 mb-2"><i class="fas fa-graduation-cap text-blue-600 mr-2"></i>本科及以上</p><p class="text-gray-600"><i class="fas fa-briefcase text-blue-600 mr-2"></i>3-5年经验</p></div><div class="mb-4"><h4 class="font-semibold text-gray-800 mb-2">职位要求：</h4><ul class="text-gray-600 text-sm space-y-1"><li>• 熟练掌握Java、C++等编程语言</li><li>• 具备良好的算法和数据结构基础</li><li>• 有人脸识别或图像处理经验优先</li></ul></div>',3)),e("div",fo,[t[4]||(t[4]=e("span",{class:"text-blue-600 font-bold text-lg"},"15K-25K",-1)),e("button",{class:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300",onClick:t[0]||(t[0]=s=>o.$router.push("/contact"))}," 立即咨询 ")])]),e("div",vo,[t[7]||(t[7]=f('<div class="flex justify-between items-start mb-4"><div><h3 class="text-xl font-bold text-gray-800 mb-2">产品经理</h3><p class="text-blue-600 font-semibold">产品部门</p></div><span class="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-semibold">热招</span></div><div class="mb-4"><p class="text-gray-600 mb-2"><i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>深圳市龙华区</p><p class="text-gray-600 mb-2"><i class="fas fa-clock text-blue-600 mr-2"></i>全职</p><p class="text-gray-600 mb-2"><i class="fas fa-graduation-cap text-blue-600 mr-2"></i>本科及以上</p><p class="text-gray-600"><i class="fas fa-briefcase text-blue-600 mr-2"></i>2-4年经验</p></div><div class="mb-4"><h4 class="font-semibold text-gray-800 mb-2">职位要求：</h4><ul class="text-gray-600 text-sm space-y-1"><li>• 具备产品规划和需求分析能力</li><li>• 熟悉用户体验设计和交互设计</li><li>• 有智能硬件产品经验优先</li></ul></div>',3)),e("div",mo,[t[6]||(t[6]=e("span",{class:"text-blue-600 font-bold text-lg"},"12K-20K",-1)),e("button",{class:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300",onClick:t[1]||(t[1]=s=>o.$router.push("/contact"))}," 立即咨询 ")])]),e("div",bo,[t[9]||(t[9]=f('<div class="flex justify-between items-start mb-4"><div><h3 class="text-xl font-bold text-gray-800 mb-2">销售经理</h3><p class="text-blue-600 font-semibold">销售部门</p></div><span class="bg-orange-100 text-orange-600 px-3 py-1 rounded-full text-sm font-semibold">推荐</span></div><div class="mb-4"><p class="text-gray-600 mb-2"><i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>深圳市龙华区</p><p class="text-gray-600 mb-2"><i class="fas fa-clock text-blue-600 mr-2"></i>全职</p><p class="text-gray-600 mb-2"><i class="fas fa-graduation-cap text-blue-600 mr-2"></i>大专及以上</p><p class="text-gray-600"><i class="fas fa-briefcase text-blue-600 mr-2"></i>1-3年经验</p></div><div class="mb-4"><h4 class="font-semibold text-gray-800 mb-2">职位要求：</h4><ul class="text-gray-600 text-sm space-y-1"><li>• 具备良好的沟通和谈判能力</li><li>• 有B2B销售经验，熟悉客户开发</li><li>• 有安防或智能设备销售经验优先</li></ul></div>',3)),e("div",ho,[t[8]||(t[8]=e("span",{class:"text-blue-600 font-bold text-lg"},"8K-15K",-1)),e("button",{class:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300",onClick:t[2]||(t[2]=s=>o.$router.push("/contact"))}," 立即咨询 ")])]),e("div",yo,[t[11]||(t[11]=f('<div class="flex justify-between items-start mb-4"><div><h3 class="text-xl font-bold text-gray-800 mb-2">硬件工程师</h3><p class="text-blue-600 font-semibold">技术部门</p></div><span class="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-semibold">在招</span></div><div class="mb-4"><p class="text-gray-600 mb-2"><i class="fas fa-map-marker-alt text-blue-600 mr-2"></i>深圳市龙华区</p><p class="text-gray-600 mb-2"><i class="fas fa-clock text-blue-600 mr-2"></i>全职</p><p class="text-gray-600 mb-2"><i class="fas fa-graduation-cap text-blue-600 mr-2"></i>本科及以上</p><p class="text-gray-600"><i class="fas fa-briefcase text-blue-600 mr-2"></i>2-5年经验</p></div><div class="mb-4"><h4 class="font-semibold text-gray-800 mb-2">职位要求：</h4><ul class="text-gray-600 text-sm space-y-1"><li>• 熟悉电路设计和PCB布局</li><li>• 具备嵌入式系统开发经验</li><li>• 有消费电子产品开发经验优先</li></ul></div>',3)),e("div",wo,[t[10]||(t[10]=e("span",{class:"text-blue-600 font-bold text-lg"},"12K-22K",-1)),e("button",{class:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition duration-300",onClick:t[3]||(t[3]=s=>o.$router.push("/contact"))}," 立即咨询 ")])])])])]),t[14]||(t[14]=f('<section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="text-center mb-16"><h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">福利待遇</h2><div class="w-24 h-1 bg-blue-600 mx-auto" data-aos="fade-up" data-aos-delay="200"></div><p class="text-gray-600 mt-4 text-lg" data-aos="fade-up" data-aos-delay="400">完善的福利体系，让您工作无后顾之忧</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"><div class="welfare-card bg-blue-50 p-6 rounded-lg text-center" data-aos="fade-up"><i class="fas fa-money-bill-wave text-blue-600 text-4xl mb-4"></i><h3 class="text-xl font-bold text-gray-800 mb-2">薪酬福利</h3><p class="text-gray-600">具有竞争力的薪酬、年终奖金、股权激励</p></div><div class="welfare-card bg-green-50 p-6 rounded-lg text-center" data-aos="fade-up" data-aos-delay="200"><i class="fas fa-shield-alt text-green-600 text-4xl mb-4"></i><h3 class="text-xl font-bold text-gray-800 mb-2">保险保障</h3><p class="text-gray-600">五险一金、补充医疗保险、意外险</p></div><div class="welfare-card bg-orange-50 p-6 rounded-lg text-center" data-aos="fade-up" data-aos-delay="400"><i class="fas fa-calendar-alt text-orange-600 text-4xl mb-4"></i><h3 class="text-xl font-bold text-gray-800 mb-2">假期福利</h3><p class="text-gray-600">带薪年假、生日假、婚假、产假等</p></div><div class="welfare-card bg-purple-50 p-6 rounded-lg text-center" data-aos="fade-up"><i class="fas fa-dumbbell text-purple-600 text-4xl mb-4"></i><h3 class="text-xl font-bold text-gray-800 mb-2">健康关怀</h3><p class="text-gray-600">年度体检、健身房、员工活动室</p></div><div class="welfare-card bg-red-50 p-6 rounded-lg text-center" data-aos="fade-up" data-aos-delay="200"><i class="fas fa-utensils text-red-600 text-4xl mb-4"></i><h3 class="text-xl font-bold text-gray-800 mb-2">生活便利</h3><p class="text-gray-600">免费工作餐、下午茶、班车接送</p></div><div class="welfare-card bg-indigo-50 p-6 rounded-lg text-center" data-aos="fade-up" data-aos-delay="400"><i class="fas fa-book text-indigo-600 text-4xl mb-4"></i><h3 class="text-xl font-bold text-gray-800 mb-2">培训发展</h3><p class="text-gray-600">内外部培训、技能提升、职业规划</p></div></div></div></section><section class="py-20 bg-gray-50"><div class="container mx-auto px-4"><div class="max-w-2xl mx-auto bg-white p-8 rounded-lg shadow-lg" data-aos="fade-up" data-aos-delay="600"><div class="text-center mb-8"><h3 class="text-2xl font-bold text-gray-800 mb-4">投递简历</h3><p class="text-gray-600">请将您的简历发送至我们的邮箱，我们会尽快与您联系</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8"><div class="text-center"><div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"><i class="fas fa-envelope text-blue-600 text-2xl"></i></div><h4 class="font-bold text-gray-800 mb-2">招聘邮箱</h4><a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800 transition duration-300"><EMAIL></a></div><div class="text-center"><div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"><i class="fas fa-phone text-green-600 text-2xl"></i></div><h4 class="font-bold text-gray-800 mb-2">联系电话</h4><a href="tel:159-8667-2052" class="text-green-600 hover:text-green-800 transition duration-300">159-8667-2052</a></div></div></div></div></section>',2))])}const $o=_(co,[["render",_o]]),ko={name:"ProductNavigation",props:{currentCategory:{type:String,default:null},currentProduct:{type:String,default:null}},setup(o){const t=bt();X();const r=m(null),a=m([{id:"android-face",name:"安卓人脸消费机",color:"blue",icon:"fas fa-mobile-alt",products:[{id:"p301-2d-2w",name:"P301-2D-2W台式人脸消费机"}]},{id:"arm-terminal",name:"ARM消费机",color:"green",icon:"fas fa-microchip",products:[{id:"a201-ic-2w",name:"A201-IC-2W ARM消费机"}]},{id:"face-temp-gate",name:"人脸测温通道闸机",color:"purple",icon:"fas fa-thermometer-half",products:[{id:"tg100-stand",name:"YKT-TG100立式测温闸机"}]},{id:"water-electric",name:"水/电控机",color:"orange",icon:"fas fa-tint",products:[{id:"wc100-water",name:"YKT-WC100智能水控机"}]},{id:"access-control",name:"门禁机",color:"red",icon:"fas fa-door-open",products:[]},{id:"campus-shared",name:"校园共享设备",color:"indigo",icon:"fas fa-share-alt",products:[{id:"wm100-washer",name:"YKT-WM100共享洗衣机"}]},{id:"face-cloud",name:"脸爱云平台",color:"cyan",icon:"fas fa-cloud",products:[{id:"basic",name:"脸爱云平台基础版"}]}]),g=s=>{r.value===s?r.value=null:r.value=s},x=(s,l)=>{t.push(`/products/${s}/${l}`)};return dt(()=>o.currentCategory,s=>{s&&r.value!==s&&(r.value=s)},{immediate:!0}),{expandedCategory:r,productCategories:a,toggleCategory:g,navigateToProduct:x}}},Co={class:"bg-white rounded-xl shadow-lg p-6 sticky top-24","data-aos":"fade-right"},Po={class:"space-y-2"},jo=["onClick"],Fo={class:"flex items-center space-x-3"},Io={class:"pl-3 pt-2 space-y-1"},No=["onClick"];function So(o,t,r,a,g,x){return u(),p("div",Co,[t[0]||(t[0]=e("h3",{class:"text-2xl font-bold text-gray-800 mb-6 text-center"},"产品分类",-1)),e("nav",Po,[(u(!0),p(R,null,W(a.productCategories,s=>(u(),p("div",{key:s.id,class:"category-item"},[e("div",{onClick:l=>a.toggleCategory(s.id),class:M(["flex items-center justify-between p-4 rounded-lg cursor-pointer transition-all duration-300 hover:transform hover:translate-x-1 group",r.currentCategory===s.id?`bg-${s.color}-100`:`hover:bg-${s.color}-50`])},[e("div",Fo,[e("span",{class:M(["font-semibold transition-colors duration-300",a.expandedCategory===s.id?`text-${s.color}-700`:r.currentCategory===s.id?`text-${s.color}-700`:`text-gray-700 group-hover:text-${s.color}-600`])},v(s.name),3)]),e("i",{class:M(["fas fa-chevron-down transition-transform duration-300",a.expandedCategory===s.id?"rotate-180":"",a.expandedCategory===s.id?`text-${s.color}-600`:r.currentCategory===s.id?`text-${s.color}-600`:`text-gray-400 group-hover:text-${s.color}-600`])},null,2)],10,jo),e("div",{class:M(["overflow-hidden transition-all duration-300 ease-in-out",a.expandedCategory===s.id?"max-h-96 opacity-100":"max-h-0 opacity-0"])},[e("div",Io,[(u(!0),p(R,null,W(s.products,l=>(u(),p("div",{key:l.id,onClick:d=>a.navigateToProduct(s.id,l.id),class:M(["p-3 rounded-lg cursor-pointer transition-all duration-300 hover:transform hover:translate-x-1 group",r.currentProduct===l.id?`bg-${s.color}-100`:""])},[e("span",{class:M(["font-semibold transition-colors duration-300",r.currentProduct===l.id?`text-${s.color}-700`:`text-gray-700 group-hover:text-${s.color}-500`])},v(l.name),3)],10,No))),128))])],2)]))),128))])])}const O=_(ko,[["render",So],["__scopeId","data-v-00145a59"]]),Ao={name:"AndroidFace",components:{ProductNavigation:O},setup(){const o=X(),t=m(null);return o.params.productId&&(t.value=o.params.productId),F(()=>{N.init({duration:1e3,once:!0})}),{currentProduct:t}}},Mo={class:"py-20 bg-white"},Ko={class:"w-full"},To={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},Do={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},zo={class:"lg:ml-4 xl:ml-8 2xl:ml-16"},Ro={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},Wo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Eo={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up"},Lo={class:"p-6"},Yo={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"200"},Bo={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"400"},qo={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"600"},Go={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-8"},Ho={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up"},Oo={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"200"};function Uo(o,t,r,a,g,x){const s=$("ProductNavigation"),l=$("router-link");return u(),p("div",null,[t[20]||(t[20]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">安卓人脸消费机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">先进人脸识别技术，智能消费新体验</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",Mo,[e("div",Ko,[e("div",To,[e("div",Do,[e("div",zo,[n(s,{"current-category":"android-face","current-product":a.currentProduct},null,8,["current-product"])])]),e("div",Ro,[e("div",Wo,[e("div",Eo,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[0]||(t[0]=d=>o.$router.push("/products/android-face/p301-2d-2w"))},t[6]||(t[6]=[e("img",{src:"https://placehold.co/300x200",alt:"P301-2D-2W台式人脸消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),e("div",Lo,[t[8]||(t[8]=e("h3",{class:"product-title text-xl font-bold text-gray-800 mb-2 transition-colors duration-200 ease-out hover:text-blue-600"},"P301-2D-2W台式",-1)),t[9]||(t[9]=e("p",{class:"text-gray-600 text-sm mb-4"},"人脸支付消费终端",-1)),n(l,{to:"/products/android-face/p301-2d-2w",class:"block w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300 text-center"},{default:c(()=>t[7]||(t[7]=[i(" 查看详情 ")])),_:1,__:[7]})])]),e("div",Yo,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[1]||(t[1]=d=>o.$router.push("/products/android-face/p50-2d-2w"))},t[10]||(t[10]=[e("img",{src:"https://placehold.co/300x200",alt:"P50-2D-2W人脸消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[11]||(t[11]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"P50-2D-2W人脸"),e("p",{class:"text-gray-600 text-sm mb-4"},"人脸支付消费终端"),e("button",{class:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",Bo,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[2]||(t[2]=d=>o.$router.push("/products/android-face/p80-2w"))},t[12]||(t[12]=[e("img",{src:"https://placehold.co/300x200",alt:"P80-2W安卓公交消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[13]||(t[13]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"P80-2W安卓公交"),e("p",{class:"text-gray-600 text-sm mb-4"},"4.3TFT彩屏，分辨率"),e("button",{class:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",qo,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[3]||(t[3]=d=>o.$router.push("/products/android-face/p60s-card"))},t[14]||(t[14]=[e("img",{src:"https://placehold.co/300x200",alt:"P60S安卓刷卡消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[15]||(t[15]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"P60S安卓刷卡"),e("p",{class:"text-gray-600 text-sm mb-4"},"Android 6.0系统"),e("button",{class:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300"}," 查看详情 ")],-1))])]),e("div",Go,[e("div",Ho,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[4]||(t[4]=d=>o.$router.push("/products/android-face/p60s-2w-qr"))},t[16]||(t[16]=[e("img",{src:"https://placehold.co/300x200",alt:"P60S-2W安卓二维码消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[17]||(t[17]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"P60S-2W安卓二维码"),e("p",{class:"text-gray-600 text-sm mb-4"},"Android 6.0系统"),e("button",{class:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",Oo,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[5]||(t[5]=d=>o.$router.push("/products/android-face/p60-2w-qr"))},t[18]||(t[18]=[e("img",{src:"https://placehold.co/300x200",alt:"P60-2W安卓二维码消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[19]||(t[19]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"P60-2W安卓二维码"),e("p",{class:"text-gray-600 text-sm mb-4"},"智能支付，文档支付"),e("button",{class:"w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-300"}," 查看详情 ")],-1))])])])])])])])}const Vo=_(Ao,[["render",Uo]]),Qo={name:"ArmTerminal",components:{ProductNavigation:O},setup(){const o=X(),t=m(null);return o.params.productId&&(t.value=o.params.productId),F(()=>{N.init({duration:1e3,once:!0})}),{currentProduct:t,openProductDetail:x=>{console.log("查看产品详情:",x)},consultNow:x=>{alert(`您正在咨询 ${x}，我们的客服将尽快与您联系！

联系电话：************
微信：youkate2024`)},callPhone:()=>{window.location.href="tel:************"}}}},Xo={class:"py-20 bg-white"},Jo={class:"w-full"},Zo={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},tl={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},el={class:"lg:ml-4 xl:ml-8 2xl:ml-16"},al={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},sl={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},ol={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up"},ll={class:"p-6"},dl={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"200"},rl={class:"p-6"},il={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"400"},nl={class:"p-6"};function cl(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[15]||(t[15]=f('<section class="bg-gradient-to-r from-orange-600 to-orange-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">ARM消费机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">高性能ARM架构，稳定可靠的消费终端</p></div></div></section>',1)),e("section",Xo,[e("div",Jo,[e("div",Zo,[e("div",tl,[e("div",el,[n(s,{"current-category":"arm-terminal","current-product":a.currentProduct},null,8,["current-product"])])]),e("div",al,[e("div",sl,[e("div",ol,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[0]||(t[0]=l=>a.openProductDetail("YKT-ARM100"))},t[6]||(t[6]=[e("img",{src:"https://placehold.co/300x200",alt:"ARM刷卡消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),e("div",ll,[t[7]||(t[7]=e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-ARM100 刷卡消费机",-1)),t[8]||(t[8]=e("p",{class:"text-gray-600 text-sm mb-4"},"ARM架构IC卡消费终端",-1)),e("button",{onClick:t[1]||(t[1]=l=>a.openProductDetail("YKT-ARM100")),class:"w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-300"}," 查看详情 ")])]),e("div",dl,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[2]||(t[2]=l=>a.openProductDetail("YKT-ARM200"))},t[9]||(t[9]=[e("img",{src:"https://placehold.co/300x200",alt:"ARM二维码消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),e("div",rl,[t[10]||(t[10]=e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-ARM200 二维码消费机",-1)),t[11]||(t[11]=e("p",{class:"text-gray-600 text-sm mb-4"},"ARM架构扫码支付终端",-1)),e("button",{onClick:t[3]||(t[3]=l=>a.openProductDetail("YKT-ARM200")),class:"w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-300"}," 查看详情 ")])]),e("div",il,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[4]||(t[4]=l=>a.openProductDetail("YKT-ARM300"))},t[12]||(t[12]=[e("img",{src:"https://placehold.co/300x200",alt:"ARM多功能消费机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),e("div",nl,[t[13]||(t[13]=e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-ARM300 多功能消费机",-1)),t[14]||(t[14]=e("p",{class:"text-gray-600 text-sm mb-4"},"ARM架构多功能终端",-1)),e("button",{onClick:t[5]||(t[5]=l=>a.openProductDetail("YKT-ARM300")),class:"w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-300"}," 查看详情 ")])])])])])])])])}const ul=_(Qo,[["render",cl]]),pl={name:"FaceTempGate",components:{ProductNavigation:O},setup(){const o=X(),t=m(null);return o.params.productId&&(t.value=o.params.productId),F(()=>{N.init({duration:1e3,once:!0})}),{currentProduct:t}}},gl={class:"py-20 bg-white"},xl={class:"w-full"},fl={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},vl={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},ml={class:"lg:ml-4 xl:ml-8 2xl:ml-16"},bl={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},hl={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},yl={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up"},wl={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"200"},_l={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"400"};function $l(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[9]||(t[9]=f('<section class="bg-gradient-to-r from-red-600 to-red-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">人脸测温通道闸机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能测温检测，安全通行管理</p></div></div></section>',1)),e("section",gl,[e("div",xl,[e("div",fl,[e("div",vl,[e("div",ml,[n(s,{"current-category":"face-temp-gate","current-product":a.currentProduct},null,8,["current-product"])])]),e("div",bl,[e("div",hl,[e("div",yl,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[0]||(t[0]=l=>o.$router.push("/products/face-temp-gate/tg100-stand"))},t[3]||(t[3]=[e("img",{src:"https://placehold.co/300x200",alt:"立式测温闸机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[4]||(t[4]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-TG100 立式测温闸机"),e("p",{class:"text-gray-600 text-sm mb-4"},"立式人脸测温通道闸机"),e("button",{class:"w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",wl,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[1]||(t[1]=l=>o.$router.push("/products/face-temp-gate/td200-desktop"))},t[5]||(t[5]=[e("img",{src:"https://placehold.co/300x200",alt:"桌面式测温机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[6]||(t[6]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-TD200 桌面测温机"),e("p",{class:"text-gray-600 text-sm mb-4"},"桌面式人脸测温终端"),e("button",{class:"w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",_l,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[2]||(t[2]=l=>o.$router.push("/products/face-temp-gate/tw300-wall"))},t[7]||(t[7]=[e("img",{src:"https://placehold.co/300x200",alt:"壁挂式测温机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[8]||(t[8]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-TW300 壁挂测温机"),e("p",{class:"text-gray-600 text-sm mb-4"},"壁挂式人脸测温终端"),e("button",{class:"w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition duration-300"}," 查看详情 ")],-1))])])])])])])])}const kl=_(pl,[["render",$l]]),Cl={name:"WaterElectric",components:{ProductNavigation:O},setup(){const o=X(),t=m(null);return o.params.productId&&(t.value=o.params.productId),F(()=>{N.init({duration:1e3,once:!0})}),{currentProduct:t}}},Pl={class:"py-20 bg-white"},jl={class:"w-full"},Fl={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},Il={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},Nl={class:"lg:ml-4 xl:ml-8 2xl:ml-16"},Sl={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},Al={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Ml={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up"},Kl={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"200"},Tl={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"400"};function Dl(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[9]||(t[9]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">水/电控机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能水电管理，节能环保新选择</p></div></div></section>',1)),e("section",Pl,[e("div",jl,[e("div",Fl,[e("div",Il,[e("div",Nl,[n(s,{"current-category":"water-electric","current-product":a.currentProduct},null,8,["current-product"])])]),e("div",Sl,[e("div",Al,[e("div",Ml,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[0]||(t[0]=l=>o.$router.push("/products/water-electric/wc100-water"))},t[3]||(t[3]=[e("img",{src:"https://placehold.co/300x200",alt:"智能水控机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[4]||(t[4]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-WC100 智能水控机"),e("p",{class:"text-gray-600 text-sm mb-4"},"IC卡智能水控终端"),e("button",{class:"w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",Kl,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[1]||(t[1]=l=>o.$router.push("/products/water-electric/ec200-electric"))},t[5]||(t[5]=[e("img",{src:"https://placehold.co/300x200",alt:"智能电控机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[6]||(t[6]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-EC200 智能电控机"),e("p",{class:"text-gray-600 text-sm mb-4"},"IC卡智能电控终端"),e("button",{class:"w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",Tl,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[2]||(t[2]=l=>o.$router.push("/products/water-electric/we300-integrated"))},t[7]||(t[7]=[e("img",{src:"https://placehold.co/300x200",alt:"水电一体机",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[8]||(t[8]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-WE300 水电一体机"),e("p",{class:"text-gray-600 text-sm mb-4"},"水电一体化控制终端"),e("button",{class:"w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition duration-300"}," 查看详情 ")],-1))])])])])])])])}const zl=_(Cl,[["render",Dl]]),Rl={name:"AccessControl",components:{ProductNavigation:O},setup(){const o=X(),t=m(null);return o.params.productId&&(t.value=o.params.productId),F(()=>{N.init({duration:1e3,once:!0})}),{currentProduct:t}}},Wl={class:"py-20 bg-white"},El={class:"w-full"},Ll={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},Yl={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},Bl={class:"lg:ml-4 xl:ml-8 2xl:ml-16"},ql={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},Gl={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Hl={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up"},Ol={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"200"},Ul={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"400"};function Vl(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[9]||(t[9]=f('<section class="bg-gradient-to-r from-indigo-600 to-indigo-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">门禁控制器</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">专业门禁控制和管理设备</p></div></div></section>',1)),e("section",Wl,[e("div",El,[e("div",Ll,[e("div",Yl,[e("div",Bl,[n(s,{"current-category":"access-control","current-product":a.currentProduct},null,8,["current-product"])])]),e("div",ql,[e("div",Gl,[e("div",Hl,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[0]||(t[0]=l=>o.$router.push("/products/access-control/ac100-face"))},t[3]||(t[3]=[e("img",{src:"https://placehold.co/300x200",alt:"人脸识别门禁机",class:"product-image w-full h-full object-cover transition-transform duration-500"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[4]||(t[4]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-AC100 人脸门禁机"),e("p",{class:"text-gray-600 text-sm mb-4"},"人脸识别门禁控制器"),e("button",{class:"w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",Ol,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[1]||(t[1]=l=>o.$router.push("/products/access-control/ac200-ic"))},t[5]||(t[5]=[e("img",{src:"https://placehold.co/300x200",alt:"IC卡门禁机",class:"product-image w-full h-full object-cover transition-transform duration-500"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[6]||(t[6]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-AC200 IC卡门禁机"),e("p",{class:"text-gray-600 text-sm mb-4"},"IC卡门禁控制器"),e("button",{class:"w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",Ul,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[2]||(t[2]=l=>o.$router.push("/products/access-control/ac300-fingerprint"))},t[7]||(t[7]=[e("img",{src:"https://placehold.co/300x200",alt:"指纹门禁机",class:"product-image w-full h-full object-cover transition-transform duration-500"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[8]||(t[8]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-AC300 指纹门禁机"),e("p",{class:"text-gray-600 text-sm mb-4"},"指纹识别门禁控制器"),e("button",{class:"w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition duration-300"}," 查看详情 ")],-1))])])])])])])])}const Ql=_(Rl,[["render",Vl]]),Xl={name:"CampusShared",components:{ProductNavigation:O},setup(){const o=X(),t=m(null);return o.params.productId&&(t.value=o.params.productId),F(()=>{N.init({duration:1e3,once:!0})}),{currentProduct:t}}},Jl={class:"py-20 bg-white"},Zl={class:"w-full"},td={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},ed={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},ad={class:"lg:ml-4 xl:ml-8 2xl:ml-16"},sd={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},od={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},ld={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up"},dd={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"200"},rd={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"400"};function id(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[9]||(t[9]=f('<section class="bg-gradient-to-r from-purple-600 to-purple-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">校园共享设备</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能共享管理，提升校园生活品质</p></div></div></section>',1)),e("section",Jl,[e("div",Zl,[e("div",td,[e("div",ed,[e("div",ad,[n(s,{"current-category":"campus-shared","current-product":a.currentProduct},null,8,["current-product"])])]),e("div",sd,[e("div",od,[e("div",ld,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[0]||(t[0]=l=>o.$router.push("/products/campus-shared/wm100-washer"))},t[3]||(t[3]=[e("img",{src:"https://placehold.co/300x200",alt:"共享洗衣机",class:"product-image w-full h-full object-cover transition-transform duration-500"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[4]||(t[4]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-WM100 共享洗衣机"),e("p",{class:"text-gray-600 text-sm mb-4"},"智能共享洗衣设备"),e("button",{class:"w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",dd,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[1]||(t[1]=l=>o.$router.push("/products/campus-shared/dm200-dryer"))},t[5]||(t[5]=[e("img",{src:"https://placehold.co/300x200",alt:"共享烘干机",class:"product-image w-full h-full object-cover transition-transform duration-500"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[6]||(t[6]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-DM200 共享烘干机"),e("p",{class:"text-gray-600 text-sm mb-4"},"智能共享烘干设备"),e("button",{class:"w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",rd,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[2]||(t[2]=l=>o.$router.push("/products/campus-shared/cp300-charger"))},t[7]||(t[7]=[e("img",{src:"https://placehold.co/300x200",alt:"共享充电桩",class:"product-image w-full h-full object-cover transition-transform duration-500"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[8]||(t[8]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"YKT-CP300 共享充电桩"),e("p",{class:"text-gray-600 text-sm mb-4"},"智能共享充电设备"),e("button",{class:"w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700 transition duration-300"}," 查看详情 ")],-1))])])])])])])])}const nd=_(Xl,[["render",id]]),cd={name:"FaceCloud",components:{ProductNavigation:O},setup(){const o=X(),t=m(null);return o.params.productId&&(t.value=o.params.productId),F(()=>{N.init({duration:1e3,once:!0})}),{currentProduct:t}}},ud={class:"py-20 bg-white"},pd={class:"w-full"},gd={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},xd={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},fd={class:"lg:ml-4 xl:ml-8 2xl:ml-16"},vd={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},md={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},bd={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up"},hd={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"200"},yd={class:"product-card bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-200 ease-out hover:shadow-xl hover:-translate-y-1","data-aos":"fade-up","data-aos-delay":"400"};function wd(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[9]||(t[9]=f('<section class="bg-gradient-to-r from-indigo-600 to-indigo-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">脸爱云平台</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能云端管理，数据驱动决策</p></div></div></section>',1)),e("section",ud,[e("div",pd,[e("div",gd,[e("div",xd,[e("div",fd,[n(s,{"current-category":"face-cloud","current-product":a.currentProduct},null,8,["current-product"])])]),e("div",vd,[e("div",md,[e("div",bd,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[0]||(t[0]=l=>o.$router.push("/products/face-cloud/basic"))},t[3]||(t[3]=[e("img",{src:"https://placehold.co/300x200",alt:"基础版云平台",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[4]||(t[4]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"脸爱云平台 基础版"),e("p",{class:"text-gray-600 text-sm mb-4"},"基础设备管理云平台"),e("button",{class:"w-full bg-cyan-600 text-white py-2 rounded-lg hover:bg-cyan-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",hd,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[1]||(t[1]=l=>o.$router.push("/products/face-cloud/professional"))},t[5]||(t[5]=[e("img",{src:"https://placehold.co/300x200",alt:"专业版云平台",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[6]||(t[6]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"脸爱云平台 专业版"),e("p",{class:"text-gray-600 text-sm mb-4"},"专业级设备管理云平台"),e("button",{class:"w-full bg-cyan-600 text-white py-2 rounded-lg hover:bg-cyan-700 transition duration-300"}," 查看详情 ")],-1))]),e("div",yd,[e("div",{class:"product-image-container h-64 relative overflow-hidden cursor-pointer",onClick:t[2]||(t[2]=l=>o.$router.push("/products/face-cloud/enterprise"))},t[7]||(t[7]=[e("img",{src:"https://placehold.co/300x200",alt:"企业版云平台",class:"product-image w-full h-full object-cover"},null,-1),e("div",{class:"image-overlay"},[e("span",null,[e("i",{class:"fas fa-eye mr-2"}),i("查看详情")])],-1)])),t[8]||(t[8]=e("div",{class:"p-6"},[e("h3",{class:"text-xl font-bold text-gray-800 mb-2"},"脸爱云平台 企业版"),e("p",{class:"text-gray-600 text-sm mb-4"},"企业级设备管理云平台"),e("button",{class:"w-full bg-cyan-600 text-white py-2 rounded-lg hover:bg-cyan-700 transition duration-300"}," 查看详情 ")],-1))])])])])])])])}const _d=_(cd,[["render",wd]]),$d={name:"AndroidFaceDetail",setup(){const o=m(0),t=m(!1),r=m(0),a=m(0),g=m(null),x=m(null),s=m([{full:"https://placehold.co/800x600/4F46E5/FFFFFF?text=正面视图",thumb:"https://placehold.co/100x100/4F46E5/FFFFFF?text=正面",alt:"正面视图"},{full:"https://placehold.co/800x600/7C3AED/FFFFFF?text=侧面视图",thumb:"https://placehold.co/100x100/7C3AED/FFFFFF?text=侧面",alt:"侧面视图"},{full:"https://placehold.co/800x600/059669/FFFFFF?text=背面视图",thumb:"https://placehold.co/100x100/059669/FFFFFF?text=背面",alt:"背面视图"},{full:"https://placehold.co/800x600/DC2626/FFFFFF?text=接口视图",thumb:"https://placehold.co/100x100/DC2626/FFFFFF?text=接口",alt:"接口视图"}]),l=A(()=>{var k;return((k=s.value[o.value])==null?void 0:k.full)||""}),d=k=>{o.value=k},w=k=>{if(!g.value)return;const b=g.value.getBoundingClientRect();r.value=k.clientX-b.left,a.value=k.clientY-b.top,t.value=!0},j=()=>{t.value=!1},C=A(()=>({width:"100px",height:"100px",left:`${r.value-100/2}px`,top:`${a.value-100/2}px`})),y=A(()=>t.value?{transform:"scale(1.1)"}:{}),K=A(()=>{if(!t.value||!g.value)return{};const k=g.value.getBoundingClientRect(),b=2,z=-(r.value*b-128),E=-(a.value*b-128);return{backgroundImage:`url(${l.value})`,backgroundPosition:`${z}px ${E}px`,backgroundSize:`${k.width*b}px ${k.height*b}px`}});return F(()=>{N.init({duration:1e3,once:!0})}),{currentImageIndex:o,currentImage:l,productImages:s,showMagnifier:t,mouseX:r,mouseY:a,mainImageContainer:g,mainImage:x,setCurrentImage:d,handleMouseMove:w,handleMouseLeave:j,magnifierStyle:C,imageStyle:y,magnifiedImageStyle:K}}},kd={class:"py-20 bg-white"},Cd={class:"container mx-auto px-4"},Pd={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},jd={class:"lg:col-span-1"},Fd={class:"bg-gray-50 rounded-xl p-6 sticky top-24"},Id={class:"space-y-2"},Nd={class:"bg-blue-50 rounded-lg"},Sd={class:"lg:col-span-2"},Ad={class:"bg-white rounded-xl shadow-lg overflow-hidden"},Md={class:"relative"},Kd=["src"],Td={class:"absolute bottom-4 left-4 flex space-x-2"},Dd=["onClick"],zd=["src","alt"],Rd={class:"lg:col-span-1"},Wd={class:"bg-white rounded-xl shadow-lg mb-6 overflow-hidden"},Ed={class:"h-64 bg-gray-100 flex items-center justify-center relative overflow-hidden"},Ld={key:1,class:"text-gray-400 text-center"};function Yd(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[t[14]||(t[14]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-dacf3a0e><div class="absolute inset-0 bg-black opacity-10" data-v-dacf3a0e></div><div class="container mx-auto px-4 relative z-10" data-v-dacf3a0e><div class="text-center" data-v-dacf3a0e><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-dacf3a0e>安卓人脸消费机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-dacf3a0e>智能识别，安全便捷</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-dacf3a0e></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-dacf3a0e></div></section>',1)),e("section",kd,[e("div",Cd,[e("div",Pd,[e("div",jd,[e("div",Fd,[t[10]||(t[10]=e("h3",{class:"text-lg font-bold text-gray-800 mb-4"},"产品分类",-1)),e("nav",Id,[n(s,{to:"/products",class:"flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600"},{default:c(()=>t[2]||(t[2]=[e("i",{class:"fas fa-th-large mr-3"},null,-1),i(" 全部产品 ")])),_:1,__:[2]}),e("div",Nd,[n(s,{to:"/products/android-face-detail",class:"flex items-center p-3 rounded-lg text-blue-600 font-semibold"},{default:c(()=>t[3]||(t[3]=[e("i",{class:"fas fa-mobile-alt mr-3"},null,-1),i(" 安卓人脸消费机 ")])),_:1,__:[3]})]),n(s,{to:"/products/arm-terminal-detail",class:"flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600"},{default:c(()=>t[4]||(t[4]=[e("i",{class:"fas fa-microchip mr-3"},null,-1),i(" ARM消费机 ")])),_:1,__:[4]}),n(s,{to:"/products/access-control-detail",class:"flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600"},{default:c(()=>t[5]||(t[5]=[e("i",{class:"fas fa-door-open mr-3"},null,-1),i(" 门禁控制器 ")])),_:1,__:[5]}),n(s,{to:"/products/face-temp-gate-detail",class:"flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600"},{default:c(()=>t[6]||(t[6]=[e("i",{class:"fas fa-thermometer-half mr-3"},null,-1),i(" 人脸测温闸机 ")])),_:1,__:[6]}),n(s,{to:"/products/face-cloud-detail",class:"flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600"},{default:c(()=>t[7]||(t[7]=[e("i",{class:"fas fa-cloud mr-3"},null,-1),i(" 人脸云端机 ")])),_:1,__:[7]}),n(s,{to:"/products/campus-shared-detail",class:"flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600"},{default:c(()=>t[8]||(t[8]=[e("i",{class:"fas fa-graduation-cap mr-3"},null,-1),i(" 校园共享设备 ")])),_:1,__:[8]}),n(s,{to:"/products/water-electric-detail",class:"flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600"},{default:c(()=>t[9]||(t[9]=[e("i",{class:"fas fa-tint mr-3"},null,-1),i(" 水电控制器 ")])),_:1,__:[9]})])])]),e("div",Sd,[e("div",Ad,[e("div",Md,[e("div",{class:"product-main-image relative overflow-hidden bg-gray-100 cursor-crosshair",onMousemove:t[0]||(t[0]=(...l)=>a.handleMouseMove&&a.handleMouseMove(...l)),onMouseleave:t[1]||(t[1]=(...l)=>a.handleMouseLeave&&a.handleMouseLeave(...l)),ref:"mainImageContainer"},[e("img",{src:a.currentImage,alt:"安卓人脸消费机",class:"w-full h-96 object-cover transition-transform duration-300",style:Z(a.imageStyle),ref:"mainImage"},null,12,Kd),a.showMagnifier?(u(),p("div",{key:0,class:"magnifier-box absolute border-2 border-blue-500 bg-blue-500 bg-opacity-20 pointer-events-none",style:Z(a.magnifierStyle)},null,4)):h("",!0)],544),e("div",Td,[(u(!0),p(R,null,W(a.productImages,(l,d)=>(u(),p("button",{key:d,onClick:w=>a.setCurrentImage(d),class:M(["w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 hover:scale-105",a.currentImageIndex===d?"border-blue-500 shadow-lg":"border-gray-300 hover:border-blue-300"])},[e("img",{src:l.thumb,alt:l.alt,class:"w-full h-full object-cover"},null,8,zd)],10,Dd))),128))])]),t[11]||(t[11]=f('<div class="p-6" data-v-dacf3a0e><div class="flex flex-wrap gap-2 mb-4" data-v-dacf3a0e><span class="bg-red-100 text-red-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-dacf3a0e>热销产品</span><span class="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-dacf3a0e>人脸识别</span><span class="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-dacf3a0e>安卓系统</span></div><h2 class="text-2xl font-bold text-gray-800 mb-3" data-v-dacf3a0e>安卓人脸消费机 P301-2D-2W台式</h2><p class="text-gray-600 leading-relaxed" data-v-dacf3a0e> 采用先进的人脸识别技术，支持离线识别，识别速度快，准确率高。 搭载安卓操作系统，界面友好，操作简单。适用于学校食堂、企业餐厅、 医院等各种消费场景，为用户提供安全便捷的支付体验。 </p></div>',1))])]),e("div",Rd,[e("div",Wd,[e("div",Ed,[a.showMagnifier?(u(),p("div",{key:0,class:"magnified-view absolute inset-0 bg-cover bg-no-repeat transition-all duration-200",style:Z(a.magnifiedImageStyle)},null,4)):(u(),p("div",Ld,t[12]||(t[12]=[e("i",{class:"fas fa-search-plus text-4xl mb-2"},null,-1),e("p",{class:"text-sm"},"悬停图片查看放大效果",-1)])))])]),t[13]||(t[13]=f('<div class="bg-white rounded-xl shadow-lg p-6" data-v-dacf3a0e><h3 class="text-lg font-bold text-gray-800 mb-4" data-v-dacf3a0e>产品参数</h3><div class="space-y-3" data-v-dacf3a0e><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>产品型号</span><span class="font-semibold" data-v-dacf3a0e>P301-2D-2W</span></div><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>操作系统</span><span class="font-semibold" data-v-dacf3a0e>Android 8.1</span></div><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>屏幕尺寸</span><span class="font-semibold" data-v-dacf3a0e>8英寸</span></div><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>识别距离</span><span class="font-semibold" data-v-dacf3a0e>0.3-1.5米</span></div><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>识别速度</span><span class="font-semibold" data-v-dacf3a0e>≤1秒</span></div><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>存储容量</span><span class="font-semibold" data-v-dacf3a0e>3万张人脸</span></div><div class="flex justify-between items-center py-2 border-b border-gray-100" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>工作温度</span><span class="font-semibold" data-v-dacf3a0e>-10℃~60℃</span></div><div class="flex justify-between items-center py-2" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>网络接口</span><span class="font-semibold" data-v-dacf3a0e>WiFi/以太网</span></div></div><div class="mt-6 space-y-3" data-v-dacf3a0e><button class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition duration-300 flex items-center justify-center" data-v-dacf3a0e><i class="fas fa-comments mr-2" data-v-dacf3a0e></i> 立即咨询 </button><button class="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition duration-300 flex items-center justify-center" data-v-dacf3a0e><i class="fas fa-phone mr-2" data-v-dacf3a0e></i> 电话咨询 </button></div></div>',1))])])])]),t[15]||(t[15]=f('<section class="py-20 bg-gray-50" data-v-dacf3a0e><div class="container mx-auto px-4" data-v-dacf3a0e><div class="max-w-6xl mx-auto" data-v-dacf3a0e><div class="text-center mb-12" data-v-dacf3a0e><h2 class="text-3xl font-bold text-gray-800 mb-4" data-aos="fade-up" data-v-dacf3a0e>产品详情</h2><p class="text-gray-600 text-lg" data-aos="fade-up" data-aos-delay="200" data-v-dacf3a0e>深入了解安卓人脸消费机的技术特点和应用优势</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16" data-v-dacf3a0e><div class="bg-white rounded-xl p-6 shadow-lg text-center" data-aos="fade-up" data-v-dacf3a0e><div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-dacf3a0e><i class="fas fa-user-check text-2xl text-blue-600" data-v-dacf3a0e></i></div><h3 class="text-xl font-bold text-gray-800 mb-3" data-v-dacf3a0e>高精度识别</h3><p class="text-gray-600" data-v-dacf3a0e>采用深度学习算法，人脸识别准确率高达99.7%，支持活体检测，防止照片欺骗。</p></div><div class="bg-white rounded-xl p-6 shadow-lg text-center" data-aos="fade-up" data-aos-delay="200" data-v-dacf3a0e><div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-dacf3a0e><i class="fas fa-bolt text-2xl text-green-600" data-v-dacf3a0e></i></div><h3 class="text-xl font-bold text-gray-800 mb-3" data-v-dacf3a0e>极速响应</h3><p class="text-gray-600" data-v-dacf3a0e>识别速度小于1秒，支持离线识别，即使网络断开也能正常工作。</p></div><div class="bg-white rounded-xl p-6 shadow-lg text-center" data-aos="fade-up" data-aos-delay="400" data-v-dacf3a0e><div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-dacf3a0e><i class="fas fa-mobile-alt text-2xl text-purple-600" data-v-dacf3a0e></i></div><h3 class="text-xl font-bold text-gray-800 mb-3" data-v-dacf3a0e>安卓系统</h3><p class="text-gray-600" data-v-dacf3a0e>基于Android 8.1系统，界面友好，支持多种应用扩展和定制开发。</p></div></div><div class="bg-white rounded-xl shadow-lg overflow-hidden mb-16" data-aos="fade-up" data-v-dacf3a0e><div class="bg-blue-600 text-white p-6" data-v-dacf3a0e><h3 class="text-2xl font-bold" data-v-dacf3a0e>技术规格</h3></div><div class="p-6" data-v-dacf3a0e><div class="grid grid-cols-1 md:grid-cols-2 gap-8" data-v-dacf3a0e><div data-v-dacf3a0e><h4 class="text-lg font-bold text-gray-800 mb-4" data-v-dacf3a0e>硬件配置</h4><div class="space-y-3" data-v-dacf3a0e><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>处理器</span><span class="font-semibold" data-v-dacf3a0e>RK3288 四核ARM Cortex-A17</span></div><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>内存</span><span class="font-semibold" data-v-dacf3a0e>2GB DDR3</span></div><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>存储</span><span class="font-semibold" data-v-dacf3a0e>16GB eMMC</span></div><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>摄像头</span><span class="font-semibold" data-v-dacf3a0e>200万像素双目摄像头</span></div><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>显示屏</span><span class="font-semibold" data-v-dacf3a0e>8英寸IPS触摸屏</span></div></div></div><div data-v-dacf3a0e><h4 class="text-lg font-bold text-gray-800 mb-4" data-v-dacf3a0e>功能特性</h4><div class="space-y-3" data-v-dacf3a0e><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>人脸库容量</span><span class="font-semibold" data-v-dacf3a0e>30,000张</span></div><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>识别角度</span><span class="font-semibold" data-v-dacf3a0e>±30°</span></div><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>工作环境</span><span class="font-semibold" data-v-dacf3a0e>-10℃~60℃</span></div><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>网络支持</span><span class="font-semibold" data-v-dacf3a0e>WiFi/以太网/4G(可选)</span></div><div class="flex justify-between" data-v-dacf3a0e><span class="text-gray-600" data-v-dacf3a0e>电源</span><span class="font-semibold" data-v-dacf3a0e>DC 12V/3A</span></div></div></div></div></div></div><div class="text-center mb-12" data-v-dacf3a0e><h3 class="text-2xl font-bold text-gray-800 mb-8" data-aos="fade-up" data-v-dacf3a0e>应用场景</h3><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" data-v-dacf3a0e><div class="bg-white rounded-xl p-6 shadow-lg" data-aos="fade-up" data-v-dacf3a0e><div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-dacf3a0e><i class="fas fa-utensils text-2xl text-blue-600" data-v-dacf3a0e></i></div><h4 class="font-bold text-gray-800 mb-2" data-v-dacf3a0e>学校食堂</h4><p class="text-gray-600 text-sm" data-v-dacf3a0e>提供快速便捷的就餐体验</p></div><div class="bg-white rounded-xl p-6 shadow-lg" data-aos="fade-up" data-aos-delay="200" data-v-dacf3a0e><div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-dacf3a0e><i class="fas fa-building text-2xl text-green-600" data-v-dacf3a0e></i></div><h4 class="font-bold text-gray-800 mb-2" data-v-dacf3a0e>企业餐厅</h4><p class="text-gray-600 text-sm" data-v-dacf3a0e>员工用餐管理更加智能</p></div><div class="bg-white rounded-xl p-6 shadow-lg" data-aos="fade-up" data-aos-delay="400" data-v-dacf3a0e><div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-dacf3a0e><i class="fas fa-hospital text-2xl text-purple-600" data-v-dacf3a0e></i></div><h4 class="font-bold text-gray-800 mb-2" data-v-dacf3a0e>医院餐厅</h4><p class="text-gray-600 text-sm" data-v-dacf3a0e>医护人员快速用餐解决方案</p></div><div class="bg-white rounded-xl p-6 shadow-lg" data-aos="fade-up" data-aos-delay="600" data-v-dacf3a0e><div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4" data-v-dacf3a0e><i class="fas fa-store text-2xl text-orange-600" data-v-dacf3a0e></i></div><h4 class="font-bold text-gray-800 mb-2" data-v-dacf3a0e>商业场所</h4><p class="text-gray-600 text-sm" data-v-dacf3a0e>提升消费体验和管理效率</p></div></div></div></div></div></section>',1))])}const Bd=_($d,[["render",Yd],["__scopeId","data-v-dacf3a0e"]]),qd={name:"ArmTerminalDetail"};function Gd(o,t,r,a,g,x){return u(),p("div",null,t[0]||(t[0]=[f('<section class="bg-gradient-to-r from-green-600 to-green-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl font-bold mb-4">ARM消费机详情</h1><p class="text-xl">高性能处理器，稳定可靠</p></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="text-center"><h2 class="text-3xl font-bold text-gray-800 mb-4">ARM消费机详情</h2><p class="text-gray-600 text-lg">详细产品信息</p></div></div></section>',2)]))}const Hd=_(qd,[["render",Gd]]),Od={name:"AccessControlDetail"};function Ud(o,t,r,a,g,x){return u(),p("div",null,t[0]||(t[0]=[f('<section class="bg-gradient-to-r from-purple-600 to-purple-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl font-bold mb-4">门禁控制器详情</h1><p class="text-xl">智能门禁管理，安全可靠</p></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="text-center"><h2 class="text-3xl font-bold text-gray-800 mb-4">门禁控制器详情</h2><p class="text-gray-600 text-lg">详细产品信息</p></div></div></section>',2)]))}const Vd=_(Od,[["render",Ud]]),Qd={name:"FaceTempGateDetail"};function Xd(o,t,r,a,g,x){return u(),p("div",null,t[0]||(t[0]=[f('<section class="bg-gradient-to-r from-red-600 to-red-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl font-bold mb-4">人脸测温通道闸机详情</h1><p class="text-xl">智能测温，安全防护</p></div></div></section><section class="py-20 bg-white"><div class="container mx-auto px-4"><div class="text-center"><h2 class="text-3xl font-bold text-gray-800 mb-4">人脸测温通道闸机详情</h2><p class="text-gray-600 text-lg">详细产品信息</p></div></div></section>',2)]))}const Jd=_(Qd,[["render",Xd]]),Zd={name:"AndroidFaceP301",components:{ProductNavigation:O},setup(){const o=m("android-face"),t=m("p301-2d-2w"),r=m(0),a=m(!1),g=m(0),x=m(0),s=m(null),l=m(null),d=m([{full:"/images/products/p301-front.png",thumb:"/images/products/p301-front.png",alt:"P301-2D-2W正面视图"},{full:"/images/products/p301-side.png",thumb:"/images/products/p301-side.png",alt:"P301-2D-2W侧面视图"},{full:"/images/products/p301-back.png",thumb:"/images/products/p301-back.png",alt:"P301-2D-2W背面视图"},{full:"/images/products/p301-front.png",thumb:"/images/products/p301-front.png",alt:"P301-2D-2W正面细节"},{full:"/images/products/p301-side.png",thumb:"/images/products/p301-side.png",alt:"P301-2D-2W侧面细节"}]),w=A(()=>{var P;return((P=d.value[r.value])==null?void 0:P.full)||""}),j=P=>{r.value=P},C=()=>{a.value=!0};let y=null,K=0;const k=P=>{!s.value||!a.value||(y&&cancelAnimationFrame(y),y=requestAnimationFrame(I=>{if(I-K>=16){const B=s.value.getBoundingClientRect();g.value=P.clientX-B.left,x.value=P.clientY-B.top,K=I}}))},b=()=>{a.value=!1,y&&(cancelAnimationFrame(y),y=null)},z=A(()=>{if(!s.value)return{};const P=s.value.getBoundingClientRect(),I=Math.min(P.width,P.height)/2,B=P.width-I,L=P.height-I,q=Math.max(0,Math.min(g.value-I/2,B)),st=Math.max(0,Math.min(x.value-I/2,L));return{width:`${I}px`,height:`${I}px`,left:`${q}px`,top:`${st}px`,borderRadius:"6px",transform:"translate3d(0, 0, 0)",willChange:"left, top, transform",boxShadow:"0 0 0 2px rgba(59, 130, 246, 0.8), 0 0 15px rgba(59, 130, 246, 0.3)","--lens-x":q,"--lens-y":st,"--lens-size":I}}),E=A(()=>a.value?{transform:"scale(1.01)"}:{}),at=A(()=>{if(!a.value||!s.value)return{};const P=s.value.getBoundingClientRect(),I=Math.min(P.width,P.height)/2,B=P.width-I,L=P.height-I,q=Math.max(0,Math.min(g.value-I/2,B)),st=Math.max(0,Math.min(x.value-I/2,L)),ot=P.width/I,lt=P.height/I,rt=P.width*ot,it=P.height*lt,ct=-q*ot,ut=-st*lt;return{backgroundImage:`url(${w.value})`,backgroundPosition:`${ct}px ${ut}px`,backgroundSize:`${rt}px ${it}px`,backgroundRepeat:"no-repeat",transform:"translate3d(0, 0, 0)",willChange:"background-position",transition:"none",filter:"contrast(1.03) brightness(1.01)",imageRendering:"crisp-edges"}}),S=m(null),Y=()=>{if(!S.value)return;const P=()=>{const I=document.querySelector("#product-description-bottom");if(!I)return;const L=I.getBoundingClientRect().bottom+window.scrollY;window.scrollY>L?(S.value.style.opacity="1",S.value.style.transform="translateX(0)"):(S.value.style.opacity="0",S.value.style.transform="translateX(-20px)")};window.addEventListener("scroll",P),P()};return F(()=>{N.init({duration:1e3,once:!0}),setTimeout(()=>{Y()},100)}),Ct(()=>{y&&(cancelAnimationFrame(y),y=null)}),{currentCategory:o,currentProduct:t,currentImageIndex:r,currentImage:w,productImages:d,showMagnifier:a,mouseX:g,mouseY:x,mainImageContainer:s,mainImage:l,setCurrentImage:j,handleMouseEnter:C,handleMouseMove:k,handleMouseLeave:b,lensStyle:z,imageTransform:E,magnifiedStyle:at,floatingNav:S}}},tr={class:"py-20 bg-white"},er={class:"w-full"},ar={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},sr={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},or={class:"lg:ml-4 xl:ml-8 2xl:ml-16",style:{"margin-top":"-1.5rem"}},lr={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},dr={class:"flex lg:flex-row flex-col mb-8",style:{gap:"5px"}},rr={class:"flex-shrink-0",style:{width:"120px"}},ir={class:"flex flex-col justify-between",style:{height:"592px"}},nr=["onClick"],cr=["src","alt"],ur={class:"flex-1"},pr={class:"grid grid-cols-1 lg:grid-cols-2",style:{gap:"5px",height:"592px"}},gr={class:"lg:col-span-1"},xr={class:"bg-white rounded-xl shadow-lg overflow-hidden",style:{height:"592px"}},fr=["src"],vr={class:"lg:col-span-1"},mr={class:"bg-white rounded-xl shadow-lg overflow-hidden relative",style:{height:"592px"}},br={class:"bg-gray-50 relative overflow-hidden",style:{width:"100%",height:"592px"}},hr={ref:"floatingNav",class:"fixed left-1/2 transform -translate-x-1/2 lg:left-20 lg:transform-none top-24 z-40 lg:block hidden transition-all duration-500 ease-in-out",style:{opacity:"0",transform:"translateX(-20px)"}};function yr(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[5]||(t[5]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-ce20f660><div class="absolute inset-0 bg-black opacity-10" data-v-ce20f660></div><div class="container mx-auto px-4 relative z-10" data-v-ce20f660><div class="text-center" data-v-ce20f660><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-ce20f660>P301-2D-2W 台式人脸消费机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-ce20f660>智能识别，安全便捷</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-ce20f660></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-ce20f660></div></section>',1)),e("section",tr,[e("div",er,[e("div",ar,[e("div",sr,[e("div",or,[n(s,{"current-category":a.currentCategory,"current-product":a.currentProduct},null,8,["current-category","current-product"])])]),e("div",lr,[e("div",dr,[e("div",rr,[e("div",ir,[(u(!0),p(R,null,W(a.productImages,(l,d)=>(u(),p("button",{key:d,onClick:w=>a.setCurrentImage(d),class:M(["w-28 h-28 rounded-lg overflow-hidden border-2 transition-all duration-200 hover:shadow-md",a.currentImageIndex===d?"border-blue-500 shadow-lg ring-2 ring-blue-200":"border-gray-300 hover:border-blue-300"])},[e("img",{src:l.thumb,alt:l.alt,class:"w-full h-full object-cover"},null,8,cr)],10,nr))),128))])]),e("div",ur,[e("div",pr,[e("div",gr,[e("div",xr,[e("div",{class:"product-main-image relative overflow-hidden bg-gray-100 cursor-zoom-in",onMousemove:t[0]||(t[0]=(...l)=>a.handleMouseMove&&a.handleMouseMove(...l)),onMouseleave:t[1]||(t[1]=(...l)=>a.handleMouseLeave&&a.handleMouseLeave(...l)),onMouseenter:t[2]||(t[2]=(...l)=>a.handleMouseEnter&&a.handleMouseEnter(...l)),ref:"mainImageContainer",style:{width:"100%",height:"592px"}},[e("img",{src:a.currentImage,alt:"P301-2D-2W台式人脸消费机",class:"w-full h-full object-contain transition-transform duration-75",style:Z(a.imageTransform),ref:"mainImage"},null,12,fr),a.showMagnifier?(u(),p("div",{key:0,class:"magnifier-lens absolute border border-blue-500 pointer-events-none",style:Z(a.lensStyle)},null,4)):h("",!0)],544)])]),e("div",vr,[e("div",mr,[e("div",br,[a.showMagnifier?(u(),p("div",{key:0,class:"magnified-view absolute inset-0 w-full h-full bg-cover bg-no-repeat z-10",style:Z(a.magnifiedStyle)},null,4)):h("",!0),t[3]||(t[3]=f('<div class="absolute inset-0 p-8 flex flex-col justify-start" data-v-ce20f660><div class="flex flex-wrap gap-3 mb-6" data-v-ce20f660><span class="bg-red-100 text-red-600 px-3 py-2 rounded-full text-sm font-semibold shadow-sm" data-v-ce20f660>热销产品</span><span class="bg-blue-100 text-blue-600 px-3 py-2 rounded-full text-sm font-semibold shadow-sm" data-v-ce20f660>人脸识别</span><span class="bg-green-100 text-green-600 px-3 py-2 rounded-full text-sm font-semibold shadow-sm" data-v-ce20f660>安卓系统</span></div><h2 class="text-2xl font-bold text-gray-800 mb-6 leading-tight" data-v-ce20f660>P301-2D-2W 台式人脸消费机</h2><div class="flex-1" data-v-ce20f660><p class="text-gray-700 text-base leading-relaxed mb-4" data-v-ce20f660> 采用先进的人脸识别技术，支持离线识别，识别速度快，准确率高。 </p><p class="text-gray-700 text-base leading-relaxed mb-4" data-v-ce20f660> 搭载安卓操作系统，界面友好，操作简单。 </p><p class="text-gray-700 text-base leading-relaxed" data-v-ce20f660> 适用于学校食堂、企业餐厅、医院等各种消费场景，为用户提供安全便捷的支付体验。 </p></div></div>',1))])])])])])])])])])]),e("div",hr,t[4]||(t[4]=[f('<div class="bg-gray-50 rounded-xl p-6 shadow-lg" data-v-ce20f660><h3 class="text-lg font-bold text-gray-800 mb-4" data-v-ce20f660>页面导航</h3><nav class="space-y-2" data-v-ce20f660><a href="#product-params" class="flex items-center p-3 rounded-lg hover:bg-green-50 transition duration-300 text-gray-600 hover:text-green-600 cursor-pointer" data-v-ce20f660><i class="fas fa-cogs mr-3" data-v-ce20f660></i> 技术规格 </a><a href="#product-details" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600 cursor-pointer" data-v-ce20f660><i class="fas fa-info-circle mr-3" data-v-ce20f660></i> 产品详情 </a></nav></div>',1)]),512),t[6]||(t[6]=f('<section id="product-params" class="py-20 bg-gray-50" data-v-ce20f660><div class="container mx-auto px-4" data-v-ce20f660><div class="grid grid-cols-1 lg:grid-cols-4 gap-8" data-v-ce20f660><div class="lg:col-span-1" data-v-ce20f660></div><div class="lg:col-span-3" data-v-ce20f660><div class="bg-white rounded-xl shadow-lg p-8" data-aos="fade-up" data-v-ce20f660><div class="text-center mb-8" data-v-ce20f660><h2 class="text-3xl font-bold text-gray-800 mb-4" data-aos="fade-up" data-v-ce20f660>技术规格</h2><div class="w-16 h-1 bg-green-600 mx-auto" data-aos="fade-up" data-aos-delay="200" data-v-ce20f660></div></div><h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center" data-v-ce20f660><i class="fas fa-cogs text-green-600 mr-3" data-v-ce20f660></i> 技术参数 </h3><div class="overflow-x-auto" data-v-ce20f660><table class="w-full border-collapse" data-v-ce20f660><tbody data-v-ce20f660><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>产品型号</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>P301-2D-2W</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>操作系统</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>Android 8.1</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>处理器</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>ARM Cortex-A7 四核 1.2GHz</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>内存</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>1GB DDR3</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>存储</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>8GB eMMC</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>屏幕尺寸</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>8英寸 IPS触摸屏</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>分辨率</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>1280×800</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>摄像头</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>200万像素双目摄像头</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>识别距离</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>0.3-1.5米</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>识别速度</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>≤1秒</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>存储容量</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>3万张人脸</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>通讯方式</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>WiFi/以太网/4G(可选)</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>工作温度</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>-10℃~60℃</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>工作湿度</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>10%~90%RH</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>电源</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>DC 12V/3A</td></tr><tr class="hover:bg-gray-50 transition duration-200" data-v-ce20f660><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-ce20f660>产品尺寸</td><td class="py-4 px-6 text-gray-600" data-v-ce20f660>280×180×45mm</td></tr></tbody></table></div></div></div></div></div></section><section id="product-details" class="py-20 bg-white" data-v-ce20f660><div class="container mx-auto px-4" data-v-ce20f660><div class="grid grid-cols-1 lg:grid-cols-4 gap-8" data-v-ce20f660><div class="lg:col-span-1" data-v-ce20f660></div><div class="lg:col-span-3" data-v-ce20f660><div class="text-center mb-8" data-v-ce20f660><h2 class="text-3xl font-bold text-gray-800 mb-4" data-aos="fade-up" data-v-ce20f660>产品详情</h2><div class="w-16 h-1 bg-blue-600 mx-auto" data-aos="fade-up" data-aos-delay="200" data-v-ce20f660></div></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-8" data-v-ce20f660><div class="bg-white rounded-xl shadow-lg p-8" data-aos="fade-up" data-v-ce20f660><h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center" data-v-ce20f660><i class="fas fa-star text-blue-600 mr-3" data-v-ce20f660></i> 核心特点 </h3><div class="space-y-4" data-v-ce20f660><div class="flex items-start" data-v-ce20f660><i class="fas fa-check-circle text-green-500 mr-3 mt-1" data-v-ce20f660></i><div data-v-ce20f660><h4 class="font-semibold text-gray-800" data-v-ce20f660>先进人脸识别技术</h4><p class="text-gray-600 text-sm" data-v-ce20f660>采用深度学习算法，识别准确率高达99.7%</p></div></div><div class="flex items-start" data-v-ce20f660><i class="fas fa-check-circle text-green-500 mr-3 mt-1" data-v-ce20f660></i><div data-v-ce20f660><h4 class="font-semibold text-gray-800" data-v-ce20f660>离线识别功能</h4><p class="text-gray-600 text-sm" data-v-ce20f660>支持离线人脸识别，网络断开也能正常使用</p></div></div><div class="flex items-start" data-v-ce20f660><i class="fas fa-check-circle text-green-500 mr-3 mt-1" data-v-ce20f660></i><div data-v-ce20f660><h4 class="font-semibold text-gray-800" data-v-ce20f660>快速识别响应</h4><p class="text-gray-600 text-sm" data-v-ce20f660>识别速度≤1秒，提升用户体验</p></div></div><div class="flex items-start" data-v-ce20f660><i class="fas fa-check-circle text-green-500 mr-3 mt-1" data-v-ce20f660></i><div data-v-ce20f660><h4 class="font-semibold text-gray-800" data-v-ce20f660>安卓操作系统</h4><p class="text-gray-600 text-sm" data-v-ce20f660>基于Android 8.1系统，界面友好，操作简单</p></div></div></div></div><div class="bg-white rounded-xl shadow-lg p-8" data-aos="fade-up" data-aos-delay="200" data-v-ce20f660><h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center" data-v-ce20f660><i class="fas fa-map-marker-alt text-blue-600 mr-3" data-v-ce20f660></i> 应用场景 </h3><div class="space-y-4" data-v-ce20f660><div class="flex items-start" data-v-ce20f660><i class="fas fa-graduation-cap text-blue-500 mr-3 mt-1" data-v-ce20f660></i><div data-v-ce20f660><h4 class="font-semibold text-gray-800" data-v-ce20f660>学校食堂</h4><p class="text-gray-600 text-sm" data-v-ce20f660>学生刷脸消费，方便快捷，提升就餐效率</p></div></div><div class="flex items-start" data-v-ce20f660><i class="fas fa-building text-green-500 mr-3 mt-1" data-v-ce20f660></i><div data-v-ce20f660><h4 class="font-semibold text-gray-800" data-v-ce20f660>企业餐厅</h4><p class="text-gray-600 text-sm" data-v-ce20f660>员工人脸识别消费，无需携带卡片</p></div></div><div class="flex items-start" data-v-ce20f660><i class="fas fa-hospital text-red-500 mr-3 mt-1" data-v-ce20f660></i><div data-v-ce20f660><h4 class="font-semibold text-gray-800" data-v-ce20f660>医院食堂</h4><p class="text-gray-600 text-sm" data-v-ce20f660>医护人员快速消费，减少接触感染风险</p></div></div><div class="flex items-start" data-v-ce20f660><i class="fas fa-store text-purple-500 mr-3 mt-1" data-v-ce20f660></i><div data-v-ce20f660><h4 class="font-semibold text-gray-800" data-v-ce20f660>连锁餐厅</h4><p class="text-gray-600 text-sm" data-v-ce20f660>会员人脸识别，享受个性化服务</p></div></div></div></div></div><div class="mt-12 bg-white rounded-xl shadow-lg p-8" data-aos="fade-up" data-aos-delay="400" data-v-ce20f660><h3 class="text-2xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center" data-v-ce20f660><i class="fas fa-cogs text-blue-600 mr-3" data-v-ce20f660></i> 技术优势 </h3><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-v-ce20f660><div class="text-center" data-v-ce20f660><div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" data-v-ce20f660><i class="fas fa-shield-alt text-blue-600 text-2xl" data-v-ce20f660></i></div><h4 class="font-semibold text-gray-800 mb-2" data-v-ce20f660>安全可靠</h4><p class="text-gray-600 text-sm" data-v-ce20f660>采用活体检测技术，防止照片、视频等欺骗攻击</p></div><div class="text-center" data-v-ce20f660><div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" data-v-ce20f660><i class="fas fa-bolt text-green-600 text-2xl" data-v-ce20f660></i></div><h4 class="font-semibold text-gray-800 mb-2" data-v-ce20f660>高效处理</h4><p class="text-gray-600 text-sm" data-v-ce20f660>高性能处理器，支持大容量人脸库快速检索</p></div><div class="text-center" data-v-ce20f660><div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" data-v-ce20f660><i class="fas fa-sync-alt text-purple-600 text-2xl" data-v-ce20f660></i></div><h4 class="font-semibold text-gray-800 mb-2" data-v-ce20f660>易于维护</h4><p class="text-gray-600 text-sm" data-v-ce20f660>支持远程升级和维护，降低运营成本</p></div></div></div></div></div></div></section>',2))])}const wr=_(Zd,[["render",yr],["__scopeId","data-v-ce20f660"]]),_r={name:"ArmTerminalA201",components:{ProductNavigation:O},setup(){const o=m("arm-terminal"),t=m("a201-ic-2w");return{currentCategory:o,currentProduct:t}}},$r={class:"py-20 bg-white"},kr={class:"w-full"},Cr={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},Pr={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},jr={class:"lg:ml-4 xl:ml-8 2xl:ml-16"};function Fr(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[1]||(t[1]=f('<section class="bg-gradient-to-r from-green-600 to-green-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">A201-IC-2W ARM消费机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">高性能处理器，稳定可靠</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",$r,[e("div",kr,[e("div",Cr,[e("div",Pr,[e("div",jr,[n(s,{"current-category":a.currentCategory,"current-product":a.currentProduct},null,8,["current-category","current-product"])])]),t[0]||(t[0]=e("div",{class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},[e("div",{class:"text-center"},[e("h2",{class:"text-3xl font-bold text-gray-800 mb-4"},"A201-IC-2W详情"),e("p",{class:"text-gray-600 text-lg"},"ARM消费机产品详情")])],-1))])])])])}const Ir=_(_r,[["render",Fr]]),Nr={name:"FaceCloudBasic",components:{ProductNavigation:O},setup(){const o=m("face-cloud"),t=m("basic");return{currentCategory:o,currentProduct:t}}},Sr={class:"py-20 bg-white"},Ar={class:"w-full"},Mr={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},Kr={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},Tr={class:"lg:ml-4 xl:ml-8 2xl:ml-16"};function Dr(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[1]||(t[1]=f('<section class="bg-gradient-to-r from-cyan-600 to-cyan-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">脸爱云平台基础版</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">云端智能管理，数据安全可靠</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",Sr,[e("div",Ar,[e("div",Mr,[e("div",Kr,[e("div",Tr,[n(s,{"current-category":a.currentCategory,"current-product":a.currentProduct},null,8,["current-category","current-product"])])]),t[0]||(t[0]=e("div",{class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},[e("div",{class:"text-center"},[e("h2",{class:"text-3xl font-bold text-gray-800 mb-4"},"脸爱云平台基础版详情"),e("p",{class:"text-gray-600 text-lg"},"云平台产品详情")])],-1))])])])])}const zr=_(Nr,[["render",Dr]]),Rr={name:"FaceTempGateTG100",components:{ProductNavigation:O},setup(){const o=m("face-temp-gate"),t=m("tg100-stand");return{currentCategory:o,currentProduct:t}}},Wr={class:"py-20 bg-white"},Er={class:"w-full"},Lr={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},Yr={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},Br={class:"lg:ml-4 xl:ml-8 2xl:ml-16"};function qr(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[1]||(t[1]=f('<section class="bg-gradient-to-r from-purple-600 to-purple-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">YKT-TG100立式测温闸机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">立式人脸测温通道闸机</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",Wr,[e("div",Er,[e("div",Lr,[e("div",Yr,[e("div",Br,[n(s,{"current-category":a.currentCategory,"current-product":a.currentProduct},null,8,["current-category","current-product"])])]),t[0]||(t[0]=e("div",{class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},[e("div",{class:"text-center"},[e("h2",{class:"text-3xl font-bold text-gray-800 mb-4"},"YKT-TG100详情"),e("p",{class:"text-gray-600 text-lg"},"立式测温闸机产品详情")])],-1))])])])])}const Gr=_(Rr,[["render",qr]]),Hr={name:"CampusSharedWM100",components:{ProductNavigation:O},setup(){const o=m("campus-shared"),t=m("wm100-washer"),r=m(0),a=m(!1),g=m(0),x=m(0),s=m(null),l=m(null),d=m([{full:"https://placehold.co/800x600/FFFFFF/333333?text=YKT-WM100+正面视图",thumb:"https://placehold.co/100x100/FFFFFF/333333?text=正面",alt:"正面视图"},{full:"https://placehold.co/800x600/FFFFFF/333333?text=YKT-WM100+侧面视图",thumb:"https://placehold.co/100x100/FFFFFF/333333?text=侧面",alt:"侧面视图"},{full:"https://placehold.co/800x600/FFFFFF/333333?text=YKT-WM100+控制面板",thumb:"https://placehold.co/100x100/FFFFFF/333333?text=控制面板",alt:"控制面板"}]),w=A(()=>{var P;return((P=d.value[r.value])==null?void 0:P.full)||""}),j=P=>{r.value=P},C=()=>{a.value=!0},y=P=>{if(!s.value||!a.value)return;const I=s.value.getBoundingClientRect();g.value=P.clientX-I.left,x.value=P.clientY-I.top},K=()=>{a.value=!1},k=A(()=>({width:"120px",height:"120px",left:`${g.value-120/2}px`,top:`${x.value-120/2}px`})),b=A(()=>a.value?{transform:"scale(1.02)"}:{}),z=A(()=>{if(!a.value||!s.value)return{};const P=s.value.getBoundingClientRect(),I=3,B=-(g.value*I-128),L=-(x.value*I-128);return{backgroundImage:`url(${w.value})`,backgroundPosition:`${B}px ${L}px`,backgroundSize:`${P.width*I}px ${P.height*I}px`,transition:"background-position 0.05s ease-out"}}),E=()=>{alert(`您正在咨询 YKT-WM100 共享洗衣机，我们的客服将尽快与您联系！

联系电话：************
微信：youkate2024`)},at=()=>{window.location.href="tel:************"},S=m(null),Y=()=>{if(!S.value)return;const P=()=>{const I=document.querySelector("#product-description-bottom");if(!I)return;const L=I.getBoundingClientRect().bottom+window.scrollY;window.scrollY>L?(S.value.style.opacity="1",S.value.style.transform="translateX(0)"):(S.value.style.opacity="0",S.value.style.transform="translateX(-20px)")};window.addEventListener("scroll",P),P()};return F(()=>{N.init({duration:1e3,once:!0}),setTimeout(()=>{Y()},100)}),{currentCategory:o,currentProduct:t,currentImageIndex:r,currentImage:w,productImages:d,showMagnifier:a,mouseX:g,mouseY:x,mainImageContainer:s,mainImage:l,setCurrentImage:j,handleMouseEnter:C,handleMouseMove:y,handleMouseLeave:K,lensStyle:k,imageTransform:b,magnifiedStyle:z,consultNow:E,callPhone:at,floatingNav:S}}},Or={class:"py-20 bg-white"},Ur={class:"w-full"},Vr={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},Qr={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},Xr={class:"lg:ml-4 xl:ml-8 2xl:ml-16"},Jr={class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},Zr={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},ti={class:"bg-white rounded-xl shadow-lg overflow-hidden mb-4"},ei={class:"relative"},ai=["src"],si={class:"bg-white rounded-xl shadow-lg p-4"},oi={class:"flex justify-center space-x-3"},li=["onClick"],di=["src","alt"],ri={class:"bg-white rounded-xl shadow-lg overflow-hidden h-96"},ii={class:"h-full bg-gray-100 flex items-center justify-center relative overflow-hidden"},ni={key:1,class:"text-gray-400 text-center"},ci={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},ui={class:"bg-white rounded-xl shadow-lg p-6 h-64 flex flex-col"},pi={class:"flex-1 flex flex-col justify-center space-y-3"},gi={ref:"floatingNav",class:"fixed left-1/2 transform -translate-x-1/2 lg:left-20 lg:transform-none top-24 z-40 md:block hidden transition-all duration-500 ease-in-out",style:{opacity:"0",transform:"translateX(-20px)"}};function xi(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[12]||(t[12]=f('<section class="bg-gradient-to-r from-green-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-2ff93818><div class="absolute inset-0 bg-black opacity-10" data-v-2ff93818></div><div class="container mx-auto px-4 relative z-10" data-v-2ff93818><div class="text-center" data-v-2ff93818><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-2ff93818>YKT-WM100 共享洗衣机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-2ff93818>智能共享，便民洗衣</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-2ff93818></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-2ff93818></div></section>',1)),e("section",Or,[e("div",Ur,[e("div",Vr,[e("div",Qr,[e("div",Xr,[n(s,{"current-category":a.currentCategory,"current-product":a.currentProduct},null,8,["current-category","current-product"])])]),e("div",Jr,[e("div",Zr,[e("div",null,[e("div",ti,[e("div",ei,[e("div",{class:"product-main-image relative overflow-hidden bg-gray-100 cursor-zoom-in h-96",onMousemove:t[0]||(t[0]=(...l)=>a.handleMouseMove&&a.handleMouseMove(...l)),onMouseleave:t[1]||(t[1]=(...l)=>a.handleMouseLeave&&a.handleMouseLeave(...l)),onMouseenter:t[2]||(t[2]=(...l)=>a.handleMouseEnter&&a.handleMouseEnter(...l)),ref:"mainImageContainer"},[e("img",{src:a.currentImage,alt:"YKT-WM100共享洗衣机",class:"w-full h-full object-contain transition-transform duration-75",style:Z(a.imageTransform),ref:"mainImage"},null,12,ai),a.showMagnifier?(u(),p("div",{key:0,class:"magnifier-lens absolute border-2 border-blue-500 bg-blue-500 bg-opacity-20 pointer-events-none rounded-full",style:Z(a.lensStyle)},null,4)):h("",!0)],544)])]),t[5]||(t[5]=e("div",{class:"border-t border-gray-200 my-4"},null,-1)),e("div",si,[e("div",oi,[(u(!0),p(R,null,W(a.productImages,(l,d)=>(u(),p("button",{key:d,onClick:w=>a.setCurrentImage(d),class:M(["w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 hover:scale-105",a.currentImageIndex===d?"border-blue-500 shadow-lg ring-2 ring-blue-200":"border-gray-300 hover:border-blue-300"])},[e("img",{src:l.thumb,alt:l.alt,class:"w-full h-full object-cover"},null,8,di)],10,li))),128))])])]),e("div",null,[e("div",ri,[e("div",ii,[a.showMagnifier?(u(),p("div",{key:0,class:"magnified-view w-full h-full bg-cover bg-no-repeat transition-all duration-100",style:Z(a.magnifiedStyle)},null,4)):(u(),p("div",ni,t[6]||(t[6]=[e("i",{class:"fas fa-search-plus text-4xl mb-2"},null,-1),e("p",{class:"text-sm"},"悬停图片查看放大效果",-1)])))])])])]),e("div",ci,[t[10]||(t[10]=f('<div data-v-2ff93818><div class="bg-white rounded-xl shadow-lg p-6 h-64" data-v-2ff93818><div class="flex flex-wrap gap-2 mb-4" data-v-2ff93818><span class="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-2ff93818>智能共享</span><span class="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-2ff93818>远程监控</span><span class="bg-purple-100 text-purple-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-2ff93818>便民服务</span></div><h2 class="text-2xl font-bold text-gray-800 mb-3" data-v-2ff93818>YKT-WM100 共享洗衣机</h2><p class="text-gray-600 leading-relaxed" id="product-description-bottom" data-v-2ff93818> 智能共享洗衣设备，支持多种支付方式，远程监控管理。 采用先进的物联网技术，为校园、公寓提供便捷、安全的洗衣服务， 让用户享受智能化的洗衣体验。 </p></div></div>',1)),e("div",null,[e("div",ui,[t[9]||(t[9]=f('<div class="flex flex-wrap gap-2 mb-4" data-v-2ff93818><span class="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-2ff93818>专业服务</span><span class="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-2ff93818>快速响应</span><span class="bg-purple-100 text-purple-600 px-3 py-1 rounded-full text-sm font-semibold" data-v-2ff93818>技术支持</span></div><h2 class="text-2xl font-bold text-gray-800 mb-3" data-v-2ff93818>产品咨询服务</h2>',2)),e("div",pi,[e("button",{onClick:t[3]||(t[3]=(...l)=>a.consultNow&&a.consultNow(...l)),class:"w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition duration-300 text-base font-semibold flex items-center justify-center"},t[7]||(t[7]=[e("i",{class:"fas fa-comments mr-3"},null,-1),i("立即咨询 ")])),e("button",{onClick:t[4]||(t[4]=(...l)=>a.callPhone&&a.callPhone(...l)),class:"w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition duration-300 text-base font-semibold flex items-center justify-center"},t[8]||(t[8]=[e("i",{class:"fas fa-phone mr-3"},null,-1),i("电话咨询 ")]))])])])])])])])]),e("div",gi,t[11]||(t[11]=[f('<div class="bg-gray-50 rounded-xl p-6 shadow-lg" data-v-2ff93818><h3 class="text-lg font-bold text-gray-800 mb-4" data-v-2ff93818>页面导航</h3><nav class="space-y-2" data-v-2ff93818><a href="#product-params" class="flex items-center p-3 rounded-lg hover:bg-green-50 transition duration-300 text-gray-600 hover:text-green-600 cursor-pointer" data-v-2ff93818><i class="fas fa-cogs mr-3" data-v-2ff93818></i> 技术规格 </a><a href="#product-details" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600 cursor-pointer" data-v-2ff93818><i class="fas fa-info-circle mr-3" data-v-2ff93818></i> 产品详情 </a></nav></div>',1)]),512),t[13]||(t[13]=f('<section id="product-params" class="py-20 bg-gray-50" data-v-2ff93818><div class="container mx-auto px-4" data-v-2ff93818><div class="grid grid-cols-1 lg:grid-cols-4 gap-8" data-v-2ff93818><div class="lg:col-span-1" data-v-2ff93818></div><div class="lg:col-span-3" data-v-2ff93818><div class="bg-white rounded-xl shadow-lg p-8" data-aos="fade-up" data-v-2ff93818><div class="text-center mb-8" data-v-2ff93818><h2 class="text-3xl font-bold text-gray-800 mb-4" data-aos="fade-up" data-v-2ff93818>技术规格</h2><div class="w-16 h-1 bg-green-600 mx-auto" data-aos="fade-up" data-aos-delay="200" data-v-2ff93818></div></div><h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center" data-v-2ff93818><i class="fas fa-cogs text-green-600 mr-3" data-v-2ff93818></i> 技术参数 </h3><div class="overflow-x-auto" data-v-2ff93818><table class="w-full border-collapse" data-v-2ff93818><tbody data-v-2ff93818><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>产品型号</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>YKT-WM100</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>洗涤容量</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>10公斤</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>洗涤方式</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>滚筒式</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>支付方式</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>刷卡/扫码/现金</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>控制系统</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>智能物联网控制</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>显示屏</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>7英寸彩色触摸屏</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>通讯方式</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>WiFi/以太网/4G</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>额定功率</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>2200W</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>额定电压</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>220V/50Hz</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>用水量</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>≤80L/次</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>洗涤时间</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>30-90分钟</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>工作环境</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>5℃~40℃</td></tr><tr class="border-b border-gray-200 hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>安全等级</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>IPX4防水等级</td></tr><tr class="hover:bg-gray-50 transition duration-200" data-v-2ff93818><td class="py-4 px-6 font-semibold text-gray-700 bg-gray-50" data-v-2ff93818>产品尺寸</td><td class="py-4 px-6 text-gray-600" data-v-2ff93818>600×650×850mm</td></tr></tbody></table></div></div></div></div></div></section><section id="product-details" class="py-20 bg-white" data-v-2ff93818><div class="container mx-auto px-4" data-v-2ff93818><div class="grid grid-cols-1 lg:grid-cols-4 gap-8" data-v-2ff93818><div class="lg:col-span-1" data-v-2ff93818></div><div class="lg:col-span-3" data-v-2ff93818><div class="text-center mb-8" data-v-2ff93818><h2 class="text-3xl font-bold text-gray-800 mb-4" data-aos="fade-up" data-v-2ff93818>产品详情</h2><div class="w-16 h-1 bg-blue-600 mx-auto" data-aos="fade-up" data-aos-delay="200" data-v-2ff93818></div></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-8" data-v-2ff93818><div class="bg-white rounded-xl shadow-lg p-8" data-aos="fade-up" data-v-2ff93818><h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center" data-v-2ff93818><i class="fas fa-star text-blue-600 mr-3" data-v-2ff93818></i> 核心特点 </h3><div class="space-y-4" data-v-2ff93818><div class="flex items-start" data-v-2ff93818><i class="fas fa-check-circle text-green-500 mr-3 mt-1" data-v-2ff93818></i><div data-v-2ff93818><h4 class="font-semibold text-gray-800" data-v-2ff93818>智能支付系统</h4><p class="text-gray-600 text-sm" data-v-2ff93818>支持刷卡、扫码、现金等多种支付方式，操作简单便捷</p></div></div><div class="flex items-start" data-v-2ff93818><i class="fas fa-check-circle text-green-500 mr-3 mt-1" data-v-2ff93818></i><div data-v-2ff93818><h4 class="font-semibold text-gray-800" data-v-2ff93818>远程监控管理</h4><p class="text-gray-600 text-sm" data-v-2ff93818>物联网技术实现远程监控，实时掌握设备运行状态</p></div></div><div class="flex items-start" data-v-2ff93818><i class="fas fa-check-circle text-green-500 mr-3 mt-1" data-v-2ff93818></i><div data-v-2ff93818><h4 class="font-semibold text-gray-800" data-v-2ff93818>多重安全保护</h4><p class="text-gray-600 text-sm" data-v-2ff93818>具备防水、防漏电、防盗等多重安全保护功能</p></div></div><div class="flex items-start" data-v-2ff93818><i class="fas fa-check-circle text-green-500 mr-3 mt-1" data-v-2ff93818></i><div data-v-2ff93818><h4 class="font-semibold text-gray-800" data-v-2ff93818>节能环保设计</h4><p class="text-gray-600 text-sm" data-v-2ff93818>采用节能技术，减少水电消耗，环保高效</p></div></div></div></div><div class="bg-white rounded-xl shadow-lg p-8" data-aos="fade-up" data-aos-delay="200" data-v-2ff93818><h3 class="text-2xl font-bold text-gray-800 mb-6 flex items-center" data-v-2ff93818><i class="fas fa-map-marker-alt text-blue-600 mr-3" data-v-2ff93818></i> 应用场景 </h3><div class="space-y-4" data-v-2ff93818><div class="flex items-start" data-v-2ff93818><i class="fas fa-graduation-cap text-blue-500 mr-3 mt-1" data-v-2ff93818></i><div data-v-2ff93818><h4 class="font-semibold text-gray-800" data-v-2ff93818>学校宿舍</h4><p class="text-gray-600 text-sm" data-v-2ff93818>为学生提供便捷的洗衣服务，支持校园卡支付</p></div></div><div class="flex items-start" data-v-2ff93818><i class="fas fa-building text-green-500 mr-3 mt-1" data-v-2ff93818></i><div data-v-2ff93818><h4 class="font-semibold text-gray-800" data-v-2ff93818>公寓楼宇</h4><p class="text-gray-600 text-sm" data-v-2ff93818>适用于各类公寓，提供24小时自助洗衣服务</p></div></div><div class="flex items-start" data-v-2ff93818><i class="fas fa-users text-red-500 mr-3 mt-1" data-v-2ff93818></i><div data-v-2ff93818><h4 class="font-semibold text-gray-800" data-v-2ff93818>员工宿舍</h4><p class="text-gray-600 text-sm" data-v-2ff93818>企业员工宿舍的理想选择，提升员工生活品质</p></div></div><div class="flex items-start" data-v-2ff93818><i class="fas fa-home text-purple-500 mr-3 mt-1" data-v-2ff93818></i><div data-v-2ff93818><h4 class="font-semibold text-gray-800" data-v-2ff93818>社区服务</h4><p class="text-gray-600 text-sm" data-v-2ff93818>社区便民服务设施，方便居民日常洗衣需求</p></div></div></div></div></div><div class="mt-12 bg-white rounded-xl shadow-lg p-8" data-aos="fade-up" data-aos-delay="400" data-v-2ff93818><h3 class="text-2xl font-bold text-gray-800 mb-6 text-center flex items-center justify-center" data-v-2ff93818><i class="fas fa-cogs text-blue-600 mr-3" data-v-2ff93818></i> 技术优势 </h3><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-v-2ff93818><div class="text-center" data-v-2ff93818><div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" data-v-2ff93818><i class="fas fa-brain text-blue-600 text-2xl" data-v-2ff93818></i></div><h4 class="font-semibold text-gray-800 mb-2" data-v-2ff93818>智能化管理</h4><p class="text-gray-600 text-sm" data-v-2ff93818>物联网技术实现智能化运营管理，提升服务效率</p></div><div class="text-center" data-v-2ff93818><div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" data-v-2ff93818><i class="fas fa-shield-alt text-green-600 text-2xl" data-v-2ff93818></i></div><h4 class="font-semibold text-gray-800 mb-2" data-v-2ff93818>安全可靠</h4><p class="text-gray-600 text-sm" data-v-2ff93818>多重安全保护机制，确保设备和用户安全</p></div><div class="text-center" data-v-2ff93818><div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" data-v-2ff93818><i class="fas fa-heart text-purple-600 text-2xl" data-v-2ff93818></i></div><h4 class="font-semibold text-gray-800 mb-2" data-v-2ff93818>便民服务</h4><p class="text-gray-600 text-sm" data-v-2ff93818>24小时自助服务，为用户提供便捷的洗衣体验</p></div></div></div></div></div></div></section>',2))])}const fi=_(Hr,[["render",xi],["__scopeId","data-v-2ff93818"]]),vi={name:"WaterElectricWC100",components:{ProductNavigation:O},setup(){const o=m("water-electric"),t=m("wc100-water");return{currentCategory:o,currentProduct:t}}},mi={class:"py-20 bg-white"},bi={class:"w-full"},hi={class:"grid grid-cols-1 lg:grid-cols-4 gap-8"},yi={class:"lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16"},wi={class:"lg:ml-4 xl:ml-8 2xl:ml-16"};function _i(o,t,r,a,g,x){const s=$("ProductNavigation");return u(),p("div",null,[t[1]||(t[1]=f('<section class="bg-gradient-to-r from-orange-600 to-orange-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">YKT-WC100智能水控机</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">IC卡智能水控终端</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",mi,[e("div",bi,[e("div",hi,[e("div",yi,[e("div",wi,[n(s,{"current-category":a.currentCategory,"current-product":a.currentProduct},null,8,["current-category","current-product"])])]),t[0]||(t[0]=e("div",{class:"lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-4 xl:pr-8 2xl:pr-16"},[e("div",{class:"text-center"},[e("h2",{class:"text-3xl font-bold text-gray-800 mb-4"},"YKT-WC100详情"),e("p",{class:"text-gray-600 text-lg"},"智能水控机产品详情")])],-1))])])])])}const $i=_(vi,[["render",_i]]),ki={name:"CompanyNews",components:{Pagination:tt},setup(){const o=m([{id:1,title:"优卡特荣获2024年度智能设备创新奖",content:"深圳市优卡特实业有限公司凭借其在智能一卡通设备领域的突出贡献，荣获2024年度智能设备创新奖。该奖项是对公司多年来在技术创新和产品质量方面不懈努力的认可。",date:"2024-01-15",category:"公司新闻"},{id:2,title:"新一代人脸识别消费机正式发布",content:"优卡特最新推出的人脸识别消费机采用先进的AI算法，识别精度达到99.9%，响应速度提升50%。该产品将广泛应用于学校、企业、医院等场所。",date:"2024-01-10",category:"产品发布"},{id:3,title:"优卡特与多家高校达成战略合作",content:"公司与清华大学、北京大学等知名高校签署战略合作协议，共同推进智慧校园建设。合作内容包括技术研发、人才培养、产学研一体化等多个方面。",date:"2024-01-05",category:"合作动态"},{id:4,title:"优卡特参加2024年智能设备展览会",content:"公司携最新产品亮相2024年国际智能设备展览会，展示了人脸识别、指纹识别等多款智能终端设备，获得了业界的广泛关注。",date:"2024-01-01",category:"展会活动"},{id:5,title:"公司通过ISO27001信息安全管理体系认证",content:"经过严格的审核评估，优卡特成功通过ISO27001信息安全管理体系认证，标志着公司在信息安全管理方面达到了国际先进水平。",date:"2023-12-28",category:"企业认证"}]),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedItems:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j}=et(o,10),C=y=>{console.log("打开企业新闻详情:",y),alert(`点击了企业新闻: ${y}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedNews:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j,openNewsDetail:C}}},Ci={class:"py-12 bg-gray-50"},Pi={class:"container mx-auto px-4"},ji={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},Fi={class:"pt-8 pb-20 bg-white"},Ii={class:"container mx-auto px-4"},Ni={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Si={class:"lg:col-span-2"},Ai={class:"grid gap-8"},Mi={class:"space-y-6"},Ki={key:0},Ti=["onClick"],Di=["alt"],zi={class:"p-4 flex-1"},Ri={class:"flex items-center mb-2"},Wi={class:"bg-blue-100 text-blue-600 px-2 py-1 rounded text-xs"},Ei={class:"text-gray-500 text-xs ml-2"},Li={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition duration-300 cursor-pointer"},Yi={class:"text-gray-600 text-sm"},Bi={key:1,class:"text-center py-16"},qi={class:"max-w-md mx-auto"},Gi={class:"text-gray-500 mb-4"},Hi={class:"font-medium text-blue-600"},Oi={class:"lg:col-span-1"},Ui={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Vi={class:"relative"},Qi={key:0,class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Xi={class:"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},Ji={class:"space-y-3"};function Zi(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-9a9b6f66><div class="absolute inset-0 bg-black opacity-10" data-v-9a9b6f66></div><div class="container mx-auto px-4 relative z-10" data-v-9a9b6f66><div class="text-center" data-v-9a9b6f66><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-9a9b6f66>企业新闻</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-9a9b6f66>了解优卡特最新动态和发展历程</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-9a9b6f66></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-9a9b6f66></div></section>',1)),e("section",Ci,[e("div",Pi,[e("div",ji,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/company-news-category",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[5]||(t[5]=[i(" 公司新闻 ")])),_:1,__:[5]}),n(s,{to:"/news/product-release",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[6]||(t[6]=[i(" 产品发布 ")])),_:1,__:[6]}),n(s,{to:"/news/exhibition",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[7]||(t[7]=[i(" 展会活动 ")])),_:1,__:[7]}),n(s,{to:"/news/corporate-culture",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[8]||(t[8]=[i(" 企业文化 ")])),_:1,__:[8]}),n(s,{to:"/news/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[9]||(t[9]=[i(" 技术创新 ")])),_:1,__:[9]})])])]),e("section",Fi,[e("div",Ii,[e("div",Ni,[e("div",Si,[e("div",Ai,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(d=>a.openNewsDetail("company-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-9a9b6f66><img src="https://placehold.co/800x400" alt="优卡特荣获2024年度智能设备创新奖" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-9a9b6f66><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-9a9b6f66> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-9a9b6f66><div class="flex items-center mb-2" data-v-9a9b6f66><span class="bg-blue-100 text-blue-600 px-3 py-1 rounded text-sm" data-v-9a9b6f66>企业新闻</span><span class="text-white text-sm ml-3" data-v-9a9b6f66>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-blue-200 transition-colors duration-300" data-v-9a9b6f66>优卡特荣获2024年度智能设备创新奖</h2><p class="text-gray-200" data-v-9a9b6f66>深圳市优卡特实业有限公司凭借其在智能一卡通设备领域的突出贡献，荣获2024年度智能设备创新奖...</p></div></div>',1)]))):h("",!0),e("div",Mi,[a.paginatedNews.length>0?(u(),p("div",Ki,[(u(!0),p(R,null,W(a.paginatedNews,d=>(u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,Di),e("div",zi,[e("div",Ri,[e("span",Wi,v(d.category),1),e("span",Ei,v(d.date),1)]),e("h4",Li,v(d.title),1),e("p",Yi,v(d.content),1)])],8,Ti))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",Bi,[e("div",qi,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",Gi,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",Hi,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",Oi,[e("div",Ui,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-blue-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",Vi,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索资讯...","onUpdate:modelValue":t[2]||(t[2]=d=>a.searchKeyword=d),onKeyup:t[3]||(t[3]=H(d=>a.handleSearch(a.searchKeyword),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-blue-50 border border-blue-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-400 focus:bg-white transition-all duration-200 placeholder-blue-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",Qi,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-9a9b6f66><i class="fas fa-fire text-red-500 mr-2" data-v-9a9b6f66></i> 热门新闻 </h3><div class="space-y-4" data-v-9a9b6f66><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-9a9b6f66><h4 class="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 mb-2" data-v-9a9b6f66>优卡特荣获智能设备创新奖</h4><p class="text-xs text-gray-500" data-v-9a9b6f66>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-9a9b6f66><h4 class="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 mb-2" data-v-9a9b6f66>新一代人脸识别设备发布</h4><p class="text-xs text-gray-500" data-v-9a9b6f66>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-9a9b6f66><h4 class="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 mb-2" data-v-9a9b6f66>与高校达成战略合作</h4><p class="text-xs text-gray-500" data-v-9a9b6f66>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",Xi,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-green-500 mr-2"}),i(" 新闻分类 ")],-1)),e("div",Ji,[n(s,{to:"/news/company-news-category",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-blue-600 transition-colors duration-300"},"公司新闻",-1),e("span",{class:"bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[19]}),n(s,{to:"/news/product-release",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-blue-600 transition-colors duration-300"},"产品发布",-1),e("span",{class:"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[20]}),n(s,{to:"/news/exhibition",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-blue-600 transition-colors duration-300"},"展会活动",-1),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"6",-1)])),_:1,__:[21]}),n(s,{to:"/news/corporate-culture",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-blue-600 transition-colors duration-300"},"企业文化",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[22]}),n(s,{to:"/news/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[23]||(t[23]=[e("span",{class:"text-gray-700 group-hover:text-blue-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"9",-1)])),_:1,__:[23]})])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"blue",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white" data-v-9a9b6f66><div class="container mx-auto px-4" data-v-9a9b6f66><div class="text-center mb-12" data-aos="fade-up" data-v-9a9b6f66><h2 class="text-3xl font-bold mb-4" data-v-9a9b6f66>联系我们</h2><p class="text-xl" data-v-9a9b6f66>如需了解更多企业信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-9a9b6f66><div class="text-center" data-v-9a9b6f66><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-9a9b6f66></i><h3 class="text-xl font-semibold mb-2" data-v-9a9b6f66>地址</h3><p data-v-9a9b6f66>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-9a9b6f66><i class="fas fa-phone text-4xl mb-4" data-v-9a9b6f66></i><h3 class="text-xl font-semibold mb-2" data-v-9a9b6f66>电话</h3><p data-v-9a9b6f66>159-8667-2052</p></div><div class="text-center" data-v-9a9b6f66><i class="fas fa-envelope text-4xl mb-4" data-v-9a9b6f66></i><h3 class="text-xl font-semibold mb-2" data-v-9a9b6f66>邮箱</h3><p data-v-9a9b6f66><EMAIL></p></div></div></div></section>',1))])}const tn=_(ki,[["render",Zi],["__scopeId","data-v-9a9b6f66"]]),en={name:"IndustryNews",components:{Pagination:tt},setup(){const o=m([{id:1,title:"2024年智能卡行业发展趋势分析报告",content:"根据最新市场调研数据，2024年全球智能卡市场预计将达到150亿美元，其中人脸识别消费机市场增长最为迅速。",date:"2024-01-20",category:"市场分析"},{id:2,title:"国家发布智能设备安全标准新规定",content:"工信部发布《智能终端设备安全技术要求》，对人脸识别、指纹识别等生物识别技术提出了更严格的安全标准。",date:"2024-01-18",category:"政策解读"},{id:3,title:"5G技术推动智能卡设备升级换代",content:"随着5G网络的普及，智能卡设备正迎来新一轮技术升级，云端处理能力和实时响应速度得到显著提升。",date:"2024-01-15",category:"技术创新"},{id:4,title:"中欧智能卡技术合作项目正式启动",content:"中欧双方在智能卡技术领域达成重要合作协议，将在技术研发、标准制定、市场拓展等方面开展深度合作。",date:"2024-01-12",category:"国际合作"}]),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedItems:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j}=et(o,10),C=y=>{console.log("打开行业资讯详情:",y),alert(`点击了行业资讯: ${y}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedNews:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j,openNewsDetail:C}}},an={class:"py-12 bg-gray-50"},sn={class:"container mx-auto px-4"},on={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},ln={class:"pt-8 pb-20 bg-white"},dn={class:"container mx-auto px-4"},rn={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},nn={class:"lg:col-span-2"},cn={class:"grid gap-8"},un={class:"space-y-6"},pn={key:0},gn=["onClick"],xn=["alt"],fn={class:"p-4 flex-1"},vn={class:"flex items-center mb-2"},mn={class:"bg-green-100 text-green-600 px-2 py-1 rounded text-xs"},bn={class:"text-gray-500 text-xs ml-2"},hn={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-green-600 transition duration-300 cursor-pointer"},yn={class:"text-gray-600 text-sm"},wn={key:1,class:"text-center py-16"},_n={class:"max-w-md mx-auto"},$n={class:"text-gray-500 mb-4"},kn={class:"font-medium text-green-600"},Cn={class:"lg:col-span-1"},Pn={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},jn={class:"relative"},Fn={key:0,class:"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},In={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},Nn={class:"space-y-3"};function Sn(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden"><div class="absolute inset-0 bg-black opacity-10"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">行业资讯</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">关注行业动态，把握发展趋势</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;"></div></section>',1)),e("section",an,[e("div",sn,[e("div",on,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/industry/market-analysis",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[5]||(t[5]=[i(" 市场分析 ")])),_:1,__:[5]}),n(s,{to:"/news/industry/policy-interpretation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[6]||(t[6]=[i(" 政策解读 ")])),_:1,__:[6]}),n(s,{to:"/news/industry/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[7]||(t[7]=[i(" 技术创新 ")])),_:1,__:[7]}),n(s,{to:"/news/industry/international-cooperation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[8]||(t[8]=[i(" 国际合作 ")])),_:1,__:[8]}),n(s,{to:"/news/industry/industry-report",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-blue-600"},{default:c(()=>t[9]||(t[9]=[i(" 行业报告 ")])),_:1,__:[9]})])])]),e("section",ln,[e("div",dn,[e("div",rn,[e("div",nn,[e("div",cn,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(d=>a.openNewsDetail("industry-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative"><img src="https://placehold.co/800x400" alt="人脸识别技术迎来新突破" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500"><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold"> 头条资讯 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6"><div class="flex items-center mb-2"><span class="bg-green-100 text-green-600 px-3 py-1 rounded text-sm">行业热点</span><span class="text-white text-sm ml-3">2024-01-20</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-green-200 transition-colors duration-300">人脸识别技术迎来新突破，准确率达99.9%</h2><p class="text-gray-200">最新研究显示，基于深度学习的人脸识别技术在复杂环境下的识别准确率已达到99.9%...</p></div></div>',1)]))):h("",!0),e("div",un,[a.paginatedNews.length>0?(u(),p("div",pn,[(u(!0),p(R,null,W(a.paginatedNews,d=>(u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,xn),e("div",fn,[e("div",vn,[e("span",mn,v(d.category),1),e("span",bn,v(d.date),1)]),e("h4",hn,v(d.title),1),e("p",yn,v(d.content),1)])],8,gn))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",wn,[e("div",_n,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关资讯",-1)),e("p",$n,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",kn,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的行业资讯 '))]),e("button",{onClick:t[1]||(t[1]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",Cn,[e("div",Pn,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-blue-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",jn,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索资讯...","onUpdate:modelValue":t[2]||(t[2]=d=>a.searchKeyword=d),onKeyup:t[3]||(t[3]=H((...d)=>o.searchNews&&o.searchNews(...d),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-blue-50 border border-blue-200 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-400 focus:bg-white transition-all duration-200 placeholder-blue-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",Fn,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center"><i class="fas fa-fire text-red-500 mr-2"></i> 热门资讯 </h3><div class="space-y-4"><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"><h4 class="text-sm font-semibold text-gray-800 group-hover:text-green-600 transition-colors duration-300 mb-2">人脸识别技术迎来新突破</h4><p class="text-xs text-gray-500">2024-01-20</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"><h4 class="text-sm font-semibold text-gray-800 group-hover:text-green-600 transition-colors duration-300 mb-2">智能卡行业发展趋势分析</h4><p class="text-xs text-gray-500">2024-01-18</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"><h4 class="text-sm font-semibold text-gray-800 group-hover:text-green-600 transition-colors duration-300 mb-2">5G技术推动设备升级</h4><p class="text-xs text-gray-500">2024-01-15</p></a></div>',2)]))):h("",!0),e("div",In,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-green-500 mr-2"}),i(" 资讯分类 ")],-1)),e("div",Nn,[n(s,{to:"/news/industry/market-analysis",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"市场分析",-1),e("span",{class:"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs"},"18",-1)])),_:1,__:[19]}),n(s,{to:"/news/industry/policy-interpretation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"政策解读",-1),e("span",{class:"bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[20]}),n(s,{to:"/news/industry/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"25",-1)])),_:1,__:[21]}),n(s,{to:"/news/industry/international-cooperation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"国际合作",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[22]}),n(s,{to:"/news/industry/industry-report",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[23]||(t[23]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"行业报告",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[23]})])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"green",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0)])}const An=_(en,[["render",Sn]]),Mn={name:"TechnicalSupport",setup(){const o=m(-1),t=r=>{o.value=o.value===r?-1:r};return F(()=>{N.init({duration:1e3,once:!0})}),{openFAQ:o,toggleFAQ:t}}},Kn={class:"technical-support"},Tn={class:"py-20 bg-white"},Dn={class:"container mx-auto px-4"},zn={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Rn={class:"lg:col-span-2"},Wn={class:"mb-12"},En={class:"space-y-4"},Ln={class:"faq-item bg-white rounded-xl p-6 border border-gray-200","data-aos":"fade-up"},Yn={class:"faq-answer mt-4 text-gray-600"},Bn={class:"faq-item bg-white rounded-xl p-6 border border-gray-200","data-aos":"fade-up","data-aos-delay":"200"},qn={class:"faq-answer mt-4 text-gray-600"},Gn={class:"faq-item bg-white rounded-xl p-6 border border-gray-200","data-aos":"fade-up","data-aos-delay":"400"},Hn={class:"faq-answer mt-4 text-gray-600"},On={class:"faq-item bg-white rounded-xl p-6 border border-gray-200","data-aos":"fade-up","data-aos-delay":"600"},Un={class:"faq-answer mt-4 text-gray-600"},Vn={class:"mb-12"},Qn={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Xn={class:"doc-card bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition duration-300","data-aos":"fade-up","data-aos-delay":"400"};function Jn(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",Kn,[t[18]||(t[18]=f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8"><div class="container mx-auto px-4"><div class="text-center"><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">技术支持</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">专业技术支持，解决您的疑问</p></div></div></section>',1)),e("section",Tn,[e("div",Dn,[e("div",zn,[e("div",Rn,[e("div",Wn,[t[12]||(t[12]=e("h2",{class:"text-3xl font-bold text-gray-800 mb-8","data-aos":"fade-up"},"常见问题",-1)),e("div",En,[e("div",Ln,[e("div",{class:"faq-question flex items-center justify-between cursor-pointer",onClick:t[0]||(t[0]=l=>a.toggleFAQ(0))},[t[4]||(t[4]=e("h3",{class:"text-lg font-semibold text-gray-800"},"设备无法正常识别人脸怎么办？",-1)),e("i",{class:M(["fas fa-chevron-down text-gray-500 transition-transform duration-300",{"rotate-180":a.openFAQ===0}])},null,2)]),D(e("div",Yn,t[5]||(t[5]=[e("p",{class:"mb-3"},"请按以下步骤排查：",-1),e("ul",{class:"list-disc list-inside space-y-2"},[e("li",null,"检查摄像头是否清洁，清理镜头表面灰尘"),e("li",null,"确认光线条件是否充足，避免逆光或过暗环境"),e("li",null,"检查人脸是否在识别区域内，距离设备30-80cm"),e("li",null,"确认用户信息是否已正确录入系统"),e("li",null,"重启设备并重新尝试")],-1)]),512),[[U,a.openFAQ===0]])]),e("div",Bn,[e("div",{class:"faq-question flex items-center justify-between cursor-pointer",onClick:t[1]||(t[1]=l=>a.toggleFAQ(1))},[t[6]||(t[6]=e("h3",{class:"text-lg font-semibold text-gray-800"},"设备显示网络连接失败如何处理？",-1)),e("i",{class:M(["fas fa-chevron-down text-gray-500 transition-transform duration-300",{"rotate-180":a.openFAQ===1}])},null,2)]),D(e("div",qn,t[7]||(t[7]=[e("p",{class:"mb-3"},"网络连接问题解决方案：",-1),e("ul",{class:"list-disc list-inside space-y-2"},[e("li",null,"检查网线连接是否牢固，确认网线无损坏"),e("li",null,"确认网络设置参数是否正确（IP地址、子网掩码、网关）"),e("li",null,"测试网络连通性，ping服务器地址"),e("li",null,"检查防火墙设置，确保端口未被屏蔽"),e("li",null,"联系网络管理员确认网络状态")],-1)]),512),[[U,a.openFAQ===1]])]),e("div",Gn,[e("div",{class:"faq-question flex items-center justify-between cursor-pointer",onClick:t[2]||(t[2]=l=>a.toggleFAQ(2))},[t[8]||(t[8]=e("h3",{class:"text-lg font-semibold text-gray-800"},"消费记录数据丢失怎么恢复？",-1)),e("i",{class:M(["fas fa-chevron-down text-gray-500 transition-transform duration-300",{"rotate-180":a.openFAQ===2}])},null,2)]),D(e("div",Hn,t[9]||(t[9]=[e("p",{class:"mb-3"},"数据恢复处理步骤：",-1),e("ul",{class:"list-disc list-inside space-y-2"},[e("li",null,"检查设备本地存储是否有备份数据"),e("li",null,"登录管理后台查看云端备份记录"),e("li",null,"确认数据同步时间和最后备份时间"),e("li",null,"联系技术支持进行数据恢复操作"),e("li",null,"建议定期备份重要数据")],-1)]),512),[[U,a.openFAQ===2]])]),e("div",On,[e("div",{class:"faq-question flex items-center justify-between cursor-pointer",onClick:t[3]||(t[3]=l=>a.toggleFAQ(3))},[t[10]||(t[10]=e("h3",{class:"text-lg font-semibold text-gray-800"},"设备系统如何升级更新？",-1)),e("i",{class:M(["fas fa-chevron-down text-gray-500 transition-transform duration-300",{"rotate-180":a.openFAQ===3}])},null,2)]),D(e("div",Un,t[11]||(t[11]=[e("p",{class:"mb-3"},"系统升级操作指南：",-1),e("ul",{class:"list-disc list-inside space-y-2"},[e("li",null,"确保设备网络连接正常"),e("li",null,"进入系统设置-系统更新菜单"),e("li",null,"检查当前版本和可用更新"),e("li",null,"下载并安装最新版本"),e("li",null,"升级过程中请勿断电或断网")],-1)]),512),[[U,a.openFAQ===3]])])])]),e("div",Vn,[t[16]||(t[16]=e("h2",{class:"text-3xl font-bold text-gray-800 mb-8","data-aos":"fade-up"},"技术文档",-1)),e("div",Qn,[e("div",Xn,[t[14]||(t[14]=e("div",{class:"flex items-center mb-4"},[e("i",{class:"fas fa-file-pdf text-red-500 text-2xl mr-3"}),e("h3",{class:"text-lg font-semibold text-gray-800"},"使用说明")],-1)),t[15]||(t[15]=e("p",{class:"text-gray-600 mb-4"},"根据您的产品型号和使用场景，参考以下使用说明进行操作。",-1)),n(s,{to:"/download-documents",class:"text-blue-600 hover:text-blue-800 font-semibold"},{default:c(()=>t[13]||(t[13]=[i("下载文档 →")])),_:1,__:[13]})])])])]),t[17]||(t[17]=f('<div class="lg:col-span-1"><div class="bg-blue-50 rounded-xl p-6 mb-8" data-aos="fade-left"><h3 class="text-xl font-bold text-gray-800 mb-4">联系技术支持</h3><div class="space-y-4"><div class="flex items-center"><i class="fas fa-phone text-blue-600 mr-3"></i><div><p class="font-semibold text-gray-800">技术热线</p><a href="tel:159-8667-2052" class="text-gray-600 hover:text-blue-600 transition duration-300">159-8667-2052</a></div></div><div class="flex items-center"><i class="fas fa-envelope text-blue-600 mr-3"></i><div><p class="font-semibold text-gray-800">技术邮箱</p><a href="mailto:<EMAIL>" class="text-gray-600 hover:text-blue-600 transition duration-300"><EMAIL></a></div></div><div class="flex items-center"><i class="fas fa-clock text-blue-600 mr-3"></i><div><p class="font-semibold text-gray-800">服务时间</p><p class="text-gray-600">周一至周六 8:55-18:00</p></div></div></div></div></div>',1))])])])])}const ft=_(Mn,[["render",Jn]]),Zn={name:"DownloadDocuments",setup(){F(()=>{N.init({duration:1e3,once:!0})})}},t0={class:"download-documents"};function e0(o,t,r,a,g,x){return u(),p("div",t0,t[0]||(t[0]=[f('<section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8" data-v-33d61996><div class="container mx-auto px-4" data-v-33d61996><div class="text-center" data-v-33d61996><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-33d61996>使用说明</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-33d61996>根据您的产品型号和使用场景，参考以下使用说明进行操作</p></div></div></section><section class="py-12 bg-white" data-v-33d61996><div class="container mx-auto px-4" data-v-33d61996><div class="space-y-6" data-v-33d61996><div class="doc-card bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition duration-300 flex flex-col md:flex-row md:items-center justify-between" data-aos="fade-up" data-v-33d61996><div class="flex items-center mb-4 md:mb-0" data-v-33d61996><i class="fas fa-file-pdf text-red-500 text-2xl mr-3" data-v-33d61996></i><h3 class="text-lg font-semibold text-gray-800" data-v-33d61996>安卓人脸消费机使用说明</h3></div><p class="text-gray-600 mb-4 md:mb-0 md:mx-8 flex-grow" data-v-33d61996>适用于P301-2D-2W台式人脸消费机等安卓人脸消费机系列产品的使用说明文档。</p><a href="#" class="text-blue-600 hover:text-blue-800 font-semibold whitespace-nowrap" data-v-33d61996>下载文档 →</a></div><div class="doc-card bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition duration-300 flex flex-col md:flex-row md:items-center justify-between" data-aos="fade-up" data-aos-delay="200" data-v-33d61996><div class="flex items-center mb-4 md:mb-0" data-v-33d61996><i class="fas fa-file-pdf text-red-500 text-2xl mr-3" data-v-33d61996></i><h3 class="text-lg font-semibold text-gray-800" data-v-33d61996>ARM消费机使用说明</h3></div><p class="text-gray-600 mb-4 md:mb-0 md:mx-8 flex-grow" data-v-33d61996>适用于A201-IC-2W ARM消费机等ARM架构消费机系列产品的使用说明文档。</p><a href="#" class="text-blue-600 hover:text-blue-800 font-semibold whitespace-nowrap" data-v-33d61996>下载文档 →</a></div><div class="doc-card bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition duration-300 flex flex-col md:flex-row md:items-center justify-between" data-aos="fade-up" data-aos-delay="400" data-v-33d61996><div class="flex items-center mb-4 md:mb-0" data-v-33d61996><i class="fas fa-file-pdf text-red-500 text-2xl mr-3" data-v-33d61996></i><h3 class="text-lg font-semibold text-gray-800" data-v-33d61996>人脸测温通道闸机使用说明</h3></div><p class="text-gray-600 mb-4 md:mb-0 md:mx-8 flex-grow" data-v-33d61996>适用于YKT-TG100立式测温闸机等人脸测温通道闸机系列产品的使用说明文档。</p><a href="#" class="text-blue-600 hover:text-blue-800 font-semibold whitespace-nowrap" data-v-33d61996>下载文档 →</a></div><div class="doc-card bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition duration-300 flex flex-col md:flex-row md:items-center justify-between" data-aos="fade-up" data-aos-delay="600" data-v-33d61996><div class="flex items-center mb-4 md:mb-0" data-v-33d61996><i class="fas fa-file-pdf text-red-500 text-2xl mr-3" data-v-33d61996></i><h3 class="text-lg font-semibold text-gray-800" data-v-33d61996>水/电控机使用说明</h3></div><p class="text-gray-600 mb-4 md:mb-0 md:mx-8 flex-grow" data-v-33d61996>适用于YKT-WC100智能水控机等水/电控机系列产品的使用说明文档。</p><a href="#" class="text-blue-600 hover:text-blue-800 font-semibold whitespace-nowrap" data-v-33d61996>下载文档 →</a></div><div class="doc-card bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition duration-300 flex flex-col md:flex-row md:items-center justify-between" data-aos="fade-up" data-aos-delay="800" data-v-33d61996><div class="flex items-center mb-4 md:mb-0" data-v-33d61996><i class="fas fa-file-pdf text-red-500 text-2xl mr-3" data-v-33d61996></i><h3 class="text-lg font-semibold text-gray-800" data-v-33d61996>门禁机使用说明</h3></div><p class="text-gray-600 mb-4 md:mb-0 md:mx-8 flex-grow" data-v-33d61996>适用于各类门禁控制器产品的使用说明文档。</p><a href="#" class="text-blue-600 hover:text-blue-800 font-semibold whitespace-nowrap" data-v-33d61996>下载文档 →</a></div><div class="doc-card bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition duration-300 flex flex-col md:flex-row md:items-center justify-between" data-aos="fade-up" data-aos-delay="1000" data-v-33d61996><div class="flex items-center mb-4 md:mb-0" data-v-33d61996><i class="fas fa-file-pdf text-red-500 text-2xl mr-3" data-v-33d61996></i><h3 class="text-lg font-semibold text-gray-800" data-v-33d61996>脸爱云平台使用说明</h3></div><p class="text-gray-600 mb-4 md:mb-0 md:mx-8 flex-grow" data-v-33d61996>适用于脸爱云平台基础版等云平台产品的使用说明文档。</p><a href="#" class="text-blue-600 hover:text-blue-800 font-semibold whitespace-nowrap" data-v-33d61996>下载文档 →</a></div></div></div></section>',2)]))}const a0=_(Zn,[["render",e0],["__scopeId","data-v-33d61996"]]),s0={name:"ProductRelease",components:{Pagination:tt},setup(){const o=m([{id:1,title:"新一代人脸识别消费机正式发布",content:"优卡特最新推出的人脸识别消费机采用先进的AI算法，识别精度达到99.9%，响应速度提升50%。该产品将广泛应用于学校、企业、医院等场所。",date:"2024-01-15",category:"产品发布"},{id:2,title:"智能水控机WC100正式上市",content:"YKT-WC100智能水控机采用IC卡技术，支持多种支付方式，具有节水环保、使用便捷等特点，是宿舍、公寓的理想选择。",date:"2024-01-10",category:"产品发布"},{id:3,title:"ARM消费机A201系列发布",content:"基于ARM架构的新一代消费机，性能更强劲，功耗更低，支持多种识别方式，为用户提供更好的使用体验。",date:"2024-01-05",category:"产品发布"},{id:4,title:"人脸测温通道闸机TG100发布",content:"集成人脸识别和体温检测功能的智能通道闸机，适用于办公楼、学校、医院等需要健康监测的场所。",date:"2024-01-01",category:"产品发布"},{id:5,title:"校园共享设备WM100洗衣机上线",content:"专为校园设计的共享洗衣机，支持手机支付，远程监控，为学生提供便捷的洗衣服务。",date:"2023-12-28",category:"产品发布"}]),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedItems:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j}=et(o,10),C=y=>{console.log("打开产品发布新闻详情:",y),alert(`点击了产品发布新闻: ${y}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedNews:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j,openNewsDetail:C}}},o0={class:"py-12 bg-gray-50"},l0={class:"container mx-auto px-4"},d0={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},r0={class:"pt-8 pb-20 bg-white"},i0={class:"container mx-auto px-4"},n0={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},c0={class:"lg:col-span-2"},u0={class:"grid gap-8"},p0={class:"space-y-6"},g0={key:0},x0=["onClick"],f0=["alt"],v0={class:"p-4 flex-1"},m0={class:"flex items-center mb-2"},b0={class:"bg-green-100 text-green-600 px-2 py-1 rounded text-xs"},h0={class:"text-gray-500 text-xs ml-2"},y0={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-green-600 transition duration-300 cursor-pointer"},w0={class:"text-gray-600 text-sm"},_0={key:1,class:"text-center py-16"},$0={class:"max-w-md mx-auto"},k0={class:"text-gray-500 mb-4"},C0={class:"font-medium text-green-600"},P0={class:"lg:col-span-1"},j0={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},F0={class:"relative"},I0={key:0,class:"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},N0={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},S0={class:"space-y-3"};function A0(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-green-600 to-green-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-d219880f><div class="absolute inset-0 bg-black opacity-10" data-v-d219880f></div><div class="container mx-auto px-4 relative z-10" data-v-d219880f><div class="text-center" data-v-d219880f><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-d219880f>产品发布</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-d219880f>了解优卡特最新产品发布动态</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-d219880f></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-d219880f></div></section>',1)),e("section",o0,[e("div",l0,[e("div",d0,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-green-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/company-news-category",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-green-600"},{default:c(()=>t[5]||(t[5]=[i(" 公司新闻 ")])),_:1,__:[5]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-green-600 text-white"}," 产品发布 ",-1)),n(s,{to:"/news/exhibition",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-green-600"},{default:c(()=>t[6]||(t[6]=[i(" 展会活动 ")])),_:1,__:[6]}),n(s,{to:"/news/corporate-culture",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-green-600"},{default:c(()=>t[7]||(t[7]=[i(" 企业文化 ")])),_:1,__:[7]}),n(s,{to:"/news/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-green-600"},{default:c(()=>t[8]||(t[8]=[i(" 技术创新 ")])),_:1,__:[8]})])])]),e("section",r0,[e("div",i0,[e("div",n0,[e("div",c0,[e("div",u0,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(d=>a.openNewsDetail("product-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-d219880f><img src="https://placehold.co/800x400" alt="新一代人脸识别消费机正式发布" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-d219880f><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-d219880f> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-d219880f><div class="flex items-center mb-2" data-v-d219880f><span class="bg-green-100 text-green-600 px-3 py-1 rounded text-sm" data-v-d219880f>产品发布</span><span class="text-white text-sm ml-3" data-v-d219880f>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-green-200 transition-colors duration-300" data-v-d219880f>新一代人脸识别消费机正式发布</h2><p class="text-gray-200" data-v-d219880f>优卡特最新推出的人脸识别消费机采用先进的AI算法，识别精度达到99.9%，响应速度提升50%...</p></div></div>',1)]))):h("",!0),e("div",p0,[a.paginatedNews.length>0?(u(),p("div",g0,[(u(!0),p(R,null,W(a.paginatedNews,d=>(u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,f0),e("div",v0,[e("div",m0,[e("span",b0,v(d.category),1),e("span",h0,v(d.date),1)]),e("h4",y0,v(d.title),1),e("p",w0,v(d.content),1)])],8,x0))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",_0,[e("div",$0,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",k0,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",C0,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",P0,[e("div",j0,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-green-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",F0,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-green-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索产品发布资讯...","onUpdate:modelValue":t[2]||(t[2]=d=>a.searchKeyword=d),onKeyup:t[3]||(t[3]=H((...d)=>o.searchNews&&o.searchNews(...d),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-green-50 border border-green-200 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-400 focus:bg-white transition-all duration-200 placeholder-green-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",I0,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-d219880f><i class="fas fa-fire text-red-500 mr-2" data-v-d219880f></i> 热门产品发布 </h3><div class="space-y-4" data-v-d219880f><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-d219880f><h4 class="text-sm font-semibold text-gray-800 group-hover:text-green-600 transition-colors duration-300 mb-2" data-v-d219880f>新一代人脸识别消费机发布</h4><p class="text-xs text-gray-500" data-v-d219880f>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-d219880f><h4 class="text-sm font-semibold text-gray-800 group-hover:text-green-600 transition-colors duration-300 mb-2" data-v-d219880f>智能水控机WC100上市</h4><p class="text-xs text-gray-500" data-v-d219880f>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-d219880f><h4 class="text-sm font-semibold text-gray-800 group-hover:text-green-600 transition-colors duration-300 mb-2" data-v-d219880f>ARM消费机A201发布</h4><p class="text-xs text-gray-500" data-v-d219880f>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",N0,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 新闻分类 ")],-1)),e("div",S0,[n(s,{to:"/news/company-news-category",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"公司新闻",-1),e("span",{class:"bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[19]}),t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-green-100 rounded-lg shadow-sm"},[e("span",{class:"text-green-600 font-semibold"},"产品发布"),e("span",{class:"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs"},"8")],-1)),n(s,{to:"/news/exhibition",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"展会活动",-1),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"6",-1)])),_:1,__:[20]}),n(s,{to:"/news/corporate-culture",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"企业文化",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[21]}),n(s,{to:"/news/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-green-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"9",-1)])),_:1,__:[22]})])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"green",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-green-600 to-green-800 text-white" data-v-d219880f><div class="container mx-auto px-4" data-v-d219880f><div class="text-center mb-12" data-aos="fade-up" data-v-d219880f><h2 class="text-3xl font-bold mb-4" data-v-d219880f>联系我们</h2><p class="text-xl" data-v-d219880f>如需了解更多产品信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-d219880f><div class="text-center" data-v-d219880f><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-d219880f></i><h3 class="text-xl font-semibold mb-2" data-v-d219880f>地址</h3><p data-v-d219880f>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-d219880f><i class="fas fa-phone text-4xl mb-4" data-v-d219880f></i><h3 class="text-xl font-semibold mb-2" data-v-d219880f>电话</h3><p data-v-d219880f>159-8667-2052</p></div><div class="text-center" data-v-d219880f><i class="fas fa-envelope text-4xl mb-4" data-v-d219880f></i><h3 class="text-xl font-semibold mb-2" data-v-d219880f>邮箱</h3><p data-v-d219880f><EMAIL></p></div></div></div></section>',1))])}const M0=_(s0,[["render",A0],["__scopeId","data-v-d219880f"]]),K0={name:"Exhibition",setup(){const o=m(""),t=m([{id:1,title:"优卡特参加2024年智能设备展览会",content:"公司携最新产品亮相2024年国际智能设备展览会，展示了人脸识别、指纹识别等多款智能终端设备，获得了业界的广泛关注。",date:"2024-01-15",category:"展会活动"},{id:2,title:"第78届中国教育装备展示会圆满结束",content:"优卡特在教育装备展上展示了智慧校园解决方案，包括校园一卡通、人脸识别系统等产品，受到教育行业客户的高度关注。",date:"2024-01-10",category:"展会活动"},{id:3,title:"2024年安防技术博览会成功参展",content:"公司在安防展上重点展示了人脸测温通道闸机、门禁控制器等安防产品，与多家合作伙伴达成合作意向。",date:"2024-01-05",category:"展会活动"},{id:4,title:"智慧城市建设博览会优卡特展台人气爆棚",content:"在智慧城市展上，优卡特的智能终端设备和解决方案吸引了众多参观者，现场签约多个项目。",date:"2024-01-01",category:"展会活动"},{id:5,title:"2023年物联网技术展览会成功举办",content:"优卡特展示了基于物联网技术的智能设备，包括云平台管理系统，展现了公司在物联网领域的技术实力。",date:"2023-12-28",category:"展会活动"}]),r=A(()=>o.value.trim()?t.value.filter(s=>s.title.toLowerCase().includes(o.value.toLowerCase())||s.content.toLowerCase().includes(o.value.toLowerCase())):t.value),a=()=>{},g=()=>{o.value=""},x=s=>{console.log("打开展会活动新闻详情:",s),alert(`点击了展会活动新闻: ${s}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{searchKeyword:o,filteredNews:r,searchNews:a,clearSearch:g,openNewsDetail:x}}},T0={class:"py-12 bg-gray-50"},D0={class:"container mx-auto px-4"},z0={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},R0={class:"pt-8 pb-20 bg-white"},W0={class:"container mx-auto px-4"},E0={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},L0={class:"lg:col-span-2"},Y0={class:"grid gap-8"},B0={class:"space-y-6"},q0={key:0},G0=["onClick"],H0=["alt"],O0={class:"p-4 flex-1"},U0={class:"flex items-center mb-2"},V0={class:"bg-purple-100 text-purple-600 px-2 py-1 rounded text-xs"},Q0={class:"text-gray-500 text-xs ml-2"},X0={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-purple-600 transition duration-300 cursor-pointer"},J0={class:"text-gray-600 text-sm"},Z0={key:1,class:"text-center py-16"},tc={class:"max-w-md mx-auto"},ec={class:"text-gray-500 mb-4"},ac={class:"font-medium text-purple-600"},sc={class:"lg:col-span-1"},oc={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},lc={class:"relative"},dc={key:0,class:"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},rc={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},ic={class:"space-y-3"};function nc(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-purple-600 to-purple-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-c79d9eb2><div class="absolute inset-0 bg-black opacity-10" data-v-c79d9eb2></div><div class="container mx-auto px-4 relative z-10" data-v-c79d9eb2><div class="text-center" data-v-c79d9eb2><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-c79d9eb2>展会活动</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-c79d9eb2>了解优卡特参与的各类展会活动</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-c79d9eb2></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-c79d9eb2></div></section>',1)),e("section",T0,[e("div",D0,[e("div",z0,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-purple-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/company-news-category",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-purple-600"},{default:c(()=>t[5]||(t[5]=[i(" 公司新闻 ")])),_:1,__:[5]}),n(s,{to:"/news/product-release",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-purple-600"},{default:c(()=>t[6]||(t[6]=[i(" 产品发布 ")])),_:1,__:[6]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-purple-600 text-white"}," 展会活动 ",-1)),n(s,{to:"/news/corporate-culture",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-purple-600"},{default:c(()=>t[7]||(t[7]=[i(" 企业文化 ")])),_:1,__:[7]}),n(s,{to:"/news/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-purple-600"},{default:c(()=>t[8]||(t[8]=[i(" 技术创新 ")])),_:1,__:[8]})])])]),e("section",R0,[e("div",W0,[e("div",E0,[e("div",L0,[e("div",Y0,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(l=>a.openNewsDetail("exhibition-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-c79d9eb2><img src="https://placehold.co/800x400" alt="优卡特参加2024年智能设备展览会" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-c79d9eb2><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-c79d9eb2> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-c79d9eb2><div class="flex items-center mb-2" data-v-c79d9eb2><span class="bg-purple-100 text-purple-600 px-3 py-1 rounded text-sm" data-v-c79d9eb2>展会活动</span><span class="text-white text-sm ml-3" data-v-c79d9eb2>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-purple-200 transition-colors duration-300" data-v-c79d9eb2>优卡特参加2024年智能设备展览会</h2><p class="text-gray-200" data-v-c79d9eb2>公司携最新产品亮相2024年国际智能设备展览会，展示了人脸识别、指纹识别等多款智能终端设备...</p></div></div>',1)]))):h("",!0),e("div",B0,[a.filteredNews.length>0?(u(),p("div",q0,[(u(!0),p(R,null,W(a.filteredNews,l=>(u(),p("a",{href:"#",onClick:T(d=>a.openNewsDetail(l.id),["prevent"]),key:l.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:l.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,H0),e("div",O0,[e("div",U0,[e("span",V0,v(l.category),1),e("span",Q0,v(l.date),1)]),e("h4",X0,v(l.title),1),e("p",J0,v(l.content),1)])],8,G0))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",Z0,[e("div",tc,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",ec,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",ac,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...l)=>a.clearSearch&&a.clearSearch(...l)),class:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",sc,[e("div",oc,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-purple-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",lc,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索展会活动资讯...","onUpdate:modelValue":t[2]||(t[2]=l=>a.searchKeyword=l),onKeyup:t[3]||(t[3]=H((...l)=>a.searchNews&&a.searchNews(...l),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-purple-50 border border-purple-200 rounded-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-400 focus:bg-white transition-all duration-200 placeholder-purple-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",dc,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-c79d9eb2><i class="fas fa-fire text-red-500 mr-2" data-v-c79d9eb2></i> 热门展会活动 </h3><div class="space-y-4" data-v-c79d9eb2><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-c79d9eb2><h4 class="text-sm font-semibold text-gray-800 group-hover:text-purple-600 transition-colors duration-300 mb-2" data-v-c79d9eb2>2024智能设备展览会</h4><p class="text-xs text-gray-500" data-v-c79d9eb2>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-c79d9eb2><h4 class="text-sm font-semibold text-gray-800 group-hover:text-purple-600 transition-colors duration-300 mb-2" data-v-c79d9eb2>教育装备展示会</h4><p class="text-xs text-gray-500" data-v-c79d9eb2>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-c79d9eb2><h4 class="text-sm font-semibold text-gray-800 group-hover:text-purple-600 transition-colors duration-300 mb-2" data-v-c79d9eb2>安防技术博览会</h4><p class="text-xs text-gray-500" data-v-c79d9eb2>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",rc,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 新闻分类 ")],-1)),e("div",ic,[n(s,{to:"/news/company-news-category",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-purple-600 transition-colors duration-300"},"公司新闻",-1),e("span",{class:"bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[19]}),n(s,{to:"/news/product-release",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-purple-600 transition-colors duration-300"},"产品发布",-1),e("span",{class:"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[20]}),t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-purple-100 rounded-lg shadow-sm"},[e("span",{class:"text-purple-600 font-semibold"},"展会活动"),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"6")],-1)),n(s,{to:"/news/corporate-culture",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-purple-600 transition-colors duration-300"},"企业文化",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[21]}),n(s,{to:"/news/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-purple-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"9",-1)])),_:1,__:[22]})])])])])])]),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-purple-600 to-purple-800 text-white" data-v-c79d9eb2><div class="container mx-auto px-4" data-v-c79d9eb2><div class="text-center mb-12" data-aos="fade-up" data-v-c79d9eb2><h2 class="text-3xl font-bold mb-4" data-v-c79d9eb2>联系我们</h2><p class="text-xl" data-v-c79d9eb2>如需了解更多展会信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-c79d9eb2><div class="text-center" data-v-c79d9eb2><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-c79d9eb2></i><h3 class="text-xl font-semibold mb-2" data-v-c79d9eb2>地址</h3><p data-v-c79d9eb2>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-c79d9eb2><i class="fas fa-phone text-4xl mb-4" data-v-c79d9eb2></i><h3 class="text-xl font-semibold mb-2" data-v-c79d9eb2>电话</h3><p data-v-c79d9eb2>159-8667-2052</p></div><div class="text-center" data-v-c79d9eb2><i class="fas fa-envelope text-4xl mb-4" data-v-c79d9eb2></i><h3 class="text-xl font-semibold mb-2" data-v-c79d9eb2>邮箱</h3><p data-v-c79d9eb2><EMAIL></p></div></div></div></section>',1))])}const cc=_(K0,[["render",nc],["__scopeId","data-v-c79d9eb2"]]),uc={name:"CorporateCulture",setup(){const o=m(""),t=m([{id:1,title:"优卡特举办2024年度员工表彰大会",content:"公司举办年度员工表彰大会，表彰在各个岗位上表现突出的优秀员工，弘扬企业文化，激励全体员工继续努力。",date:"2024-01-15",category:"企业文化"},{id:2,title:"公司组织员工团队建设活动",content:"为增强团队凝聚力，公司组织全体员工参加户外团建活动，通过各种团队协作游戏，增进同事间的友谊。",date:"2024-01-10",category:"企业文化"},{id:3,title:"优卡特企业价值观培训圆满结束",content:"公司举办企业价值观培训，帮助员工深入理解公司文化理念，提升员工的归属感和责任感。",date:"2024-01-05",category:"企业文化"},{id:4,title:"员工生日会温馨举办",content:"公司为本月生日的员工举办生日会，营造温馨的工作氛围，体现公司对员工的关爱。",date:"2024-01-01",category:"企业文化"},{id:5,title:"优卡特年终总结大会成功召开",content:"公司召开年终总结大会，回顾一年来的发展成果，表彰优秀员工，展望新年发展规划。",date:"2023-12-28",category:"企业文化"}]),r=A(()=>o.value.trim()?t.value.filter(s=>s.title.toLowerCase().includes(o.value.toLowerCase())||s.content.toLowerCase().includes(o.value.toLowerCase())):t.value),a=()=>{},g=()=>{o.value=""},x=s=>{console.log("打开企业文化新闻详情:",s),alert(`点击了企业文化新闻: ${s}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{searchKeyword:o,filteredNews:r,searchNews:a,clearSearch:g,openNewsDetail:x}}},pc={class:"py-12 bg-gray-50"},gc={class:"container mx-auto px-4"},xc={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},fc={class:"pt-8 pb-20 bg-white"},vc={class:"container mx-auto px-4"},mc={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},bc={class:"lg:col-span-2"},hc={class:"grid gap-8"},yc={class:"space-y-6"},wc={key:0},_c=["onClick"],$c=["alt"],kc={class:"p-4 flex-1"},Cc={class:"flex items-center mb-2"},Pc={class:"bg-orange-100 text-orange-600 px-2 py-1 rounded text-xs"},jc={class:"text-gray-500 text-xs ml-2"},Fc={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-orange-600 transition duration-300 cursor-pointer"},Ic={class:"text-gray-600 text-sm"},Nc={key:1,class:"text-center py-16"},Sc={class:"max-w-md mx-auto"},Ac={class:"text-gray-500 mb-4"},Mc={class:"font-medium text-orange-600"},Kc={class:"lg:col-span-1"},Tc={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Dc={class:"relative"},zc={key:0,class:"bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Rc={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},Wc={class:"space-y-3"};function Ec(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-orange-600 to-orange-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-fd5dc80d><div class="absolute inset-0 bg-black opacity-10" data-v-fd5dc80d></div><div class="container mx-auto px-4 relative z-10" data-v-fd5dc80d><div class="text-center" data-v-fd5dc80d><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-fd5dc80d>企业文化</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-fd5dc80d>了解优卡特的企业文化和价值观</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-fd5dc80d></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-fd5dc80d></div></section>',1)),e("section",pc,[e("div",gc,[e("div",xc,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-orange-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/company-news-category",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-orange-600"},{default:c(()=>t[5]||(t[5]=[i(" 公司新闻 ")])),_:1,__:[5]}),n(s,{to:"/news/product-release",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-orange-600"},{default:c(()=>t[6]||(t[6]=[i(" 产品发布 ")])),_:1,__:[6]}),n(s,{to:"/news/exhibition",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-orange-600"},{default:c(()=>t[7]||(t[7]=[i(" 展会活动 ")])),_:1,__:[7]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-orange-600 text-white"}," 企业文化 ",-1)),n(s,{to:"/news/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-orange-600"},{default:c(()=>t[8]||(t[8]=[i(" 技术创新 ")])),_:1,__:[8]})])])]),e("section",fc,[e("div",vc,[e("div",mc,[e("div",bc,[e("div",hc,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(l=>a.openNewsDetail("culture-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-fd5dc80d><img src="https://placehold.co/800x400" alt="优卡特举办2024年度员工表彰大会" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-fd5dc80d><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-fd5dc80d> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-fd5dc80d><div class="flex items-center mb-2" data-v-fd5dc80d><span class="bg-orange-100 text-orange-600 px-3 py-1 rounded text-sm" data-v-fd5dc80d>企业文化</span><span class="text-white text-sm ml-3" data-v-fd5dc80d>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-orange-200 transition-colors duration-300" data-v-fd5dc80d>优卡特举办2024年度员工表彰大会</h2><p class="text-gray-200" data-v-fd5dc80d>公司举办年度员工表彰大会，表彰在各个岗位上表现突出的优秀员工，弘扬企业文化...</p></div></div>',1)]))):h("",!0),e("div",yc,[a.filteredNews.length>0?(u(),p("div",wc,[(u(!0),p(R,null,W(a.filteredNews,l=>(u(),p("a",{href:"#",onClick:T(d=>a.openNewsDetail(l.id),["prevent"]),key:l.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:l.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,$c),e("div",kc,[e("div",Cc,[e("span",Pc,v(l.category),1),e("span",jc,v(l.date),1)]),e("h4",Fc,v(l.title),1),e("p",Ic,v(l.content),1)])],8,_c))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",Nc,[e("div",Sc,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",Ac,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",Mc,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...l)=>a.clearSearch&&a.clearSearch(...l)),class:"bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",Kc,[e("div",Tc,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-orange-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",Dc,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-orange-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索企业文化资讯...","onUpdate:modelValue":t[2]||(t[2]=l=>a.searchKeyword=l),onKeyup:t[3]||(t[3]=H((...l)=>a.searchNews&&a.searchNews(...l),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-orange-50 border border-orange-200 rounded-full focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-400 focus:bg-white transition-all duration-200 placeholder-orange-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",zc,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-fd5dc80d><i class="fas fa-fire text-red-500 mr-2" data-v-fd5dc80d></i> 热门企业文化 </h3><div class="space-y-4" data-v-fd5dc80d><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-fd5dc80d><h4 class="text-sm font-semibold text-gray-800 group-hover:text-orange-600 transition-colors duration-300 mb-2" data-v-fd5dc80d>年度员工表彰大会</h4><p class="text-xs text-gray-500" data-v-fd5dc80d>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-fd5dc80d><h4 class="text-sm font-semibold text-gray-800 group-hover:text-orange-600 transition-colors duration-300 mb-2" data-v-fd5dc80d>团队建设活动</h4><p class="text-xs text-gray-500" data-v-fd5dc80d>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-fd5dc80d><h4 class="text-sm font-semibold text-gray-800 group-hover:text-orange-600 transition-colors duration-300 mb-2" data-v-fd5dc80d>企业价值观培训</h4><p class="text-xs text-gray-500" data-v-fd5dc80d>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",Rc,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 新闻分类 ")],-1)),e("div",Wc,[n(s,{to:"/news/company-news-category",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-orange-600 transition-colors duration-300"},"公司新闻",-1),e("span",{class:"bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[19]}),n(s,{to:"/news/product-release",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-orange-600 transition-colors duration-300"},"产品发布",-1),e("span",{class:"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[20]}),n(s,{to:"/news/exhibition",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-orange-600 transition-colors duration-300"},"展会活动",-1),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"6",-1)])),_:1,__:[21]}),t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-orange-100 rounded-lg shadow-sm"},[e("span",{class:"text-orange-600 font-semibold"},"企业文化"),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"15")],-1)),n(s,{to:"/news/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-orange-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"9",-1)])),_:1,__:[22]})])])])])])]),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-orange-600 to-orange-800 text-white" data-v-fd5dc80d><div class="container mx-auto px-4" data-v-fd5dc80d><div class="text-center mb-12" data-aos="fade-up" data-v-fd5dc80d><h2 class="text-3xl font-bold mb-4" data-v-fd5dc80d>联系我们</h2><p class="text-xl" data-v-fd5dc80d>如需了解更多企业文化信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-fd5dc80d><div class="text-center" data-v-fd5dc80d><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-fd5dc80d></i><h3 class="text-xl font-semibold mb-2" data-v-fd5dc80d>地址</h3><p data-v-fd5dc80d>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-fd5dc80d><i class="fas fa-phone text-4xl mb-4" data-v-fd5dc80d></i><h3 class="text-xl font-semibold mb-2" data-v-fd5dc80d>电话</h3><p data-v-fd5dc80d>159-8667-2052</p></div><div class="text-center" data-v-fd5dc80d><i class="fas fa-envelope text-4xl mb-4" data-v-fd5dc80d></i><h3 class="text-xl font-semibold mb-2" data-v-fd5dc80d>邮箱</h3><p data-v-fd5dc80d><EMAIL></p></div></div></div></section>',1))])}const Lc=_(uc,[["render",Ec],["__scopeId","data-v-fd5dc80d"]]),Yc={name:"TechnicalInnovation",setup(){const o=m(""),t=m([{id:1,title:"优卡特AI算法技术获得重大突破",content:"公司自主研发的人脸识别AI算法在准确率和速度方面取得重大突破，识别精度提升至99.9%，响应时间缩短至0.3秒。",date:"2024-01-15",category:"技术创新"},{id:2,title:"脸爱云平台架构全面升级",content:"公司对脸爱云平台进行全面架构升级，采用微服务架构，提升系统稳定性和扩展性，支持更大规模的设备接入。",date:"2024-01-10",category:"技术创新"},{id:3,title:"物联网技术在智能设备中的创新应用",content:"公司将最新的物联网技术应用到智能终端设备中，实现设备间的智能互联，提升用户体验。",date:"2024-01-05",category:"技术创新"},{id:4,title:"区块链技术在一卡通系统中的应用研究",content:"公司开始研究区块链技术在一卡通系统中的应用，旨在提升系统安全性和数据可信度。",date:"2024-01-01",category:"技术创新"},{id:5,title:"边缘计算技术助力智能设备升级",content:"公司将边缘计算技术应用到智能设备中，实现本地数据处理，减少网络延迟，提升设备响应速度。",date:"2023-12-28",category:"技术创新"}]),r=A(()=>o.value.trim()?t.value.filter(s=>s.title.toLowerCase().includes(o.value.toLowerCase())||s.content.toLowerCase().includes(o.value.toLowerCase())):t.value),a=()=>{},g=()=>{o.value=""},x=s=>{console.log("打开技术创新新闻详情:",s),alert(`点击了技术创新新闻: ${s}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{searchKeyword:o,filteredNews:r,searchNews:a,clearSearch:g,openNewsDetail:x}}},Bc={class:"py-12 bg-gray-50"},qc={class:"container mx-auto px-4"},Gc={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},Hc={class:"pt-8 pb-20 bg-white"},Oc={class:"container mx-auto px-4"},Uc={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Vc={class:"lg:col-span-2"},Qc={class:"grid gap-8"},Xc={class:"space-y-6"},Jc={key:0},Zc=["onClick"],tu=["alt"],eu={class:"p-4 flex-1"},au={class:"flex items-center mb-2"},su={class:"bg-red-100 text-red-600 px-2 py-1 rounded text-xs"},ou={class:"text-gray-500 text-xs ml-2"},lu={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-red-600 transition duration-300 cursor-pointer"},du={class:"text-gray-600 text-sm"},ru={key:1,class:"text-center py-16"},iu={class:"max-w-md mx-auto"},nu={class:"text-gray-500 mb-4"},cu={class:"font-medium text-red-600"},uu={class:"lg:col-span-1"},pu={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},gu={class:"relative"},xu={key:0,class:"bg-gradient-to-br from-red-50 to-pink-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},fu={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},vu={class:"space-y-3"};function mu(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-red-600 to-red-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-02f90155><div class="absolute inset-0 bg-black opacity-10" data-v-02f90155></div><div class="container mx-auto px-4 relative z-10" data-v-02f90155><div class="text-center" data-v-02f90155><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-02f90155>技术创新</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-02f90155>了解优卡特的技术创新成果</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-02f90155></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-02f90155></div></section>',1)),e("section",Bc,[e("div",qc,[e("div",Gc,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-red-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/company-news-category",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-red-600"},{default:c(()=>t[5]||(t[5]=[i(" 公司新闻 ")])),_:1,__:[5]}),n(s,{to:"/news/product-release",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-red-600"},{default:c(()=>t[6]||(t[6]=[i(" 产品发布 ")])),_:1,__:[6]}),n(s,{to:"/news/exhibition",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-red-600"},{default:c(()=>t[7]||(t[7]=[i(" 展会活动 ")])),_:1,__:[7]}),n(s,{to:"/news/corporate-culture",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-red-600"},{default:c(()=>t[8]||(t[8]=[i(" 企业文化 ")])),_:1,__:[8]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-red-600 text-white"}," 技术创新 ",-1))])])]),e("section",Hc,[e("div",Oc,[e("div",Uc,[e("div",Vc,[e("div",Qc,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(l=>a.openNewsDetail("tech-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-02f90155><img src="https://placehold.co/800x400" alt="优卡特AI算法技术获得重大突破" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-02f90155><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-02f90155> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-02f90155><div class="flex items-center mb-2" data-v-02f90155><span class="bg-red-100 text-red-600 px-3 py-1 rounded text-sm" data-v-02f90155>技术创新</span><span class="text-white text-sm ml-3" data-v-02f90155>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-red-200 transition-colors duration-300" data-v-02f90155>优卡特AI算法技术获得重大突破</h2><p class="text-gray-200" data-v-02f90155>公司自主研发的人脸识别AI算法在准确率和速度方面取得重大突破，识别精度提升至99.9%...</p></div></div>',1)]))):h("",!0),e("div",Xc,[a.filteredNews.length>0?(u(),p("div",Jc,[(u(!0),p(R,null,W(a.filteredNews,l=>(u(),p("a",{href:"#",onClick:T(d=>a.openNewsDetail(l.id),["prevent"]),key:l.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:l.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,tu),e("div",eu,[e("div",au,[e("span",su,v(l.category),1),e("span",ou,v(l.date),1)]),e("h4",lu,v(l.title),1),e("p",du,v(l.content),1)])],8,Zc))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",ru,[e("div",iu,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",nu,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",cu,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...l)=>a.clearSearch&&a.clearSearch(...l)),class:"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",uu,[e("div",pu,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-red-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",gu,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-red-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索技术创新资讯...","onUpdate:modelValue":t[2]||(t[2]=l=>a.searchKeyword=l),onKeyup:t[3]||(t[3]=H((...l)=>a.searchNews&&a.searchNews(...l),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-red-50 border border-red-200 rounded-full focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-400 focus:bg-white transition-all duration-200 placeholder-red-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",xu,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-02f90155><i class="fas fa-fire text-red-500 mr-2" data-v-02f90155></i> 热门技术创新 </h3><div class="space-y-4" data-v-02f90155><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-02f90155><h4 class="text-sm font-semibold text-gray-800 group-hover:text-red-600 transition-colors duration-300 mb-2" data-v-02f90155>AI算法技术突破</h4><p class="text-xs text-gray-500" data-v-02f90155>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-02f90155><h4 class="text-sm font-semibold text-gray-800 group-hover:text-red-600 transition-colors duration-300 mb-2" data-v-02f90155>云平台架构升级</h4><p class="text-xs text-gray-500" data-v-02f90155>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-02f90155><h4 class="text-sm font-semibold text-gray-800 group-hover:text-red-600 transition-colors duration-300 mb-2" data-v-02f90155>物联网技术应用</h4><p class="text-xs text-gray-500" data-v-02f90155>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",fu,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 新闻分类 ")],-1)),e("div",vu,[n(s,{to:"/news/company-news-category",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-red-600 transition-colors duration-300"},"公司新闻",-1),e("span",{class:"bg-blue-100 text-blue-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[19]}),n(s,{to:"/news/product-release",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-red-600 transition-colors duration-300"},"产品发布",-1),e("span",{class:"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[20]}),n(s,{to:"/news/exhibition",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-red-600 transition-colors duration-300"},"展会活动",-1),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"6",-1)])),_:1,__:[21]}),n(s,{to:"/news/corporate-culture",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-red-600 transition-colors duration-300"},"企业文化",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[22]}),t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-red-100 rounded-lg shadow-sm"},[e("span",{class:"text-red-600 font-semibold"},"技术创新"),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"9")],-1))])])])])])]),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-red-600 to-red-800 text-white" data-v-02f90155><div class="container mx-auto px-4" data-v-02f90155><div class="text-center mb-12" data-aos="fade-up" data-v-02f90155><h2 class="text-3xl font-bold mb-4" data-v-02f90155>联系我们</h2><p class="text-xl" data-v-02f90155>如需了解更多技术创新信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-02f90155><div class="text-center" data-v-02f90155><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-02f90155></i><h3 class="text-xl font-semibold mb-2" data-v-02f90155>地址</h3><p data-v-02f90155>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-02f90155><i class="fas fa-phone text-4xl mb-4" data-v-02f90155></i><h3 class="text-xl font-semibold mb-2" data-v-02f90155>电话</h3><p data-v-02f90155>159-8667-2052</p></div><div class="text-center" data-v-02f90155><i class="fas fa-envelope text-4xl mb-4" data-v-02f90155></i><h3 class="text-xl font-semibold mb-2" data-v-02f90155>邮箱</h3><p data-v-02f90155><EMAIL></p></div></div></div></section>',1))])}const bu=_(Yc,[["render",mu],["__scopeId","data-v-02f90155"]]),hu={name:"CompanyNewsCategory",setup(){const o=m(""),t=m([{id:1,title:"优卡特公司总部迁至新址",content:"为适应公司快速发展需要，优卡特公司总部正式迁至深圳市龙华新区大和路硅谷动力清湖园A9栋2楼，新办公环境将为员工提供更好的工作条件。",date:"2024-01-15",category:"公司新闻"},{id:2,title:"新任CEO正式上任",content:"经董事会决议，任命张先生为公司新任CEO，张先生拥有丰富的行业经验，将带领公司迈向新的发展阶段。",date:"2024-01-10",category:"公司新闻"},{id:3,title:"公司组织架构调整",content:"为提升运营效率，公司对组织架构进行调整，新设立产品研发部和市场拓展部，进一步优化业务流程。",date:"2024-01-05",category:"公司新闻"},{id:4,title:"优卡特获得新一轮融资",content:"公司成功完成B轮融资，融资金额达5000万元，将主要用于产品研发和市场拓展，加速公司发展步伐。",date:"2024-01-01",category:"公司新闻"},{id:5,title:"公司员工规模突破200人",content:"随着业务的快速发展，公司员工规模已突破200人，为满足发展需要，公司将继续招聘优秀人才。",date:"2023-12-28",category:"公司新闻"}]),r=A(()=>o.value.trim()?t.value.filter(s=>s.title.toLowerCase().includes(o.value.toLowerCase())||s.content.toLowerCase().includes(o.value.toLowerCase())):t.value),a=()=>{},g=()=>{o.value=""},x=s=>{console.log("打开公司新闻详情:",s),alert(`点击了公司新闻: ${s}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{searchKeyword:o,filteredNews:r,searchNews:a,clearSearch:g,openNewsDetail:x}}},yu={class:"py-12 bg-gray-50"},wu={class:"container mx-auto px-4"},_u={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},$u={class:"pt-8 pb-20 bg-white"},ku={class:"container mx-auto px-4"},Cu={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Pu={class:"lg:col-span-2"},ju={class:"grid gap-8"},Fu={class:"space-y-6"},Iu={key:0},Nu=["onClick"],Su=["alt"],Au={class:"p-4 flex-1"},Mu={class:"flex items-center mb-2"},Ku={class:"bg-indigo-100 text-indigo-600 px-2 py-1 rounded text-xs"},Tu={class:"text-gray-500 text-xs ml-2"},Du={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-indigo-600 transition duration-300 cursor-pointer"},zu={class:"text-gray-600 text-sm"},Ru={key:1,class:"text-center py-16"},Wu={class:"max-w-md mx-auto"},Eu={class:"text-gray-500 mb-4"},Lu={class:"font-medium text-indigo-600"},Yu={class:"lg:col-span-1"},Bu={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},qu={class:"relative"},Gu={key:0,class:"bg-gradient-to-br from-indigo-50 to-purple-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Hu={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},Ou={class:"space-y-3"};function Uu(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-indigo-600 to-indigo-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-9c992e26><div class="absolute inset-0 bg-black opacity-10" data-v-9c992e26></div><div class="container mx-auto px-4 relative z-10" data-v-9c992e26><div class="text-center" data-v-9c992e26><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-9c992e26>公司新闻</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-9c992e26>了解优卡特最新公司动态</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-9c992e26></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-9c992e26></div></section>',1)),e("section",yu,[e("div",wu,[e("div",_u,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-indigo-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-indigo-600 text-white"}," 公司新闻 ",-1)),n(s,{to:"/news/product-release",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-indigo-600"},{default:c(()=>t[5]||(t[5]=[i(" 产品发布 ")])),_:1,__:[5]}),n(s,{to:"/news/exhibition",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-indigo-600"},{default:c(()=>t[6]||(t[6]=[i(" 展会活动 ")])),_:1,__:[6]}),n(s,{to:"/news/corporate-culture",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-indigo-600"},{default:c(()=>t[7]||(t[7]=[i(" 企业文化 ")])),_:1,__:[7]}),n(s,{to:"/news/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-indigo-600"},{default:c(()=>t[8]||(t[8]=[i(" 技术创新 ")])),_:1,__:[8]})])])]),e("section",$u,[e("div",ku,[e("div",Cu,[e("div",Pu,[e("div",ju,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(l=>a.openNewsDetail("company-news-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-9c992e26><img src="https://placehold.co/800x400" alt="优卡特公司总部迁至新址" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-9c992e26><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-9c992e26> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-9c992e26><div class="flex items-center mb-2" data-v-9c992e26><span class="bg-indigo-100 text-indigo-600 px-3 py-1 rounded text-sm" data-v-9c992e26>公司新闻</span><span class="text-white text-sm ml-3" data-v-9c992e26>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-indigo-200 transition-colors duration-300" data-v-9c992e26>优卡特公司总部迁至新址</h2><p class="text-gray-200" data-v-9c992e26>为适应公司快速发展需要，优卡特公司总部正式迁至深圳市龙华新区大和路硅谷动力清湖园A9栋2楼...</p></div></div>',1)]))):h("",!0),e("div",Fu,[a.filteredNews.length>0?(u(),p("div",Iu,[(u(!0),p(R,null,W(a.filteredNews,l=>(u(),p("a",{href:"#",onClick:T(d=>a.openNewsDetail(l.id),["prevent"]),key:l.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:l.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,Su),e("div",Au,[e("div",Mu,[e("span",Ku,v(l.category),1),e("span",Tu,v(l.date),1)]),e("h4",Du,v(l.title),1),e("p",zu,v(l.content),1)])],8,Nu))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",Ru,[e("div",Wu,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",Eu,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",Lu,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...l)=>a.clearSearch&&a.clearSearch(...l)),class:"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",Yu,[e("div",Bu,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-indigo-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",qu,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-indigo-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索公司新闻...","onUpdate:modelValue":t[2]||(t[2]=l=>a.searchKeyword=l),onKeyup:t[3]||(t[3]=H((...l)=>a.searchNews&&a.searchNews(...l),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-indigo-50 border border-indigo-200 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-400 focus:bg-white transition-all duration-200 placeholder-indigo-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",Gu,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-9c992e26><i class="fas fa-fire text-red-500 mr-2" data-v-9c992e26></i> 热门公司新闻 </h3><div class="space-y-4" data-v-9c992e26><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-9c992e26><h4 class="text-sm font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors duration-300 mb-2" data-v-9c992e26>公司总部迁至新址</h4><p class="text-xs text-gray-500" data-v-9c992e26>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-9c992e26><h4 class="text-sm font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors duration-300 mb-2" data-v-9c992e26>新任CEO正式上任</h4><p class="text-xs text-gray-500" data-v-9c992e26>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-9c992e26><h4 class="text-sm font-semibold text-gray-800 group-hover:text-indigo-600 transition-colors duration-300 mb-2" data-v-9c992e26>公司组织架构调整</h4><p class="text-xs text-gray-500" data-v-9c992e26>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",Hu,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 新闻分类 ")],-1)),e("div",Ou,[t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-indigo-100 rounded-lg shadow-sm"},[e("span",{class:"text-indigo-600 font-semibold"},"公司新闻"),e("span",{class:"bg-indigo-100 text-indigo-600 px-2 py-1 rounded-full text-xs"},"12")],-1)),n(s,{to:"/news/product-release",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-indigo-600 transition-colors duration-300"},"产品发布",-1),e("span",{class:"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[19]}),n(s,{to:"/news/exhibition",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-indigo-600 transition-colors duration-300"},"展会活动",-1),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"6",-1)])),_:1,__:[20]}),n(s,{to:"/news/corporate-culture",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-indigo-600 transition-colors duration-300"},"企业文化",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[21]}),n(s,{to:"/news/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-indigo-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"9",-1)])),_:1,__:[22]})])])])])])]),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-indigo-600 to-indigo-800 text-white" data-v-9c992e26><div class="container mx-auto px-4" data-v-9c992e26><div class="text-center mb-12" data-aos="fade-up" data-v-9c992e26><h2 class="text-3xl font-bold mb-4" data-v-9c992e26>联系我们</h2><p class="text-xl" data-v-9c992e26>如需了解更多公司信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-9c992e26><div class="text-center" data-v-9c992e26><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-9c992e26></i><h3 class="text-xl font-semibold mb-2" data-v-9c992e26>地址</h3><p data-v-9c992e26>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-9c992e26><i class="fas fa-phone text-4xl mb-4" data-v-9c992e26></i><h3 class="text-xl font-semibold mb-2" data-v-9c992e26>电话</h3><p data-v-9c992e26>159-8667-2052</p></div><div class="text-center" data-v-9c992e26><i class="fas fa-envelope text-4xl mb-4" data-v-9c992e26></i><h3 class="text-xl font-semibold mb-2" data-v-9c992e26>邮箱</h3><p data-v-9c992e26><EMAIL></p></div></div></div></section>',1))])}const Vu=_(hu,[["render",Uu],["__scopeId","data-v-9c992e26"]]),Qu={name:"MarketAnalysis",components:{Pagination:tt},setup(){const o=m([{id:1,title:"2024年智能一卡通市场分析报告",content:"根据最新市场调研数据，2024年智能一卡通市场规模预计将达到500亿元，同比增长15%。人脸识别、指纹识别等生物识别技术的应用推动了市场快速发展。",date:"2024-01-15",category:"市场分析"},{id:2,title:"人脸识别技术市场趋势分析",content:"人脸识别技术在校园、企业、政府等领域的应用不断扩大，预计2024年市场规模将突破200亿元，技术精度和安全性持续提升。",date:"2024-01-10",category:"市场分析"},{id:3,title:"校园智能化建设市场发展前景",content:"随着教育信息化2.0的推进，校园智能化建设迎来新机遇。智慧校园解决方案市场预计年增长率将达到20%以上。",date:"2024-01-05",category:"市场分析"},{id:4,title:"智能门禁系统市场竞争格局",content:"智能门禁系统市场竞争激烈，技术创新和产品差异化成为企业竞争的关键。云端管理和移动应用成为发展趋势。",date:"2024-01-01",category:"市场分析"},{id:5,title:"消费机行业发展现状与趋势",content:"传统消费机向智能化转型，支付方式多样化，用户体验不断优化。预计智能消费机市场将保持稳定增长。",date:"2023-12-28",category:"市场分析"}]),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedItems:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j}=et(o,10),C=y=>{console.log("打开市场分析新闻详情:",y),alert(`点击了市场分析新闻: ${y}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedNews:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j,openNewsDetail:C}}},Xu={class:"py-12 bg-gray-50"},Ju={class:"container mx-auto px-4"},Zu={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},tp={class:"pt-8 pb-20 bg-white"},ep={class:"container mx-auto px-4"},ap={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},sp={class:"lg:col-span-2"},op={class:"grid gap-8"},lp={class:"space-y-6"},dp={key:0},rp=["onClick"],ip=["alt"],np={class:"p-4 flex-1"},cp={class:"flex items-center mb-2"},up={class:"bg-cyan-100 text-cyan-600 px-2 py-1 rounded text-xs"},pp={class:"text-gray-500 text-xs ml-2"},gp={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-cyan-600 transition duration-300 cursor-pointer"},xp={class:"text-gray-600 text-sm"},fp={key:1,class:"text-center py-16"},vp={class:"max-w-md mx-auto"},mp={class:"text-gray-500 mb-4"},bp={class:"font-medium text-cyan-600"},hp={class:"lg:col-span-1"},yp={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},wp={class:"relative"},_p={key:0,class:"bg-gradient-to-br from-cyan-50 to-blue-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},$p={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},kp={class:"space-y-3"};function Cp(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-cyan-600 to-cyan-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-44e5755d><div class="absolute inset-0 bg-black opacity-10" data-v-44e5755d></div><div class="container mx-auto px-4 relative z-10" data-v-44e5755d><div class="text-center" data-v-44e5755d><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-44e5755d>市场分析</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-44e5755d>深度解析行业市场趋势与发展机遇</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-44e5755d></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-44e5755d></div></section>',1)),e("section",Xu,[e("div",Ju,[e("div",Zu,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-cyan-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-cyan-600 text-white"}," 市场分析 ",-1)),n(s,{to:"/news/industry/policy-interpretation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-cyan-600"},{default:c(()=>t[5]||(t[5]=[i(" 政策解读 ")])),_:1,__:[5]}),n(s,{to:"/news/industry/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-cyan-600"},{default:c(()=>t[6]||(t[6]=[i(" 技术创新 ")])),_:1,__:[6]}),n(s,{to:"/news/industry/international-cooperation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-cyan-600"},{default:c(()=>t[7]||(t[7]=[i(" 国际合作 ")])),_:1,__:[7]}),n(s,{to:"/news/industry/industry-report",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-cyan-600"},{default:c(()=>t[8]||(t[8]=[i(" 行业报告 ")])),_:1,__:[8]})])])]),e("section",tp,[e("div",ep,[e("div",ap,[e("div",sp,[e("div",op,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(d=>a.openNewsDetail("market-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-44e5755d><img src="https://placehold.co/800x400" alt="2024年智能一卡通市场分析报告" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-44e5755d><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-44e5755d> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-44e5755d><div class="flex items-center mb-2" data-v-44e5755d><span class="bg-cyan-100 text-cyan-600 px-3 py-1 rounded text-sm" data-v-44e5755d>市场分析</span><span class="text-white text-sm ml-3" data-v-44e5755d>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-cyan-200 transition-colors duration-300" data-v-44e5755d>2024年智能一卡通市场分析报告</h2><p class="text-gray-200" data-v-44e5755d>根据最新市场调研数据，2024年智能一卡通市场规模预计将达到500亿元，同比增长15%...</p></div></div>',1)]))):h("",!0),e("div",lp,[a.paginatedNews.length>0?(u(),p("div",dp,[(u(!0),p(R,null,W(a.paginatedNews,d=>(u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,ip),e("div",np,[e("div",cp,[e("span",up,v(d.category),1),e("span",pp,v(d.date),1)]),e("h4",gp,v(d.title),1),e("p",xp,v(d.content),1)])],8,rp))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",fp,[e("div",vp,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",mp,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",bp,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-cyan-600 text-white px-4 py-2 rounded-lg hover:bg-cyan-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",hp,[e("div",yp,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-cyan-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",wp,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索市场分析资讯...","onUpdate:modelValue":t[2]||(t[2]=d=>a.searchKeyword=d),onKeyup:t[3]||(t[3]=H(d=>a.handleSearch(a.searchKeyword),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-cyan-50 border border-cyan-200 rounded-full focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-400 focus:bg-white transition-all duration-200 placeholder-cyan-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",_p,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-44e5755d><i class="fas fa-fire text-red-500 mr-2" data-v-44e5755d></i> 热门市场分析 </h3><div class="space-y-4" data-v-44e5755d><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-44e5755d><h4 class="text-sm font-semibold text-gray-800 group-hover:text-cyan-600 transition-colors duration-300 mb-2" data-v-44e5755d>智能一卡通市场报告</h4><p class="text-xs text-gray-500" data-v-44e5755d>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-44e5755d><h4 class="text-sm font-semibold text-gray-800 group-hover:text-cyan-600 transition-colors duration-300 mb-2" data-v-44e5755d>人脸识别技术趋势</h4><p class="text-xs text-gray-500" data-v-44e5755d>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-44e5755d><h4 class="text-sm font-semibold text-gray-800 group-hover:text-cyan-600 transition-colors duration-300 mb-2" data-v-44e5755d>校园智能化发展</h4><p class="text-xs text-gray-500" data-v-44e5755d>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",$p,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 行业资讯分类 ")],-1)),e("div",kp,[t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-cyan-100 rounded-lg shadow-sm"},[e("span",{class:"text-cyan-600 font-semibold"},"市场分析"),e("span",{class:"bg-cyan-100 text-cyan-600 px-2 py-1 rounded-full text-xs"},"15")],-1)),n(s,{to:"/news/industry/policy-interpretation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-cyan-600 transition-colors duration-300"},"政策解读",-1),e("span",{class:"bg-green-100 text-green-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[19]}),n(s,{to:"/news/industry/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-cyan-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"18",-1)])),_:1,__:[20]}),n(s,{to:"/news/industry/international-cooperation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-cyan-600 transition-colors duration-300"},"国际合作",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[21]}),n(s,{to:"/news/industry/industry-report",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-cyan-600 transition-colors duration-300"},"行业报告",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"10",-1)])),_:1,__:[22]})])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"cyan",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-cyan-600 to-cyan-800 text-white" data-v-44e5755d><div class="container mx-auto px-4" data-v-44e5755d><div class="text-center mb-12" data-aos="fade-up" data-v-44e5755d><h2 class="text-3xl font-bold mb-4" data-v-44e5755d>联系我们</h2><p class="text-xl" data-v-44e5755d>如需了解更多市场分析信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-44e5755d><div class="text-center" data-v-44e5755d><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-44e5755d></i><h3 class="text-xl font-semibold mb-2" data-v-44e5755d>地址</h3><p data-v-44e5755d>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-44e5755d><i class="fas fa-phone text-4xl mb-4" data-v-44e5755d></i><h3 class="text-xl font-semibold mb-2" data-v-44e5755d>电话</h3><p data-v-44e5755d>159-8667-2052</p></div><div class="text-center" data-v-44e5755d><i class="fas fa-envelope text-4xl mb-4" data-v-44e5755d></i><h3 class="text-xl font-semibold mb-2" data-v-44e5755d>邮箱</h3><p data-v-44e5755d><EMAIL></p></div></div></div></section>',1))])}const Pp=_(Qu,[["render",Cp],["__scopeId","data-v-44e5755d"]]),jp={name:"PolicyInterpretation",components:{Pagination:tt},setup(){const o=m([{id:1,title:"数据安全法实施细则解读",content:"新版数据安全法实施细则对智能设备数据采集、存储、使用提出了更严格的要求，企业需要建立完善的数据安全管理体系。",date:"2024-01-15",category:"政策解读"},{id:2,title:"教育信息化2.0政策导向分析",content:"教育部发布的教育信息化2.0行动计划，明确了智慧校园建设的发展方向，为教育技术企业带来新机遇。",date:"2024-01-10",category:"政策解读"},{id:3,title:"智慧城市建设规划政策要点",content:"国家发改委发布智慧城市建设指导意见，强调数字化转型和智能化升级，为相关产业发展提供政策支撑。",date:"2024-01-05",category:"政策解读"},{id:4,title:"网络安全等级保护制度解读",content:"网络安全等级保护制度2.0版本对智能终端设备提出了新的安全要求，企业需要加强产品安全设计。",date:"2024-01-01",category:"政策解读"},{id:5,title:"个人信息保护法实施影响",content:"个人信息保护法的实施对人脸识别等生物识别技术的应用产生重要影响，企业需要合规处理个人信息。",date:"2023-12-28",category:"政策解读"}]),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedItems:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j}=et(o,10),C=y=>{console.log("打开政策解读新闻详情:",y),alert(`点击了政策解读新闻: ${y}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedNews:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j,openNewsDetail:C}}},Fp={class:"py-12 bg-gray-50"},Ip={class:"container mx-auto px-4"},Np={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},Sp={class:"pt-8 pb-20 bg-white"},Ap={class:"container mx-auto px-4"},Mp={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Kp={class:"lg:col-span-2"},Tp={class:"grid gap-8"},Dp={class:"space-y-6"},zp={key:0},Rp=["onClick"],Wp=["alt"],Ep={class:"p-4 flex-1"},Lp={class:"flex items-center mb-2"},Yp={class:"bg-emerald-100 text-emerald-600 px-2 py-1 rounded text-xs"},Bp={class:"text-gray-500 text-xs ml-2"},qp={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-emerald-600 transition duration-300 cursor-pointer"},Gp={class:"text-gray-600 text-sm"},Hp={key:1,class:"text-center py-16"},Op={class:"max-w-md mx-auto"},Up={class:"text-gray-500 mb-4"},Vp={class:"font-medium text-emerald-600"},Qp={class:"lg:col-span-1"},Xp={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Jp={class:"relative"},Zp={key:0,class:"bg-gradient-to-br from-emerald-50 to-green-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},tg={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},eg={class:"space-y-3"};function ag(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-emerald-600 to-emerald-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-ec839034><div class="absolute inset-0 bg-black opacity-10" data-v-ec839034></div><div class="container mx-auto px-4 relative z-10" data-v-ec839034><div class="text-center" data-v-ec839034><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-ec839034>政策解读</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-ec839034>深入解读行业政策法规与发展导向</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-ec839034></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-ec839034></div></section>',1)),e("section",Fp,[e("div",Ip,[e("div",Np,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-emerald-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/industry/market-analysis",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-emerald-600"},{default:c(()=>t[5]||(t[5]=[i(" 市场分析 ")])),_:1,__:[5]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-emerald-600 text-white"}," 政策解读 ",-1)),n(s,{to:"/news/industry/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-emerald-600"},{default:c(()=>t[6]||(t[6]=[i(" 技术创新 ")])),_:1,__:[6]}),n(s,{to:"/news/industry/international-cooperation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-emerald-600"},{default:c(()=>t[7]||(t[7]=[i(" 国际合作 ")])),_:1,__:[7]}),n(s,{to:"/news/industry/industry-report",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-emerald-600"},{default:c(()=>t[8]||(t[8]=[i(" 行业报告 ")])),_:1,__:[8]})])])]),e("section",Sp,[e("div",Ap,[e("div",Mp,[e("div",Kp,[e("div",Tp,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(d=>a.openNewsDetail("policy-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-ec839034><img src="https://placehold.co/800x400" alt="数据安全法实施细则解读" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-ec839034><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-ec839034> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-ec839034><div class="flex items-center mb-2" data-v-ec839034><span class="bg-emerald-100 text-emerald-600 px-3 py-1 rounded text-sm" data-v-ec839034>政策解读</span><span class="text-white text-sm ml-3" data-v-ec839034>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-emerald-200 transition-colors duration-300" data-v-ec839034>数据安全法实施细则解读</h2><p class="text-gray-200" data-v-ec839034>新版数据安全法实施细则对智能设备数据采集、存储、使用提出了更严格的要求...</p></div></div>',1)]))):h("",!0),e("div",Dp,[a.paginatedNews.length>0?(u(),p("div",zp,[(u(!0),p(R,null,W(a.paginatedNews,d=>(u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,Wp),e("div",Ep,[e("div",Lp,[e("span",Yp,v(d.category),1),e("span",Bp,v(d.date),1)]),e("h4",qp,v(d.title),1),e("p",Gp,v(d.content),1)])],8,Rp))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",Hp,[e("div",Op,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",Up,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",Vp,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",Qp,[e("div",Xp,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-emerald-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",Jp,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-emerald-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索政策解读资讯...","onUpdate:modelValue":t[2]||(t[2]=d=>a.searchKeyword=d),onKeyup:t[3]||(t[3]=H(d=>a.handleSearch(a.searchKeyword),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-emerald-50 border border-emerald-200 rounded-full focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-400 focus:bg-white transition-all duration-200 placeholder-emerald-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",Zp,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-ec839034><i class="fas fa-fire text-red-500 mr-2" data-v-ec839034></i> 热门政策解读 </h3><div class="space-y-4" data-v-ec839034><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-ec839034><h4 class="text-sm font-semibold text-gray-800 group-hover:text-emerald-600 transition-colors duration-300 mb-2" data-v-ec839034>数据安全法实施细则</h4><p class="text-xs text-gray-500" data-v-ec839034>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-ec839034><h4 class="text-sm font-semibold text-gray-800 group-hover:text-emerald-600 transition-colors duration-300 mb-2" data-v-ec839034>教育信息化政策导向</h4><p class="text-xs text-gray-500" data-v-ec839034>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-ec839034><h4 class="text-sm font-semibold text-gray-800 group-hover:text-emerald-600 transition-colors duration-300 mb-2" data-v-ec839034>智慧城市建设规划</h4><p class="text-xs text-gray-500" data-v-ec839034>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",tg,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 行业资讯分类 ")],-1)),e("div",eg,[n(s,{to:"/news/industry/market-analysis",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-emerald-600 transition-colors duration-300"},"市场分析",-1),e("span",{class:"bg-cyan-100 text-cyan-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[19]}),t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-emerald-100 rounded-lg shadow-sm"},[e("span",{class:"text-emerald-600 font-semibold"},"政策解读"),e("span",{class:"bg-emerald-100 text-emerald-600 px-2 py-1 rounded-full text-xs"},"12")],-1)),n(s,{to:"/news/industry/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-emerald-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-purple-100 text-purple-600 px-2 py-1 rounded-full text-xs"},"18",-1)])),_:1,__:[20]}),n(s,{to:"/news/industry/international-cooperation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-emerald-600 transition-colors duration-300"},"国际合作",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[21]}),n(s,{to:"/news/industry/industry-report",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-emerald-600 transition-colors duration-300"},"行业报告",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"10",-1)])),_:1,__:[22]})])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"emerald",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-emerald-600 to-emerald-800 text-white" data-v-ec839034><div class="container mx-auto px-4" data-v-ec839034><div class="text-center mb-12" data-aos="fade-up" data-v-ec839034><h2 class="text-3xl font-bold mb-4" data-v-ec839034>联系我们</h2><p class="text-xl" data-v-ec839034>如需了解更多政策解读信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-ec839034><div class="text-center" data-v-ec839034><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-ec839034></i><h3 class="text-xl font-semibold mb-2" data-v-ec839034>地址</h3><p data-v-ec839034>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-ec839034><i class="fas fa-phone text-4xl mb-4" data-v-ec839034></i><h3 class="text-xl font-semibold mb-2" data-v-ec839034>电话</h3><p data-v-ec839034>159-8667-2052</p></div><div class="text-center" data-v-ec839034><i class="fas fa-envelope text-4xl mb-4" data-v-ec839034></i><h3 class="text-xl font-semibold mb-2" data-v-ec839034>邮箱</h3><p data-v-ec839034><EMAIL></p></div></div></div></section>',1))])}const sg=_(jp,[["render",ag],["__scopeId","data-v-ec839034"]]),og={name:"TechnicalInnovation",components:{Pagination:tt},setup(){const o=m([{id:1,title:"AI驱动的下一代生物识别技术",content:"基于深度学习的多模态生物识别技术取得重大突破，识别精度提升至99.99%，在复杂环境下仍能保持高准确率。",date:"2024-01-15",category:"技术创新"},{id:2,title:"边缘计算在智能终端中的应用",content:"边缘计算技术的应用使智能终端设备能够实现本地数据处理，大幅降低延迟，提升用户体验。",date:"2024-01-10",category:"技术创新"},{id:3,title:"5G技术推动物联网设备升级",content:"5G网络的普及为物联网设备带来新机遇，高速率、低延迟的特性使智能设备互联更加高效。",date:"2024-01-05",category:"技术创新"},{id:4,title:"区块链技术在身份认证中的应用",content:"区块链技术为身份认证提供了新的解决方案，去中心化的特性增强了系统安全性和可信度。",date:"2024-01-01",category:"技术创新"},{id:5,title:"量子加密技术保障数据安全",content:"量子加密技术的发展为数据安全提供了更强的保障，在智能设备数据传输中具有重要应用价值。",date:"2023-12-28",category:"技术创新"}]),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedItems:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j}=et(o,10),C=y=>{console.log("打开技术创新新闻详情:",y),alert(`点击了技术创新新闻: ${y}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedNews:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j,openNewsDetail:C}}},lg={class:"py-12 bg-gray-50"},dg={class:"container mx-auto px-4"},rg={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},ig={class:"pt-8 pb-20 bg-white"},ng={class:"container mx-auto px-4"},cg={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},ug={class:"lg:col-span-2"},pg={class:"grid gap-8"},gg={class:"space-y-6"},xg={key:0},fg=["onClick"],vg=["alt"],mg={class:"p-4 flex-1"},bg={class:"flex items-center mb-2"},hg={class:"bg-violet-100 text-violet-600 px-2 py-1 rounded text-xs"},yg={class:"text-gray-500 text-xs ml-2"},wg={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-violet-600 transition duration-300 cursor-pointer"},_g={class:"text-gray-600 text-sm"},$g={key:1,class:"text-center py-16"},kg={class:"max-w-md mx-auto"},Cg={class:"text-gray-500 mb-4"},Pg={class:"font-medium text-violet-600"},jg={class:"lg:col-span-1"},Fg={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Ig={class:"relative"},Ng={key:0,class:"bg-gradient-to-br from-violet-50 to-purple-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Sg={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},Ag={class:"space-y-3"};function Mg(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-violet-600 to-violet-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-61ba34f2><div class="absolute inset-0 bg-black opacity-10" data-v-61ba34f2></div><div class="container mx-auto px-4 relative z-10" data-v-61ba34f2><div class="text-center" data-v-61ba34f2><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-61ba34f2>技术创新</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-61ba34f2>探索行业前沿技术与创新应用</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-61ba34f2></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-61ba34f2></div></section>',1)),e("section",lg,[e("div",dg,[e("div",rg,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-violet-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/industry/market-analysis",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-violet-600"},{default:c(()=>t[5]||(t[5]=[i(" 市场分析 ")])),_:1,__:[5]}),n(s,{to:"/news/industry/policy-interpretation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-violet-600"},{default:c(()=>t[6]||(t[6]=[i(" 政策解读 ")])),_:1,__:[6]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-violet-600 text-white"}," 技术创新 ",-1)),n(s,{to:"/news/industry/international-cooperation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-violet-600"},{default:c(()=>t[7]||(t[7]=[i(" 国际合作 ")])),_:1,__:[7]}),n(s,{to:"/news/industry/industry-report",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-violet-600"},{default:c(()=>t[8]||(t[8]=[i(" 行业报告 ")])),_:1,__:[8]})])])]),e("section",ig,[e("div",ng,[e("div",cg,[e("div",ug,[e("div",pg,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(d=>a.openNewsDetail("tech-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-61ba34f2><img src="https://placehold.co/800x400" alt="AI驱动的下一代生物识别技术" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-61ba34f2><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-61ba34f2> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-61ba34f2><div class="flex items-center mb-2" data-v-61ba34f2><span class="bg-violet-100 text-violet-600 px-3 py-1 rounded text-sm" data-v-61ba34f2>技术创新</span><span class="text-white text-sm ml-3" data-v-61ba34f2>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-violet-200 transition-colors duration-300" data-v-61ba34f2>AI驱动的下一代生物识别技术</h2><p class="text-gray-200" data-v-61ba34f2>基于深度学习的多模态生物识别技术取得重大突破，识别精度提升至99.99%...</p></div></div>',1)]))):h("",!0),e("div",gg,[a.paginatedNews.length>0?(u(),p("div",xg,[(u(!0),p(R,null,W(a.paginatedNews,d=>(u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,vg),e("div",mg,[e("div",bg,[e("span",hg,v(d.category),1),e("span",yg,v(d.date),1)]),e("h4",wg,v(d.title),1),e("p",_g,v(d.content),1)])],8,fg))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",$g,[e("div",kg,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",Cg,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",Pg,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-violet-600 text-white px-4 py-2 rounded-lg hover:bg-violet-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",jg,[e("div",Fg,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-violet-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",Ig,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-violet-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索技术创新资讯...","onUpdate:modelValue":t[2]||(t[2]=d=>a.searchKeyword=d),onKeyup:t[3]||(t[3]=H(d=>a.handleSearch(a.searchKeyword),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-violet-50 border border-violet-200 rounded-full focus:outline-none focus:ring-2 focus:ring-violet-500 focus:border-violet-400 focus:bg-white transition-all duration-200 placeholder-violet-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",Ng,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-61ba34f2><i class="fas fa-fire text-red-500 mr-2" data-v-61ba34f2></i> 热门技术创新 </h3><div class="space-y-4" data-v-61ba34f2><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-61ba34f2><h4 class="text-sm font-semibold text-gray-800 group-hover:text-violet-600 transition-colors duration-300 mb-2" data-v-61ba34f2>AI生物识别技术</h4><p class="text-xs text-gray-500" data-v-61ba34f2>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-61ba34f2><h4 class="text-sm font-semibold text-gray-800 group-hover:text-violet-600 transition-colors duration-300 mb-2" data-v-61ba34f2>边缘计算应用</h4><p class="text-xs text-gray-500" data-v-61ba34f2>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-61ba34f2><h4 class="text-sm font-semibold text-gray-800 group-hover:text-violet-600 transition-colors duration-300 mb-2" data-v-61ba34f2>5G物联网技术</h4><p class="text-xs text-gray-500" data-v-61ba34f2>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",Sg,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 行业资讯分类 ")],-1)),e("div",Ag,[n(s,{to:"/news/industry/market-analysis",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-violet-600 transition-colors duration-300"},"市场分析",-1),e("span",{class:"bg-cyan-100 text-cyan-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[19]}),n(s,{to:"/news/industry/policy-interpretation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-violet-600 transition-colors duration-300"},"政策解读",-1),e("span",{class:"bg-emerald-100 text-emerald-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[20]}),t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-violet-100 rounded-lg shadow-sm"},[e("span",{class:"text-violet-600 font-semibold"},"技术创新"),e("span",{class:"bg-violet-100 text-violet-600 px-2 py-1 rounded-full text-xs"},"18")],-1)),n(s,{to:"/news/industry/international-cooperation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-violet-600 transition-colors duration-300"},"国际合作",-1),e("span",{class:"bg-orange-100 text-orange-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[21]}),n(s,{to:"/news/industry/industry-report",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-violet-600 transition-colors duration-300"},"行业报告",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"10",-1)])),_:1,__:[22]})])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"violet",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-violet-600 to-violet-800 text-white" data-v-61ba34f2><div class="container mx-auto px-4" data-v-61ba34f2><div class="text-center mb-12" data-aos="fade-up" data-v-61ba34f2><h2 class="text-3xl font-bold mb-4" data-v-61ba34f2>联系我们</h2><p class="text-xl" data-v-61ba34f2>如需了解更多技术创新信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-61ba34f2><div class="text-center" data-v-61ba34f2><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-61ba34f2></i><h3 class="text-xl font-semibold mb-2" data-v-61ba34f2>地址</h3><p data-v-61ba34f2>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-61ba34f2><i class="fas fa-phone text-4xl mb-4" data-v-61ba34f2></i><h3 class="text-xl font-semibold mb-2" data-v-61ba34f2>电话</h3><p data-v-61ba34f2>159-8667-2052</p></div><div class="text-center" data-v-61ba34f2><i class="fas fa-envelope text-4xl mb-4" data-v-61ba34f2></i><h3 class="text-xl font-semibold mb-2" data-v-61ba34f2>邮箱</h3><p data-v-61ba34f2><EMAIL></p></div></div></div></section>',1))])}const Kg=_(og,[["render",Mg],["__scopeId","data-v-61ba34f2"]]),Tg={name:"InternationalCooperation",components:{Pagination:tt},setup(){const o=m([{id:1,title:"中欧智能设备技术合作项目启动",content:"中欧双方在智能识别技术领域达成深度合作协议，共同推进技术创新与应用，促进双方在智能设备领域的技术交流。",date:"2024-01-15",category:"国际合作"},{id:2,title:"亚太地区智能终端产业战略联盟成立",content:"亚太地区多家智能终端企业联合成立产业联盟，共同制定行业标准，推动区域内技术合作与市场拓展。",date:"2024-01-10",category:"国际合作"},{id:3,title:"一带一路科技合作新进展",content:"在一带一路倡议框架下，多国在智能设备技术领域达成合作共识，共同推进技术标准化和产业化发展。",date:"2024-01-05",category:"国际合作"},{id:4,title:"中美企业在AI领域开展技术交流",content:"中美两国企业在人工智能和生物识别技术领域开展深度交流，探讨技术合作与标准制定的可能性。",date:"2024-01-01",category:"国际合作"},{id:5,title:"国际智能设备标准化组织会议召开",content:"国际智能设备标准化组织年度会议在日内瓦召开，各国代表就行业标准制定和技术规范达成重要共识。",date:"2023-12-28",category:"国际合作"}]),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedItems:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j}=et(o,10),C=y=>{console.log("打开国际合作新闻详情:",y),alert(`点击了国际合作新闻: ${y}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedNews:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j,openNewsDetail:C}}},Dg={class:"py-12 bg-gray-50"},zg={class:"container mx-auto px-4"},Rg={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},Wg={class:"pt-8 pb-20 bg-white"},Eg={class:"container mx-auto px-4"},Lg={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},Yg={class:"lg:col-span-2"},Bg={class:"grid gap-8"},qg={class:"space-y-6"},Gg={key:0},Hg=["onClick"],Og=["alt"],Ug={class:"p-4 flex-1"},Vg={class:"flex items-center mb-2"},Qg={class:"bg-amber-100 text-amber-600 px-2 py-1 rounded text-xs"},Xg={class:"text-gray-500 text-xs ml-2"},Jg={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-amber-600 transition duration-300 cursor-pointer"},Zg={class:"text-gray-600 text-sm"},tx={key:1,class:"text-center py-16"},ex={class:"max-w-md mx-auto"},ax={class:"text-gray-500 mb-4"},sx={class:"font-medium text-amber-600"},ox={class:"lg:col-span-1"},lx={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},dx={class:"relative"},rx={key:0,class:"bg-gradient-to-br from-amber-50 to-yellow-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},ix={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},nx={class:"space-y-3"};function cx(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-amber-600 to-amber-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-2db78941><div class="absolute inset-0 bg-black opacity-10" data-v-2db78941></div><div class="container mx-auto px-4 relative z-10" data-v-2db78941><div class="text-center" data-v-2db78941><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-2db78941>国际合作</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-2db78941>关注全球合作动态与国际交流</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-2db78941></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-2db78941></div></section>',1)),e("section",Dg,[e("div",zg,[e("div",Rg,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-amber-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/industry/market-analysis",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-amber-600"},{default:c(()=>t[5]||(t[5]=[i(" 市场分析 ")])),_:1,__:[5]}),n(s,{to:"/news/industry/policy-interpretation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-amber-600"},{default:c(()=>t[6]||(t[6]=[i(" 政策解读 ")])),_:1,__:[6]}),n(s,{to:"/news/industry/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-amber-600"},{default:c(()=>t[7]||(t[7]=[i(" 技术创新 ")])),_:1,__:[7]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-amber-600 text-white"}," 国际合作 ",-1)),n(s,{to:"/news/industry/industry-report",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-amber-600"},{default:c(()=>t[8]||(t[8]=[i(" 行业报告 ")])),_:1,__:[8]})])])]),e("section",Wg,[e("div",Eg,[e("div",Lg,[e("div",Yg,[e("div",Bg,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(d=>a.openNewsDetail("cooperation-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-2db78941><img src="https://placehold.co/800x400" alt="中欧智能设备技术合作项目启动" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-2db78941><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-2db78941> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-2db78941><div class="flex items-center mb-2" data-v-2db78941><span class="bg-amber-100 text-amber-600 px-3 py-1 rounded text-sm" data-v-2db78941>国际合作</span><span class="text-white text-sm ml-3" data-v-2db78941>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-amber-200 transition-colors duration-300" data-v-2db78941>中欧智能设备技术合作项目启动</h2><p class="text-gray-200" data-v-2db78941>中欧双方在智能识别技术领域达成深度合作协议，共同推进技术创新与应用...</p></div></div>',1)]))):h("",!0),e("div",qg,[a.paginatedNews.length>0?(u(),p("div",Gg,[(u(!0),p(R,null,W(a.paginatedNews,d=>(u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,Og),e("div",Ug,[e("div",Vg,[e("span",Qg,v(d.category),1),e("span",Xg,v(d.date),1)]),e("h4",Jg,v(d.title),1),e("p",Zg,v(d.content),1)])],8,Hg))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",tx,[e("div",ex,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",ax,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",sx,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",ox,[e("div",lx,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-amber-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",dx,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索国际合作资讯...","onUpdate:modelValue":t[2]||(t[2]=d=>a.searchKeyword=d),onKeyup:t[3]||(t[3]=H(d=>a.handleSearch(a.searchKeyword),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-amber-50 border border-amber-200 rounded-full focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-400 focus:bg-white transition-all duration-200 placeholder-amber-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",rx,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-2db78941><i class="fas fa-fire text-red-500 mr-2" data-v-2db78941></i> 热门国际合作 </h3><div class="space-y-4" data-v-2db78941><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-2db78941><h4 class="text-sm font-semibold text-gray-800 group-hover:text-amber-600 transition-colors duration-300 mb-2" data-v-2db78941>中欧技术合作项目</h4><p class="text-xs text-gray-500" data-v-2db78941>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-2db78941><h4 class="text-sm font-semibold text-gray-800 group-hover:text-amber-600 transition-colors duration-300 mb-2" data-v-2db78941>亚太地区战略联盟</h4><p class="text-xs text-gray-500" data-v-2db78941>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-2db78941><h4 class="text-sm font-semibold text-gray-800 group-hover:text-amber-600 transition-colors duration-300 mb-2" data-v-2db78941>一带一路科技合作</h4><p class="text-xs text-gray-500" data-v-2db78941>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",ix,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 行业资讯分类 ")],-1)),e("div",nx,[n(s,{to:"/news/industry/market-analysis",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-amber-600 transition-colors duration-300"},"市场分析",-1),e("span",{class:"bg-cyan-100 text-cyan-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[19]}),n(s,{to:"/news/industry/policy-interpretation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-amber-600 transition-colors duration-300"},"政策解读",-1),e("span",{class:"bg-emerald-100 text-emerald-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[20]}),n(s,{to:"/news/industry/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-amber-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-violet-100 text-violet-600 px-2 py-1 rounded-full text-xs"},"18",-1)])),_:1,__:[21]}),t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-amber-100 rounded-lg shadow-sm"},[e("span",{class:"text-amber-600 font-semibold"},"国际合作"),e("span",{class:"bg-amber-100 text-amber-600 px-2 py-1 rounded-full text-xs"},"8")],-1)),n(s,{to:"/news/industry/industry-report",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-amber-600 transition-colors duration-300"},"行业报告",-1),e("span",{class:"bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs"},"10",-1)])),_:1,__:[22]})])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"amber",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-amber-600 to-amber-800 text-white" data-v-2db78941><div class="container mx-auto px-4" data-v-2db78941><div class="text-center mb-12" data-aos="fade-up" data-v-2db78941><h2 class="text-3xl font-bold mb-4" data-v-2db78941>联系我们</h2><p class="text-xl" data-v-2db78941>如需了解更多国际合作信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-2db78941><div class="text-center" data-v-2db78941><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-2db78941></i><h3 class="text-xl font-semibold mb-2" data-v-2db78941>地址</h3><p data-v-2db78941>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-2db78941><i class="fas fa-phone text-4xl mb-4" data-v-2db78941></i><h3 class="text-xl font-semibold mb-2" data-v-2db78941>电话</h3><p data-v-2db78941>159-8667-2052</p></div><div class="text-center" data-v-2db78941><i class="fas fa-envelope text-4xl mb-4" data-v-2db78941></i><h3 class="text-xl font-semibold mb-2" data-v-2db78941>邮箱</h3><p data-v-2db78941><EMAIL></p></div></div></div></section>',1))])}const ux=_(Tg,[["render",cx],["__scopeId","data-v-2db78941"]]),px={name:"IndustryReport",components:{Pagination:tt},setup(){const o=m([{id:1,title:"2024年中国智能设备行业发展报告",content:"权威机构发布2024年中国智能设备行业发展报告，深度分析市场现状与未来趋势，预测行业将保持20%以上的年增长率。",date:"2024-01-15",category:"行业报告"},{id:2,title:"生物识别技术应用白皮书",content:"行业协会发布生物识别技术应用白皮书，详细阐述了人脸识别、指纹识别等技术的应用现状和发展前景。",date:"2024-01-10",category:"行业报告"},{id:3,title:"智慧校园建设指导报告",content:"教育部门发布智慧校园建设指导报告，为各级学校智能化改造提供了详细的技术规范和实施指南。",date:"2024-01-05",category:"行业报告"},{id:4,title:"物联网安全技术研究报告",content:"网络安全机构发布物联网安全技术研究报告，分析了当前物联网设备面临的安全挑战和解决方案。",date:"2024-01-01",category:"行业报告"},{id:5,title:"智能终端市场竞争格局分析",content:"市场研究机构发布智能终端市场竞争格局分析报告，深入分析了主要厂商的市场份额和竞争策略。",date:"2023-12-28",category:"行业报告"}]),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedItems:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j}=et(o,10),C=y=>{console.log("打开行业报告新闻详情:",y),alert(`点击了行业报告新闻: ${y}`)};return F(()=>{N.init({duration:1e3,once:!0})}),{currentPage:t,itemsPerPage:r,searchKeyword:a,paginatedNews:g,totalItems:x,totalPages:s,handlePageChange:l,handleItemsPerPageChange:d,handleSearch:w,clearSearch:j,openNewsDetail:C}}},gx={class:"py-12 bg-gray-50"},xx={class:"container mx-auto px-4"},fx={class:"flex flex-wrap justify-center gap-4","data-aos":"fade-right"},vx={class:"pt-8 pb-20 bg-white"},mx={class:"container mx-auto px-4"},bx={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},hx={class:"lg:col-span-2"},yx={class:"grid gap-8"},wx={class:"space-y-6"},_x={key:0},$x=["onClick"],kx=["alt"],Cx={class:"p-4 flex-1"},Px={class:"flex items-center mb-2"},jx={class:"bg-rose-100 text-rose-600 px-2 py-1 rounded text-xs"},Fx={class:"text-gray-500 text-xs ml-2"},Ix={class:"text-lg font-bold text-gray-800 mb-2 group-hover:text-rose-600 transition duration-300 cursor-pointer"},Nx={class:"text-gray-600 text-sm"},Sx={key:1,class:"text-center py-16"},Ax={class:"max-w-md mx-auto"},Mx={class:"text-gray-500 mb-4"},Kx={class:"font-medium text-rose-600"},Tx={class:"lg:col-span-1"},Dx={class:"bg-white rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},zx={class:"relative"},Rx={key:0,class:"bg-gradient-to-br from-rose-50 to-pink-50 rounded-xl p-6 shadow-lg mb-8","data-aos":"fade-up"},Wx={class:"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-lg","data-aos":"fade-up","data-aos-delay":"200"},Ex={class:"space-y-3"};function Lx(o,t,r,a,g,x){const s=$("router-link"),l=$("Pagination");return u(),p("div",null,[t[25]||(t[25]=f('<section class="bg-gradient-to-r from-rose-600 to-rose-800 text-white pt-24 pb-8 relative overflow-hidden" data-v-951fc20c><div class="absolute inset-0 bg-black opacity-10" data-v-951fc20c></div><div class="container mx-auto px-4 relative z-10" data-v-951fc20c><div class="text-center" data-v-951fc20c><h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up" data-v-951fc20c>行业报告</h1><p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200" data-v-951fc20c>权威发布行业深度研究报告</p></div></div><div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce" data-v-951fc20c></div><div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay:1s;" data-v-951fc20c></div></section>',1)),e("section",gx,[e("div",xx,[e("div",fx,[n(s,{to:"/news",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-rose-600"},{default:c(()=>t[4]||(t[4]=[i(" 全部新闻 ")])),_:1,__:[4]}),n(s,{to:"/news/industry/market-analysis",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-rose-600"},{default:c(()=>t[5]||(t[5]=[i(" 市场分析 ")])),_:1,__:[5]}),n(s,{to:"/news/industry/policy-interpretation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-rose-600"},{default:c(()=>t[6]||(t[6]=[i(" 政策解读 ")])),_:1,__:[6]}),n(s,{to:"/news/industry/technical-innovation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-rose-600"},{default:c(()=>t[7]||(t[7]=[i(" 技术创新 ")])),_:1,__:[7]}),n(s,{to:"/news/industry/international-cooperation",class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-white text-gray-600 hover:text-rose-600"},{default:c(()=>t[8]||(t[8]=[i(" 国际合作 ")])),_:1,__:[8]}),t[9]||(t[9]=e("button",{class:"category-tag px-6 py-3 rounded-full shadow-md font-semibold transition-all duration-300 bg-rose-600 text-white"}," 行业报告 ",-1))])])]),e("section",vx,[e("div",mx,[e("div",bx,[e("div",hx,[e("div",yx,[a.searchKeyword.trim()===""?(u(),p("a",{key:0,href:"#",onClick:t[0]||(t[0]=T(d=>a.openNewsDetail("report-featured-1"),["prevent"])),class:"featured-news bg-white rounded-xl overflow-hidden shadow-lg border border-gray-100 group hover:shadow-2xl transition-all duration-500 block cursor-pointer","data-aos":"fade-up"},t[10]||(t[10]=[f('<div class="relative" data-v-951fc20c><img src="https://placehold.co/800x400" alt="2024年中国智能设备行业发展报告" class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-500" data-v-951fc20c><div class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full text-sm font-bold" data-v-951fc20c> 头条新闻 </div><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6" data-v-951fc20c><div class="flex items-center mb-2" data-v-951fc20c><span class="bg-rose-100 text-rose-600 px-3 py-1 rounded text-sm" data-v-951fc20c>行业报告</span><span class="text-white text-sm ml-3" data-v-951fc20c>2024-01-15</span></div><h2 class="text-2xl font-bold text-white mb-2 group-hover:text-rose-200 transition-colors duration-300" data-v-951fc20c>2024年中国智能设备行业发展报告</h2><p class="text-gray-200" data-v-951fc20c>权威机构发布2024年中国智能设备行业发展报告，深度分析市场现状与未来趋势...</p></div></div>',1)]))):h("",!0),e("div",wx,[a.paginatedNews.length>0?(u(),p("div",_x,[(u(!0),p(R,null,W(a.paginatedNews,d=>(u(),p("a",{href:"#",onClick:T(w=>a.openNewsDetail(d.id),["prevent"]),key:d.id,class:"news-card bg-white rounded-lg overflow-hidden border border-gray-100 flex group hover:shadow-lg transition-all duration-300 block","data-aos":"fade-up"},[e("img",{src:"https://placehold.co/150x100",alt:d.title,class:"news-image w-32 h-24 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,kx),e("div",Cx,[e("div",Px,[e("span",jx,v(d.category),1),e("span",Fx,v(d.date),1)]),e("h4",Ix,v(d.title),1),e("p",Nx,v(d.content),1)])],8,$x))),128))])):a.searchKeyword.trim()!==""?(u(),p("div",Sx,[e("div",Ax,[t[14]||(t[14]=e("i",{class:"fas fa-search text-6xl text-gray-300 mb-4"},null,-1)),t[15]||(t[15]=e("h3",{class:"text-xl font-semibold text-gray-600 mb-2"},"未找到相关新闻",-1)),e("p",Mx,[t[11]||(t[11]=i(' 没有找到包含 "')),e("span",Kx,v(a.searchKeyword),1),t[12]||(t[12]=i('" 的新闻内容 '))]),e("button",{onClick:t[1]||(t[1]=(...d)=>a.clearSearch&&a.clearSearch(...d)),class:"bg-rose-600 text-white px-4 py-2 rounded-lg hover:bg-rose-700 transition duration-300"},t[13]||(t[13]=[e("i",{class:"fas fa-times mr-2"},null,-1),i("清除搜索 ")]))])])):h("",!0)])])]),e("div",Tx,[e("div",Dx,[t[17]||(t[17]=e("h3",{class:"text-xl font-bold text-gray-800 mb-4 flex items-center"},[e("i",{class:"fas fa-search text-rose-500 mr-2"}),i(" 搜索资讯 ")],-1)),e("div",zx,[t[16]||(t[16]=e("i",{class:"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-rose-400"},null,-1)),D(e("input",{type:"text",placeholder:"搜索行业报告...","onUpdate:modelValue":t[2]||(t[2]=d=>a.searchKeyword=d),onKeyup:t[3]||(t[3]=H(d=>a.handleSearch(a.searchKeyword),["enter"])),class:"pl-10 pr-4 py-3 w-full bg-rose-50 border border-rose-200 rounded-full focus:outline-none focus:ring-2 focus:ring-rose-500 focus:border-rose-400 focus:bg-white transition-all duration-200 placeholder-rose-400"},null,544),[[G,a.searchKeyword]])])]),a.searchKeyword.trim()===""?(u(),p("div",Rx,t[18]||(t[18]=[f('<h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center" data-v-951fc20c><i class="fas fa-fire text-red-500 mr-2" data-v-951fc20c></i> 热门行业报告 </h3><div class="space-y-4" data-v-951fc20c><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-951fc20c><h4 class="text-sm font-semibold text-gray-800 group-hover:text-rose-600 transition-colors duration-300 mb-2" data-v-951fc20c>智能设备行业发展报告</h4><p class="text-xs text-gray-500" data-v-951fc20c>2024-01-15</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-951fc20c><h4 class="text-sm font-semibold text-gray-800 group-hover:text-rose-600 transition-colors duration-300 mb-2" data-v-951fc20c>生物识别技术白皮书</h4><p class="text-xs text-gray-500" data-v-951fc20c>2024-01-10</p></a><a href="#" class="block p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group" data-v-951fc20c><h4 class="text-sm font-semibold text-gray-800 group-hover:text-rose-600 transition-colors duration-300 mb-2" data-v-951fc20c>智慧校园建设指南</h4><p class="text-xs text-gray-500" data-v-951fc20c>2024-01-05</p></a></div>',2)]))):h("",!0),e("div",Wx,[t[24]||(t[24]=e("h3",{class:"text-xl font-bold text-gray-800 mb-6 flex items-center"},[e("i",{class:"fas fa-list text-blue-500 mr-2"}),i(" 行业资讯分类 ")],-1)),e("div",Ex,[n(s,{to:"/news/industry/market-analysis",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[19]||(t[19]=[e("span",{class:"text-gray-700 group-hover:text-rose-600 transition-colors duration-300"},"市场分析",-1),e("span",{class:"bg-cyan-100 text-cyan-600 px-2 py-1 rounded-full text-xs"},"15",-1)])),_:1,__:[19]}),n(s,{to:"/news/industry/policy-interpretation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[20]||(t[20]=[e("span",{class:"text-gray-700 group-hover:text-rose-600 transition-colors duration-300"},"政策解读",-1),e("span",{class:"bg-emerald-100 text-emerald-600 px-2 py-1 rounded-full text-xs"},"12",-1)])),_:1,__:[20]}),n(s,{to:"/news/industry/technical-innovation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[21]||(t[21]=[e("span",{class:"text-gray-700 group-hover:text-rose-600 transition-colors duration-300"},"技术创新",-1),e("span",{class:"bg-violet-100 text-violet-600 px-2 py-1 rounded-full text-xs"},"18",-1)])),_:1,__:[21]}),n(s,{to:"/news/industry/international-cooperation",class:"flex items-center justify-between p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group"},{default:c(()=>t[22]||(t[22]=[e("span",{class:"text-gray-700 group-hover:text-rose-600 transition-colors duration-300"},"国际合作",-1),e("span",{class:"bg-amber-100 text-amber-600 px-2 py-1 rounded-full text-xs"},"8",-1)])),_:1,__:[22]}),t[23]||(t[23]=e("a",{href:"#",class:"flex items-center justify-between p-3 bg-rose-100 rounded-lg shadow-sm"},[e("span",{class:"text-rose-600 font-semibold"},"行业报告"),e("span",{class:"bg-rose-100 text-rose-600 px-2 py-1 rounded-full text-xs"},"10")],-1))])])])])])]),a.totalItems>0?(u(),Q(l,{key:0,"current-page":a.currentPage,"total-items":a.totalItems,"items-per-page":a.itemsPerPage,"theme-color":"rose",onPageChange:a.handlePageChange,onItemsPerPageChange:a.handleItemsPerPageChange},null,8,["current-page","total-items","items-per-page","onPageChange","onItemsPerPageChange"])):h("",!0),t[26]||(t[26]=f('<section class="py-16 bg-gradient-to-r from-rose-600 to-rose-800 text-white" data-v-951fc20c><div class="container mx-auto px-4" data-v-951fc20c><div class="text-center mb-12" data-aos="fade-up" data-v-951fc20c><h2 class="text-3xl font-bold mb-4" data-v-951fc20c>联系我们</h2><p class="text-xl" data-v-951fc20c>如需了解更多行业报告信息，请联系我们</p></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200" data-v-951fc20c><div class="text-center" data-v-951fc20c><i class="fas fa-map-marker-alt text-4xl mb-4" data-v-951fc20c></i><h3 class="text-xl font-semibold mb-2" data-v-951fc20c>地址</h3><p data-v-951fc20c>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</p></div><div class="text-center" data-v-951fc20c><i class="fas fa-phone text-4xl mb-4" data-v-951fc20c></i><h3 class="text-xl font-semibold mb-2" data-v-951fc20c>电话</h3><p data-v-951fc20c>159-8667-2052</p></div><div class="text-center" data-v-951fc20c><i class="fas fa-envelope text-4xl mb-4" data-v-951fc20c></i><h3 class="text-xl font-semibold mb-2" data-v-951fc20c>邮箱</h3><p data-v-951fc20c><EMAIL></p></div></div></div></section>',1))])}const Yx=_(px,[["render",Lx],["__scopeId","data-v-951fc20c"]]),Bx={name:"SmartCampus",setup(){F(()=>{N.init({duration:1e3,once:!0})})}},qx={class:"bg-gradient-to-r from-blue-600 to-indigo-800 text-white pt-24 pb-16 relative overflow-hidden"},Gx={class:"container mx-auto px-4 relative z-10"},Hx={class:"text-center"},Ox={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"},Ux={class:"py-20 bg-blue-600 text-white"},Vx={class:"container mx-auto px-4 text-center"},Qx={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"};function Xx(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[e("section",qx,[t[3]||(t[3]=e("div",{class:"absolute inset-0 bg-black opacity-10"},null,-1)),e("div",Gx,[e("div",Hx,[t[1]||(t[1]=e("h1",{class:"text-4xl md:text-6xl font-bold mb-4","data-aos":"fade-up"},"智慧校园解决方案",-1)),t[2]||(t[2]=e("p",{class:"text-xl md:text-2xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"构建数字化、智能化的现代校园生态",-1)),e("div",Ox,[n(s,{to:"/contact",class:"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[0]||(t[0]=[i(" 立即咨询 ")])),_:1,__:[0]})])])]),t[4]||(t[4]=e("div",{class:"absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"},null,-1)),t[5]||(t[5]=e("div",{class:"absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce",style:{"animation-delay":"1s"}},null,-1))]),t[9]||(t[9]=f('<section class="py-20 bg-white" data-v-c5415944><div class="container mx-auto px-4" data-v-c5415944><div class="h-16" data-v-c5415944></div><div class="h-32" data-v-c5415944></div></div></section><section class="py-20 bg-gray-50" data-v-c5415944><div class="container mx-auto px-4" data-v-c5415944><div class="h-32" data-v-c5415944></div></div></section><section class="py-20 bg-white" data-v-c5415944><div class="container mx-auto px-4" data-v-c5415944><div class="h-32" data-v-c5415944></div></div></section>',3)),e("section",Ux,[e("div",Vx,[t[7]||(t[7]=e("h2",{class:"text-3xl font-bold mb-4","data-aos":"fade-up"},"开启您的智慧校园之旅",-1)),t[8]||(t[8]=e("p",{class:"text-xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"专业团队为您提供定制化解决方案",-1)),e("div",Qx,[n(s,{to:"/contact",class:"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[6]||(t[6]=[e("i",{class:"fas fa-phone mr-2"},null,-1),i("立即咨询 ")])),_:1,__:[6]})])])])])}const Jx=_(Bx,[["render",Xx],["__scopeId","data-v-c5415944"]]),Zx={name:"EnterprisePark",setup(){F(()=>{N.init({duration:1e3,once:!0})})}},tf={class:"bg-gradient-to-r from-green-600 to-teal-800 text-white pt-24 pb-16 relative overflow-hidden"},ef={class:"container mx-auto px-4 relative z-10"},af={class:"text-center"},sf={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"},of={class:"py-20 bg-green-600 text-white"},lf={class:"container mx-auto px-4 text-center"},df={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"};function rf(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[e("section",tf,[t[3]||(t[3]=e("div",{class:"absolute inset-0 bg-black opacity-10"},null,-1)),e("div",ef,[e("div",af,[t[1]||(t[1]=e("h1",{class:"text-4xl md:text-6xl font-bold mb-4","data-aos":"fade-up"},"企业园区解决方案",-1)),t[2]||(t[2]=e("p",{class:"text-xl md:text-2xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"打造智能化、数字化的现代企业园区",-1)),e("div",sf,[n(s,{to:"/contact",class:"bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[0]||(t[0]=[i(" 立即咨询 ")])),_:1,__:[0]})])])]),t[4]||(t[4]=e("div",{class:"absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"},null,-1)),t[5]||(t[5]=e("div",{class:"absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce",style:{"animation-delay":"1s"}},null,-1))]),t[9]||(t[9]=f('<section class="py-20 bg-white" data-v-1c249547><div class="container mx-auto px-4" data-v-1c249547><div class="h-16" data-v-1c249547></div><div class="h-32" data-v-1c249547></div></div></section><section class="py-20 bg-gray-50" data-v-1c249547><div class="container mx-auto px-4" data-v-1c249547><div class="h-32" data-v-1c249547></div></div></section><section class="py-20 bg-white" data-v-1c249547><div class="container mx-auto px-4" data-v-1c249547><div class="h-32" data-v-1c249547></div></div></section>',3)),e("section",of,[e("div",lf,[t[7]||(t[7]=e("h2",{class:"text-3xl font-bold mb-4","data-aos":"fade-up"},"构建您的智慧园区",-1)),t[8]||(t[8]=e("p",{class:"text-xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"专业团队为您提供定制化解决方案",-1)),e("div",df,[n(s,{to:"/contact",class:"bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[6]||(t[6]=[e("i",{class:"fas fa-phone mr-2"},null,-1),i("立即咨询 ")])),_:1,__:[6]})])])])])}const nf=_(Zx,[["render",rf],["__scopeId","data-v-1c249547"]]),cf={name:"MedicalInstitution",setup(){F(()=>{N.init({duration:1e3,once:!0})})}},uf={class:"bg-gradient-to-r from-red-600 to-pink-800 text-white pt-24 pb-16 relative overflow-hidden"},pf={class:"container mx-auto px-4 relative z-10"},gf={class:"text-center"},xf={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"},ff={class:"py-20 bg-red-600 text-white"},vf={class:"container mx-auto px-4 text-center"},mf={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"};function bf(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[e("section",uf,[t[3]||(t[3]=e("div",{class:"absolute inset-0 bg-black opacity-10"},null,-1)),e("div",pf,[e("div",gf,[t[1]||(t[1]=e("h1",{class:"text-4xl md:text-6xl font-bold mb-4","data-aos":"fade-up"},"医疗机构解决方案",-1)),t[2]||(t[2]=e("p",{class:"text-xl md:text-2xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"构建安全、高效的智慧医疗环境",-1)),e("div",xf,[n(s,{to:"/contact",class:"bg-white text-red-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[0]||(t[0]=[i(" 立即咨询 ")])),_:1,__:[0]})])])]),t[4]||(t[4]=e("div",{class:"absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"},null,-1)),t[5]||(t[5]=e("div",{class:"absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce",style:{"animation-delay":"1s"}},null,-1))]),t[9]||(t[9]=f('<section class="py-20 bg-white" data-v-17cea7f7><div class="container mx-auto px-4" data-v-17cea7f7><div class="h-16" data-v-17cea7f7></div><div class="h-32" data-v-17cea7f7></div></div></section><section class="py-20 bg-gray-50" data-v-17cea7f7><div class="container mx-auto px-4" data-v-17cea7f7><div class="h-32" data-v-17cea7f7></div></div></section><section class="py-20 bg-white" data-v-17cea7f7><div class="container mx-auto px-4" data-v-17cea7f7><div class="h-32" data-v-17cea7f7></div></div></section>',3)),e("section",ff,[e("div",vf,[t[7]||(t[7]=e("h2",{class:"text-3xl font-bold mb-4","data-aos":"fade-up"},"构建您的智慧医疗环境",-1)),t[8]||(t[8]=e("p",{class:"text-xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"专业团队为您提供定制化解决方案",-1)),e("div",mf,[n(s,{to:"/contact",class:"bg-white text-red-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[6]||(t[6]=[e("i",{class:"fas fa-phone mr-2"},null,-1),i("立即咨询 ")])),_:1,__:[6]})])])])])}const hf=_(cf,[["render",bf],["__scopeId","data-v-17cea7f7"]]),yf={name:"GovernmentAgency",setup(){F(()=>{N.init({duration:1e3,once:!0})})}},wf={class:"bg-gradient-to-r from-purple-600 to-indigo-800 text-white pt-24 pb-16 relative overflow-hidden"},_f={class:"container mx-auto px-4 relative z-10"},$f={class:"text-center"},kf={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"},Cf={class:"py-20 bg-purple-600 text-white"},Pf={class:"container mx-auto px-4 text-center"},jf={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"};function Ff(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[e("section",wf,[t[3]||(t[3]=e("div",{class:"absolute inset-0 bg-black opacity-10"},null,-1)),e("div",_f,[e("div",$f,[t[1]||(t[1]=e("h1",{class:"text-4xl md:text-6xl font-bold mb-4","data-aos":"fade-up"},"政府机关解决方案",-1)),t[2]||(t[2]=e("p",{class:"text-xl md:text-2xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"打造安全、高效的数字政务环境",-1)),e("div",kf,[n(s,{to:"/contact",class:"bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[0]||(t[0]=[i(" 立即咨询 ")])),_:1,__:[0]})])])]),t[4]||(t[4]=e("div",{class:"absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"},null,-1)),t[5]||(t[5]=e("div",{class:"absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce",style:{"animation-delay":"1s"}},null,-1))]),t[9]||(t[9]=f('<section class="py-20 bg-white" data-v-d6dac884><div class="container mx-auto px-4" data-v-d6dac884><div class="h-16" data-v-d6dac884></div><div class="h-32" data-v-d6dac884></div></div></section><section class="py-20 bg-gray-50" data-v-d6dac884><div class="container mx-auto px-4" data-v-d6dac884><div class="h-32" data-v-d6dac884></div></div></section><section class="py-20 bg-white" data-v-d6dac884><div class="container mx-auto px-4" data-v-d6dac884><div class="h-32" data-v-d6dac884></div></div></section>',3)),e("section",Cf,[e("div",Pf,[t[7]||(t[7]=e("h2",{class:"text-3xl font-bold mb-4","data-aos":"fade-up"},"构建您的数字政务环境",-1)),t[8]||(t[8]=e("p",{class:"text-xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"专业团队为您提供定制化解决方案",-1)),e("div",jf,[n(s,{to:"/contact",class:"bg-white text-purple-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[6]||(t[6]=[e("i",{class:"fas fa-phone mr-2"},null,-1),i("立即咨询 ")])),_:1,__:[6]})])])])])}const If=_(yf,[["render",Ff],["__scopeId","data-v-d6dac884"]]),Nf={name:"FactoryPark",setup(){F(()=>{N.init({duration:1e3,once:!0})})}},Sf={class:"bg-gradient-to-r from-orange-600 to-red-800 text-white pt-24 pb-16 relative overflow-hidden"},Af={class:"container mx-auto px-4 relative z-10"},Mf={class:"text-center"},Kf={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"},Tf={class:"py-20 bg-orange-600 text-white"},Df={class:"container mx-auto px-4 text-center"},zf={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"};function Rf(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[e("section",Sf,[t[3]||(t[3]=e("div",{class:"absolute inset-0 bg-black opacity-10"},null,-1)),e("div",Af,[e("div",Mf,[t[1]||(t[1]=e("h1",{class:"text-4xl md:text-6xl font-bold mb-4","data-aos":"fade-up"},"工厂园区解决方案",-1)),t[2]||(t[2]=e("p",{class:"text-xl md:text-2xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"构建安全、高效的智能制造环境",-1)),e("div",Kf,[n(s,{to:"/contact",class:"bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[0]||(t[0]=[i(" 立即咨询 ")])),_:1,__:[0]})])])]),t[4]||(t[4]=e("div",{class:"absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"},null,-1)),t[5]||(t[5]=e("div",{class:"absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce",style:{"animation-delay":"1s"}},null,-1))]),t[9]||(t[9]=f('<section class="py-20 bg-white" data-v-d8c92b8d><div class="container mx-auto px-4" data-v-d8c92b8d><div class="h-16" data-v-d8c92b8d></div><div class="h-32" data-v-d8c92b8d></div></div></section><section class="py-20 bg-gray-50" data-v-d8c92b8d><div class="container mx-auto px-4" data-v-d8c92b8d><div class="h-32" data-v-d8c92b8d></div></div></section><section class="py-20 bg-white" data-v-d8c92b8d><div class="container mx-auto px-4" data-v-d8c92b8d><div class="h-32" data-v-d8c92b8d></div></div></section>',3)),e("section",Tf,[e("div",Df,[t[7]||(t[7]=e("h2",{class:"text-3xl font-bold mb-4","data-aos":"fade-up"},"构建您的智能工厂",-1)),t[8]||(t[8]=e("p",{class:"text-xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"专业团队为您提供定制化解决方案",-1)),e("div",zf,[n(s,{to:"/contact",class:"bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[6]||(t[6]=[e("i",{class:"fas fa-phone mr-2"},null,-1),i("立即咨询 ")])),_:1,__:[6]})])])])])}const Wf=_(Nf,[["render",Rf],["__scopeId","data-v-d8c92b8d"]]),Ef={name:"SmartHotel",setup(){F(()=>{N.init({duration:1e3,once:!0})})}},Lf={class:"bg-gradient-to-r from-teal-600 to-cyan-800 text-white pt-24 pb-16 relative overflow-hidden"},Yf={class:"container mx-auto px-4 relative z-10"},Bf={class:"text-center"},qf={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"},Gf={class:"py-20 bg-teal-600 text-white"},Hf={class:"container mx-auto px-4 text-center"},Of={class:"flex justify-center","data-aos":"fade-up","data-aos-delay":"400"};function Uf(o,t,r,a,g,x){const s=$("router-link");return u(),p("div",null,[e("section",Lf,[t[3]||(t[3]=e("div",{class:"absolute inset-0 bg-black opacity-10"},null,-1)),e("div",Yf,[e("div",Bf,[t[1]||(t[1]=e("h1",{class:"text-4xl md:text-6xl font-bold mb-4","data-aos":"fade-up"},"智慧酒店解决方案",-1)),t[2]||(t[2]=e("p",{class:"text-xl md:text-2xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"打造智能化、个性化的酒店服务体验",-1)),e("div",qf,[n(s,{to:"/contact",class:"bg-white text-teal-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[0]||(t[0]=[i(" 立即咨询 ")])),_:1,__:[0]})])])]),t[4]||(t[4]=e("div",{class:"absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"},null,-1)),t[5]||(t[5]=e("div",{class:"absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce",style:{"animation-delay":"1s"}},null,-1))]),t[9]||(t[9]=f('<section class="py-20 bg-white" data-v-fcf8d8cf><div class="container mx-auto px-4" data-v-fcf8d8cf><div class="h-16" data-v-fcf8d8cf></div><div class="h-32" data-v-fcf8d8cf></div></div></section><section class="py-20 bg-gray-50" data-v-fcf8d8cf><div class="container mx-auto px-4" data-v-fcf8d8cf><div class="h-32" data-v-fcf8d8cf></div></div></section><section class="py-20 bg-white" data-v-fcf8d8cf><div class="container mx-auto px-4" data-v-fcf8d8cf><div class="h-32" data-v-fcf8d8cf></div></div></section>',3)),e("section",Gf,[e("div",Hf,[t[7]||(t[7]=e("h2",{class:"text-3xl font-bold mb-4","data-aos":"fade-up"},"构建您的智慧酒店",-1)),t[8]||(t[8]=e("p",{class:"text-xl mb-8","data-aos":"fade-up","data-aos-delay":"200"},"专业团队为您提供定制化解决方案",-1)),e("div",Of,[n(s,{to:"/contact",class:"bg-white text-teal-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300 inline-block"},{default:c(()=>t[6]||(t[6]=[e("i",{class:"fas fa-phone mr-2"},null,-1),i("立即咨询 ")])),_:1,__:[6]})])])])])}const Vf=_(Ef,[["render",Uf],["__scopeId","data-v-fcf8d8cf"]]),Qf=[{path:"/",name:"Home",component:ne,meta:{title:"首页 - 深圳市优卡特实业有限公司",description:"智能一卡通设备领导者，提供创新的产品和服务"}},{path:"/about",name:"About",component:Ce,meta:{title:"关于我们 - 深圳市优卡特实业有限公司",description:"了解优卡特的发展历程与企业文化"}},{path:"/about/history",name:"DevelopmentHistory",component:Os,meta:{title:"发展历程 - 深圳市优卡特实业有限公司",description:"回顾优卡特20年来的发展足迹与重要里程碑"}},{path:"/about/team",name:"Team",component:Qs,meta:{title:"企业团队 - 深圳市优卡特实业有限公司",description:"认识优卡特的核心管理团队与技术专家"}},{path:"/about/honors",name:"Honors",component:no,meta:{title:"企业荣誉 - 深圳市优卡特实业有限公司",description:"展示优卡特获得的各项认证、奖项与资质"}},{path:"/about/careers",name:"Careers",component:$o,meta:{title:"招贤纳士 - 深圳市优卡特实业有限公司",description:"加入优卡特，共同创造智能一卡通的美好未来"}},{path:"/products",name:"Products",component:Re,meta:{title:"产品中心 - 深圳市优卡特实业有限公司",description:"智能一卡通设备，引领科技创新"}},{path:"/products/android-face",name:"AndroidFace",component:Vo,meta:{title:"安卓人脸消费机 - 深圳市优卡特实业有限公司",description:"智能人脸识别，安全便捷消费"}},{path:"/products/arm-terminal",name:"ArmTerminal",component:ul,meta:{title:"ARM消费机 - 深圳市优卡特实业有限公司",description:"高性能ARM处理器，稳定可靠"}},{path:"/products/face-temp-gate",name:"FaceTempGate",component:kl,meta:{title:"人脸测温通道闸机 - 深圳市优卡特实业有限公司",description:"人脸识别+体温检测，双重安全保障"}},{path:"/products/water-electric",name:"WaterElectric",component:zl,meta:{title:"水/电控机 - 深圳市优卡特实业有限公司",description:"智能水电管理，节能环保"}},{path:"/products/access-control",name:"AccessControl",component:Ql,meta:{title:"门禁机 - 深圳市优卡特实业有限公司",description:"智能门禁管理，安全可靠"}},{path:"/products/campus-shared",name:"CampusShared",component:nd,meta:{title:"校园共享设备 - 深圳市优卡特实业有限公司",description:"智慧校园，共享便民"}},{path:"/products/face-cloud",name:"FaceCloud",component:_d,meta:{title:"脸爱云平台 - 深圳市优卡特实业有限公司",description:"云端智能管理，数据安全可靠"}},{path:"/products/android-face-detail",name:"AndroidFaceDetail",component:Bd,meta:{title:"安卓人脸消费机详情 - 深圳市优卡特实业有限公司",description:"安卓人脸消费机产品详情，智能识别，安全便捷"}},{path:"/products/arm-terminal-detail",name:"ArmTerminalDetail",component:Hd,meta:{title:"ARM消费机详情 - 深圳市优卡特实业有限公司",description:"ARM消费机产品详情，高性能处理器，稳定可靠"}},{path:"/products/access-control-detail",name:"AccessControlDetail",component:Vd,meta:{title:"门禁控制器详情 - 深圳市优卡特实业有限公司",description:"门禁控制器产品详情，智能门禁管理，安全可靠"}},{path:"/products/face-temp-gate-detail",name:"FaceTempGateDetail",component:Jd,meta:{title:"人脸测温通道闸机详情 - 深圳市优卡特实业有限公司",description:"人脸测温通道闸机产品详情，智能测温，安全防护"}},{path:"/products/android-face/p301-2d-2w",name:"AndroidFaceP301",component:wr,meta:{title:"P301-2D-2W台式人脸消费机详情 - 深圳市优卡特实业有限公司",description:"P301-2D-2W台式人脸消费机产品详情，智能识别，安全便捷"}},{path:"/products/arm-terminal/a201-ic-2w",name:"ArmTerminalA201",component:Ir,meta:{title:"A201-IC-2W ARM消费机详情 - 深圳市优卡特实业有限公司",description:"A201-IC-2W ARM消费机产品详情，高性能处理器，稳定可靠"}},{path:"/products/face-cloud/basic",name:"FaceCloudBasic",component:zr,meta:{title:"脸爱云平台基础版详情 - 深圳市优卡特实业有限公司",description:"脸爱云平台基础版产品详情，基础设备管理云平台"}},{path:"/products/face-temp-gate/tg100-stand",name:"FaceTempGateTG100",component:Gr,meta:{title:"YKT-TG100立式测温闸机详情 - 深圳市优卡特实业有限公司",description:"YKT-TG100立式测温闸机产品详情，立式人脸测温通道闸机"}},{path:"/products/campus-shared/wm100-washer",name:"CampusSharedWM100",component:fi,meta:{title:"YKT-WM100共享洗衣机详情 - 深圳市优卡特实业有限公司",description:"YKT-WM100共享洗衣机产品详情，智能共享洗衣设备"}},{path:"/products/water-electric/wc100-water",name:"WaterElectricWC100",component:$i,meta:{title:"YKT-WC100智能水控机详情 - 深圳市优卡特实业有限公司",description:"YKT-WC100智能水控机产品详情，IC卡智能水控终端"}},{path:"/products/:category/:productId",name:"ProductDetail",component:()=>Mt(()=>import("./ProductDetail-Bi-_IzTU.js"),__vite__mapDeps([0,1,2,3])),meta:{title:"产品详情 - 深圳市优卡特实业有限公司",description:"查看产品详细信息"}},{path:"/solutions",name:"Solutions",component:qe,meta:{title:"解决方案 - 深圳市优卡特实业有限公司",description:"为不同行业提供专业的一卡通解决方案"}},{path:"/solutions/smart-campus",name:"SmartCampus",component:Jx,meta:{title:"智慧校园解决方案 - 深圳市优卡特实业有限公司",description:"构建数字化、智能化的现代校园生态，提供全方位的智慧校园解决方案"}},{path:"/solutions/enterprise-park",name:"EnterprisePark",component:nf,meta:{title:"企业园区解决方案 - 深圳市优卡特实业有限公司",description:"打造智能化、数字化的现代企业园区管理平台"}},{path:"/solutions/medical-institution",name:"MedicalInstitution",component:hf,meta:{title:"医疗机构解决方案 - 深圳市优卡特实业有限公司",description:"构建安全、高效的智慧医疗环境管理系统"}},{path:"/solutions/government-agency",name:"GovernmentAgency",component:If,meta:{title:"政府机关解决方案 - 深圳市优卡特实业有限公司",description:"打造安全、高效的数字政务环境管理平台"}},{path:"/solutions/factory-park",name:"FactoryPark",component:Wf,meta:{title:"工厂园区解决方案 - 深圳市优卡特实业有限公司",description:"构建安全、高效的智能制造环境管理系统"}},{path:"/solutions/smart-hotel",name:"SmartHotel",component:Vf,meta:{title:"智慧酒店解决方案 - 深圳市优卡特实业有限公司",description:"打造智能化、个性化的酒店服务体验管理平台"}},{path:"/news",name:"News",component:Ka,meta:{title:"新闻中心 - 深圳市优卡特实业有限公司",description:"了解优卡特最新动态与行业资讯"}},{path:"/news/company",name:"CompanyNews",component:tn,meta:{title:"企业新闻 - 深圳市优卡特实业有限公司",description:"了解优卡特最新企业动态"}},{path:"/news/industry",name:"IndustryNews",component:An,meta:{title:"行业资讯 - 深圳市优卡特实业有限公司",description:"关注行业动态，把握发展趋势"}},{path:"/news/support",name:"TechnicalSupport",component:ft,meta:{title:"技术支持 - 深圳市优卡特实业有限公司",description:"专业技术支持，解决您的疑问"}},{path:"/technical-support",name:"TechnicalSupportMain",component:ft,meta:{title:"技术支持 - 深圳市优卡特实业有限公司",description:"专业技术支持，解决您的疑问"}},{path:"/download-documents",name:"DownloadDocuments",component:a0,meta:{title:"下载文档 - 深圳市优卡特实业有限公司",description:"下载产品使用说明文档"}},{path:"/news/:id",name:"NewsDetail",component:Ps,meta:{title:"新闻详情 - 深圳市优卡特实业有限公司",description:"查看新闻详细内容"}},{path:"/news/product-release",name:"ProductRelease",component:M0,meta:{title:"产品发布 - 深圳市优卡特实业有限公司",description:"了解优卡特最新产品发布动态"}},{path:"/news/exhibition",name:"Exhibition",component:cc,meta:{title:"展会活动 - 深圳市优卡特实业有限公司",description:"了解优卡特参与的各类展会活动"}},{path:"/news/corporate-culture",name:"CorporateCulture",component:Lc,meta:{title:"企业文化 - 深圳市优卡特实业有限公司",description:"了解优卡特的企业文化和价值观"}},{path:"/news/technical-innovation",name:"TechnicalInnovation",component:bu,meta:{title:"技术创新 - 深圳市优卡特实业有限公司",description:"了解优卡特的技术创新成果"}},{path:"/news/company-news-category",name:"CompanyNewsCategory",component:Vu,meta:{title:"公司新闻 - 深圳市优卡特实业有限公司",description:"了解优卡特最新公司动态"}},{path:"/news/industry/market-analysis",name:"MarketAnalysis",component:Pp,meta:{title:"市场分析 - 深圳市优卡特实业有限公司",description:"深度解析行业市场趋势与发展机遇"}},{path:"/news/industry/policy-interpretation",name:"PolicyInterpretation",component:sg,meta:{title:"政策解读 - 深圳市优卡特实业有限公司",description:"深入解读行业政策法规与发展导向"}},{path:"/news/industry/technical-innovation",name:"IndustryTechnicalInnovation",component:Kg,meta:{title:"技术创新 - 深圳市优卡特实业有限公司",description:"探索行业前沿技术与创新应用"}},{path:"/news/industry/international-cooperation",name:"InternationalCooperation",component:ux,meta:{title:"国际合作 - 深圳市优卡特实业有限公司",description:"关注全球合作动态与国际交流"}},{path:"/news/industry/industry-report",name:"IndustryReport",component:Yx,meta:{title:"行业报告 - 深圳市优卡特实业有限公司",description:"权威发布行业深度研究报告"}},{path:"/contact",name:"Contact",component:Is,meta:{title:"联系我们 - 深圳市优卡特实业有限公司",description:"我们期待与您的合作，随时为您提供专业服务"}}],wt=Pt({history:jt(),routes:Qf,scrollBehavior(o,t,r){return r||{top:0}}});wt.beforeEach((o,t,r)=>{if(o.meta.title&&(document.title=o.meta.title),o.meta.description){const a=document.querySelector('meta[name="description"]');a&&a.setAttribute("content",o.meta.description)}r()});const _t="/images/products/szjocat.jpg",Xf={name:"Navbar"},Jf={class:"fixed w-full top-0 bg-white shadow-md z-50"},Zf={class:"container mx-auto px-4"},tv={class:"flex justify-between items-center py-4"},ev={class:"flex items-center"},av={class:"hidden lg:flex items-center space-x-8 nav-menu-bubbles flex-1 justify-end pr-8 pl-24"},sv={class:"nav-item group animate-fade-in",style:{"animation-delay":"0.3s"}},ov={class:"mega-menu py-2"},lv={class:"menu-item-with-submenu"},dv={class:"submenu"},rv={class:"menu-item-with-submenu"},iv={class:"submenu"},nv={class:"menu-item-with-submenu"},cv={class:"submenu"},uv={class:"menu-item-with-submenu"},pv={class:"submenu"},gv={class:"menu-item-with-submenu"},xv={class:"menu-item-with-submenu"},fv={class:"submenu"},vv={class:"menu-item-with-submenu"},mv={class:"submenu"},bv={class:"nav-item group animate-fade-in",style:{"animation-delay":"0.4s"}},hv={class:"mega-menu py-2"},yv={class:"nav-item group animate-fade-in",style:{"animation-delay":"0.5s"}},wv={class:"mega-menu py-2"},_v={class:"nav-item group animate-fade-in",style:{"animation-delay":"0.2s"}},$v={class:"mega-menu py-2"};function kv(o,t,r,a,g,x){const s=$("router-link");return u(),p("nav",Jf,[e("div",Zf,[e("div",tv,[e("div",ev,[n(s,{to:"/",class:"flex items-center"},{default:c(()=>t[0]||(t[0]=[e("img",{src:_t,alt:"深圳市优卡特实业有限公司标志，蓝色现代简约设计，展示公司品牌形象的科技风格logo",class:"navbar-logo w-auto max-w-none object-contain hover:opacity-80 transition duration-300"},null,-1)])),_:1,__:[0]})]),e("div",av,[n(s,{to:"/",class:"bubbles animate-fade-in",style:{"animation-delay":"0.1s"}},{default:c(()=>t[1]||(t[1]=[e("span",{class:"text"},"首页",-1)])),_:1,__:[1]}),e("div",sv,[n(s,{to:"/products",class:"bubbles"},{default:c(()=>t[2]||(t[2]=[e("span",{class:"text"},"产品中心",-1),e("i",{class:"fas fa-chevron-down text-xs"},null,-1)])),_:1,__:[2]}),e("div",ov,[e("div",lv,[n(s,{to:"/products/android-face",class:"menu-item-link"},{default:c(()=>t[3]||(t[3]=[e("span",null,"安卓人脸消费机",-1),e("i",{class:"fas fa-chevron-right text-xs"},null,-1)])),_:1,__:[3]}),e("div",dv,[n(s,{to:"/products/android-face/p301-2d-2w",class:"submenu-item"},{default:c(()=>t[4]||(t[4]=[i("P301-2D-2W台式人脸消费机")])),_:1,__:[4]})])]),e("div",rv,[n(s,{to:"/products/arm-terminal",class:"menu-item-link"},{default:c(()=>t[5]||(t[5]=[e("span",null,"ARM消费机",-1),e("i",{class:"fas fa-chevron-right text-xs"},null,-1)])),_:1,__:[5]}),e("div",iv,[n(s,{to:"/products/arm-terminal/a201-ic-2w",class:"submenu-item"},{default:c(()=>t[6]||(t[6]=[i("A201-IC-2W ARM消费机")])),_:1,__:[6]})])]),e("div",nv,[n(s,{to:"/products/face-temp-gate",class:"menu-item-link"},{default:c(()=>t[7]||(t[7]=[e("span",null,"人脸测温通道闸机",-1),e("i",{class:"fas fa-chevron-right text-xs"},null,-1)])),_:1,__:[7]}),e("div",cv,[n(s,{to:"/products/face-temp-gate/tg100-stand",class:"submenu-item"},{default:c(()=>t[8]||(t[8]=[i("YKT-TG100立式测温闸机")])),_:1,__:[8]})])]),e("div",uv,[n(s,{to:"/products/water-electric",class:"menu-item-link"},{default:c(()=>t[9]||(t[9]=[e("span",null,"水/电控机",-1),e("i",{class:"fas fa-chevron-right text-xs"},null,-1)])),_:1,__:[9]}),e("div",pv,[n(s,{to:"/products/water-electric/wc100-water",class:"submenu-item"},{default:c(()=>t[10]||(t[10]=[i("YKT-WC100智能水控机")])),_:1,__:[10]})])]),e("div",gv,[n(s,{to:"/products/access-control",class:"menu-item-link"},{default:c(()=>t[11]||(t[11]=[e("span",null,"门禁机",-1),e("i",{class:"fas fa-chevron-right text-xs"},null,-1)])),_:1,__:[11]}),t[12]||(t[12]=e("div",{class:"submenu"},null,-1))]),e("div",xv,[n(s,{to:"/products/campus-shared",class:"menu-item-link"},{default:c(()=>t[13]||(t[13]=[e("span",null,"校园共享设备",-1),e("i",{class:"fas fa-chevron-right text-xs"},null,-1)])),_:1,__:[13]}),e("div",fv,[n(s,{to:"/products/campus-shared/wm100-washer",class:"submenu-item"},{default:c(()=>t[14]||(t[14]=[i("YKT-WM100共享洗衣机")])),_:1,__:[14]})])]),e("div",vv,[n(s,{to:"/products/face-cloud",class:"menu-item-link"},{default:c(()=>t[15]||(t[15]=[e("span",null,"脸爱云平台",-1),e("i",{class:"fas fa-chevron-right text-xs"},null,-1)])),_:1,__:[15]}),e("div",mv,[n(s,{to:"/products/face-cloud/basic",class:"submenu-item"},{default:c(()=>t[16]||(t[16]=[i("脸爱云平台基础版")])),_:1,__:[16]})])])])]),e("div",bv,[n(s,{to:"/solutions",class:"bubbles"},{default:c(()=>t[17]||(t[17]=[e("span",{class:"text"},"解决方案",-1),e("i",{class:"fas fa-chevron-down text-xs"},null,-1)])),_:1,__:[17]}),e("div",hv,[n(s,{to:"/solutions/smart-campus",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[18]||(t[18]=[i("智慧校园解决方案")])),_:1,__:[18]}),n(s,{to:"/solutions/enterprise-park",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[19]||(t[19]=[i("企业园区解决方案")])),_:1,__:[19]}),n(s,{to:"/solutions/medical-institution",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[20]||(t[20]=[i("医疗机构解决方案")])),_:1,__:[20]}),n(s,{to:"/solutions/government-agency",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[21]||(t[21]=[i("政府机关解决方案")])),_:1,__:[21]}),n(s,{to:"/solutions/factory-park",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[22]||(t[22]=[i("工厂园区解决方案")])),_:1,__:[22]}),n(s,{to:"/solutions/smart-hotel",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[23]||(t[23]=[i("智慧酒店解决方案")])),_:1,__:[23]})])]),e("div",yv,[n(s,{to:"/news",class:"bubbles"},{default:c(()=>t[24]||(t[24]=[e("span",{class:"text"},"新闻中心",-1),e("i",{class:"fas fa-chevron-down text-xs"},null,-1)])),_:1,__:[24]}),e("div",wv,[n(s,{to:"/news/company",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[25]||(t[25]=[i("企业新闻")])),_:1,__:[25]}),n(s,{to:"/news/industry",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[26]||(t[26]=[i("行业资讯")])),_:1,__:[26]})])]),e("div",_v,[n(s,{to:"/about",class:"bubbles"},{default:c(()=>t[27]||(t[27]=[e("span",{class:"text"},"关于我们",-1),e("i",{class:"fas fa-chevron-down text-xs"},null,-1)])),_:1,__:[27]}),e("div",$v,[n(s,{to:"/about/history",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[28]||(t[28]=[i("发展历程")])),_:1,__:[28]}),n(s,{to:"/about/team",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[29]||(t[29]=[i("企业团队")])),_:1,__:[29]}),n(s,{to:"/about/honors",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[30]||(t[30]=[i("企业荣誉")])),_:1,__:[30]}),n(s,{to:"/about/careers",class:"block px-4 py-2 hover:bg-gray-100 text-gray-700"},{default:c(()=>t[31]||(t[31]=[i("招贤纳士")])),_:1,__:[31]})])]),n(s,{to:"/technical-support",class:"bubbles animate-fade-in",style:{"animation-delay":"0.6s"}},{default:c(()=>t[32]||(t[32]=[e("span",{class:"text"},"技术支持",-1)])),_:1,__:[32]}),n(s,{to:"/contact",class:"bubbles animate-fade-in",style:{"animation-delay":"0.65s"}},{default:c(()=>t[33]||(t[33]=[e("span",{class:"text"},"联系我们",-1)])),_:1,__:[33]})]),t[34]||(t[34]=e("div",{class:"lg:hidden"},[e("button",{class:"text-gray-800 focus:outline-none"},[e("i",{class:"fas fa-bars text-2xl"})])],-1))])])])}const Cv=_(Xf,[["render",kv]]),vt="/images/products/jocat.jpg",mt="/images/products/weibo.jpg",Pv={name:"Footer"},jv={class:"bg-gray-900 text-white pt-16 pb-8"},Fv={class:"container-custom px-6"},Iv={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 md:gap-3 mb-8"},Nv={class:"company-info-section"},Sv={class:"logo-container text-center mb-6"},Av={class:"space-y-2"},Mv={class:"space-y-2"};function Kv(o,t,r,a,g,x){const s=$("router-link");return u(),p("footer",jv,[e("div",Fv,[e("div",Iv,[e("div",Nv,[e("div",Sv,[n(s,{to:"/",class:"inline-block"},{default:c(()=>t[0]||(t[0]=[e("img",{src:_t,alt:"优卡特logo，白色版本用于深色背景，显示公司名称和标志性图形设计",class:"mx-auto h-14 md:h-16 w-auto max-w-none object-contain hover:opacity-80 transition duration-300"},null,-1)])),_:1,__:[0]})]),t[1]||(t[1]=e("div",{class:"social-media-section"},[e("h5",{class:"text-sm font-semibold text-gray-300 mb-6 text-center"},"关注我们"),e("div",{class:"example-2 flex justify-center items-center"},[e("div",{class:"icon-content mx-4 relative wechat-enhanced"},[e("div",{class:"tooltip-qr absolute -top-40 left-1/2 transform -translate-x-1/2 opacity-0 invisible transition-all duration-300 z-50"},[e("div",{class:"bg-white rounded-lg shadow-xl p-3 border-2 border-green-500 max-w-none"},[e("img",{src:vt,alt:"微信二维码",class:"w-20 h-32 rounded object-contain bg-white",onerror:"console.log('微信图片加载失败:', this.src); this.src='https://placehold.co/80x80/22c55e/FFFFFF?text=微信二维码'",onload:"console.log('微信图片加载成功:', this.src)"})])]),e("a",{href:"#","data-social":"wechat",class:"relative overflow-hidden flex justify-center items-center w-12 h-12 rounded-full text-gray-600 bg-white transition-all duration-300 hover:text-white hover:shadow-lg group"},[e("i",{class:"fab fa-weixin text-2xl relative z-10"}),e("div",{class:"filled absolute bottom-0 left-0 w-full h-0 bg-green-500 transition-all duration-300"})]),e("div",{class:"qr-popup absolute left-full top-1/2 transform -translate-y-1/2 ml-6 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-40"},[e("div",{class:"bg-white rounded-lg shadow-2xl p-4 border-2 border-green-500 relative min-w-max"},[e("div",{class:"absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-white"}),e("div",{class:"absolute right-full top-1/2 transform -translate-y-1/2 mr-0.5 w-0 h-0 border-t-3 border-b-3 border-r-3 border-t-transparent border-b-transparent border-r-green-500"}),e("div",{class:"text-center"},[e("img",{src:vt,alt:"微信二维码",class:"w-24 h-36 mx-auto mb-3 rounded object-contain bg-white",onerror:"console.log('微信大图片加载失败:', this.src); this.src='https://placehold.co/80x80/22c55e/FFFFFF?text=微信'",onload:"console.log('微信大图片加载成功:', this.src)"}),e("p",{class:"text-gray-800 text-xs font-semibold whitespace-nowrap"},"扫码关注我们的微信"),e("p",{class:"text-gray-600 text-xs"},"@优卡特科技")]),e("div",{class:"absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-ping"})])])]),e("div",{class:"icon-content mx-4 relative weibo-enhanced"},[e("div",{class:"tooltip-qr absolute -top-40 left-1/2 transform -translate-x-1/2 opacity-0 invisible transition-all duration-300 z-50"},[e("div",{class:"bg-white rounded-lg shadow-xl p-3 border-2 border-red-500 max-w-none"},[e("img",{src:mt,alt:"微博二维码",class:"w-20 h-32 rounded object-contain bg-white",onerror:"console.log('图片加载失败:', this.src); this.src='https://placehold.co/80x80/ef4444/FFFFFF?text=微博二维码'",onload:"console.log('图片加载成功:', this.src)"})])]),e("a",{href:"#","data-social":"weibo",class:"relative overflow-hidden flex justify-center items-center w-12 h-12 rounded-full text-gray-600 bg-white transition-all duration-300 hover:text-white hover:shadow-lg group"},[e("i",{class:"fab fa-weibo text-2xl relative z-10"}),e("div",{class:"filled absolute bottom-0 left-0 w-full h-0 bg-red-500 transition-all duration-300"})]),e("div",{class:"qr-popup absolute left-full top-1/2 transform -translate-y-1/2 ml-6 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-40"},[e("div",{class:"bg-white rounded-lg shadow-2xl p-4 border-2 border-red-500 relative min-w-max"},[e("div",{class:"absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-white"}),e("div",{class:"absolute right-full top-1/2 transform -translate-y-1/2 mr-0.5 w-0 h-0 border-t-3 border-b-3 border-r-3 border-t-transparent border-b-transparent border-r-red-500"}),e("div",{class:"text-center"},[e("img",{src:mt,alt:"微博二维码",class:"w-24 h-36 mx-auto mb-3 rounded object-contain bg-white",onerror:"console.log('大二维码加载失败:', this.src); this.src='https://placehold.co/80x80/FF6B6B/FFFFFF?text=微博'",onload:"console.log('大二维码加载成功:', this.src)"}),e("p",{class:"text-gray-800 text-xs font-semibold whitespace-nowrap"},"扫码关注我们的微博"),e("p",{class:"text-gray-600 text-xs"},"@优卡特科技")]),e("div",{class:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"})])])])])],-1))]),e("div",null,[t[8]||(t[8]=e("h4",{class:"text-lg font-bold mb-4"},"快速链接",-1)),e("ul",Av,[e("li",null,[n(s,{to:"/",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[2]||(t[2]=[i("首页")])),_:1,__:[2]})]),e("li",null,[n(s,{to:"/about",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[3]||(t[3]=[i("关于我们")])),_:1,__:[3]})]),e("li",null,[n(s,{to:"/products",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[4]||(t[4]=[i("产品中心")])),_:1,__:[4]})]),e("li",null,[n(s,{to:"/solutions",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[5]||(t[5]=[i("解决方案")])),_:1,__:[5]})]),e("li",null,[n(s,{to:"/news",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[6]||(t[6]=[i("新闻中心")])),_:1,__:[6]})]),e("li",null,[n(s,{to:"/contact",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[7]||(t[7]=[i("联系我们")])),_:1,__:[7]})])])]),e("div",null,[t[15]||(t[15]=e("h4",{class:"text-lg font-bold mb-4"},"产品中心",-1)),e("ul",Mv,[e("li",null,[n(s,{to:"/products/android-face",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[9]||(t[9]=[i("安卓人脸消费机")])),_:1,__:[9]})]),e("li",null,[n(s,{to:"/products/arm-terminal",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[10]||(t[10]=[i("ARM消费机")])),_:1,__:[10]})]),e("li",null,[n(s,{to:"/products/face-temp-gate",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[11]||(t[11]=[i("人脸测温通道闸机")])),_:1,__:[11]})]),e("li",null,[n(s,{to:"/products/water-electric",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[12]||(t[12]=[i("水/电控机")])),_:1,__:[12]})]),e("li",null,[n(s,{to:"/products/access-control",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[13]||(t[13]=[i("门禁机")])),_:1,__:[13]})]),e("li",null,[n(s,{to:"/products/campus-shared",class:"text-gray-400 hover:text-white transition duration-300"},{default:c(()=>t[14]||(t[14]=[i("校园共享设备")])),_:1,__:[14]})])])]),t[16]||(t[16]=f('<div data-v-0edcf70d><h4 class="text-lg font-bold mb-4" data-v-0edcf70d>联系信息</h4><ul class="space-y-2" data-v-0edcf70d><li class="flex items-start" data-v-0edcf70d><i class="fas fa-map-marker-alt mt-1 mr-3 text-gray-400" data-v-0edcf70d></i><span class="text-gray-400" data-v-0edcf70d>深圳市龙华新区大和路硅谷动力清湖园A9栋2楼</span></li><li class="flex items-start" data-v-0edcf70d><i class="fas fa-phone-alt mt-1 mr-3 text-gray-400" data-v-0edcf70d></i><a href="tel:159-8667-2052" class="text-gray-400 hover:text-white transition duration-300" data-v-0edcf70d>159-8667-2052</a></li><li class="flex items-start" data-v-0edcf70d><i class="fas fa-envelope mt-1 mr-3 text-gray-400" data-v-0edcf70d></i><a href="mailto:<EMAIL>" class="text-gray-400 hover:text-white transition duration-300" data-v-0edcf70d><EMAIL></a></li><li class="flex items-start" data-v-0edcf70d><i class="fas fa-clock mt-1 mr-3 text-gray-400" data-v-0edcf70d></i><span class="text-gray-400" data-v-0edcf70d>周一至周六: 8:55 - 18:00</span></li></ul></div>',1))]),t[17]||(t[17]=f('<div class="border-t border-gray-800 pt-8 mt-8" data-v-0edcf70d><div class="flex flex-col md:flex-row justify-center items-center space-y-2 md:space-y-0 md:space-x-4" data-v-0edcf70d><p class="text-gray-400 text-center" data-v-0edcf70d>© 2025-2035 -人脸消费机- 深圳市优卡特实业有限公司 版权所有</p><span class="text-gray-400 hidden md:inline" data-v-0edcf70d>|</span><a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition duration-300 text-center" data-v-0edcf70d> 粤ICP备20000796号 </a></div></div>',1))])])}const Tv=_(Pv,[["render",Kv],["__scopeId","data-v-0edcf70d"]]),Dv={name:"CustomerService",setup(){const o=X(),t=m(!1),r=m(!1),a=m(!1),g=m(!1),x=["/images/products/jocat.jpg","/images/products/szjocat.jpg","/images/products/weibo.jpg"],s=m(x[0]),l=m(0),d=()=>{a.value&&(t.value=!t.value,t.value||(r.value=!1))},w=z=>{console.error("图片加载失败:",z.target.src),l.value<x.length-1?(l.value++,s.value=x[l.value],console.log("尝试加载备用图片:",s.value)):(z.target.style.display="none",z.target.parentElement.innerHTML='<i class="fas fa-qrcode text-gray-400 text-4xl"></i>')},j=z=>{console.log("图片加载成功:",z.target.src)},C=()=>{g.value=o.path==="/"||o.name==="Home",console.log("当前页面检测:",{path:o.path,name:o.name,isHomePage:g.value})},y=()=>{if(!g.value){a.value=!0;return}const z=document.querySelector(".carousel-fullscreen");if(!z){const Y=window.pageYOffset>300;a.value=Y;return}const S=z.getBoundingClientRect().bottom<=0;!S&&a.value&&(t.value=!1,r.value=!1),a.value=S},K=()=>{C(),g.value?y():a.value=!0};let k=null;const b=()=>{k||(k=setTimeout(()=>{y(),k=null},16))};return dt(()=>o.path,()=>{console.log("路由变化，重新初始化客服组件显示状态"),K()},{immediate:!0}),F(()=>{console.log("客服组件已挂载"),K(),window.addEventListener("scroll",b,{passive:!0})}),ht(()=>{console.log("客服组件即将卸载"),window.removeEventListener("scroll",b),k&&clearTimeout(k)}),{isVisible:t,showQRCode:r,qrCodeImage:s,shouldShowService:a,toggleFloat:d,handleImageError:w,handleImageLoad:j}}},zv={class:"customer-service-container"},Rv={key:0,class:"customer-service-float fixed right-0 top-1/2 transform -translate-y-1/2 bg-white shadow-2xl rounded-l-2xl z-50"},Wv={class:"p-4 w-52"},Ev={class:"flex justify-center"},Lv={key:0,class:"qr-code-popup fixed right-52 top-1/2 transform -translate-y-1/2 bg-white shadow-xl rounded-lg p-3 z-40"},Yv={class:"text-center"},Bv={class:"w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center mb-2 overflow-hidden"},qv=["src"];function Gv(o,t,r,a,g,x){return u(),p("div",zv,[n(pt,{name:"fade-slide",appear:""},{default:c(()=>[a.isVisible&&a.shouldShowService?(u(),p("div",Rv,[e("button",{onClick:t[0]||(t[0]=(...s)=>a.toggleFloat&&a.toggleFloat(...s)),class:"absolute top-2 right-2 w-6 h-6 bg-gray-200 hover:bg-red-500 hover:text-white rounded-full flex items-center justify-center transition-all duration-300 text-gray-600 text-sm z-10"},t[6]||(t[6]=[e("i",{class:"fas fa-times"},null,-1)])),e("div",Wv,[t[8]||(t[8]=e("div",{class:"flex justify-center mb-3"},[e("div",{class:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg"},[e("i",{class:"fas fa-headset text-white text-lg"})])],-1)),t[9]||(t[9]=e("h3",{class:"text-center text-base font-bold text-gray-800 mb-3"},"在线客服",-1)),t[10]||(t[10]=e("div",{class:"space-y-2 mb-3"},[e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center"},[e("i",{class:"fas fa-phone text-green-600 text-xs"})]),e("div",null,[e("div",{class:"text-xs text-gray-500"},"电话"),e("a",{href:"tel:159-8667-2052",class:"text-xs font-medium text-blue-600 hover:text-blue-800 transition-colors"},"159-8667-2052")])]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center"},[e("i",{class:"fab fa-qq text-blue-600 text-xs"})]),e("div",null,[e("div",{class:"text-xs text-gray-500"},"QQ咨询"),e("div",{class:"text-xs font-medium text-blue-700"},"15986672052")])]),e("div",{class:"flex items-center space-x-2"},[e("div",{class:"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center"},[e("i",{class:"fas fa-envelope text-red-600 text-xs"})]),e("div",null,[e("div",{class:"text-xs text-gray-500"},"邮箱"),e("a",{href:"mailto:<EMAIL>",class:"text-xs font-medium text-blue-600 hover:text-blue-800 transition-colors"},"<EMAIL>")])])],-1)),t[11]||(t[11]=e("hr",{class:"border-gray-200 mb-3"},null,-1)),e("div",Ev,[e("div",{onMouseenter:t[1]||(t[1]=s=>a.showQRCode=!0),onMouseleave:t[2]||(t[2]=s=>a.showQRCode=!1),class:"w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-all duration-300 shadow-md"},t[7]||(t[7]=[e("i",{class:"fas fa-qrcode text-white text-lg"},null,-1)]),32)])])])):h("",!0)]),_:1}),n(pt,{name:"qr-fade",appear:""},{default:c(()=>[a.showQRCode&&a.isVisible&&a.shouldShowService?(u(),p("div",Lv,[e("div",Yv,[e("div",Bv,[e("img",{src:a.qrCodeImage,alt:"企业微信二维码",class:"w-full h-full object-cover",onError:t[3]||(t[3]=(...s)=>a.handleImageError&&a.handleImageError(...s)),onLoad:t[4]||(t[4]=(...s)=>a.handleImageLoad&&a.handleImageLoad(...s))},null,40,qv)]),t[12]||(t[12]=e("p",{class:"text-xs text-gray-600"},"扫码关注企业微信",-1))])])):h("",!0)]),_:1}),n(pt,{name:"button-fade",appear:""},{default:c(()=>[!a.isVisible&&a.shouldShowService?(u(),p("button",{key:0,onClick:t[5]||(t[5]=(...s)=>a.toggleFloat&&a.toggleFloat(...s)),class:"customer-service-toggle fixed right-4 top-1/2 transform -translate-y-1/2 z-50 uiverse-button"},t[13]||(t[13]=[e("i",{class:"fas fa-comments mr-1"},null,-1),i(" 在线客服 ")]))):h("",!0)]),_:1})])}const Hv=_(Dv,[["render",Gv],["__scopeId","data-v-d62170f9"]]),Ov={name:"App",components:{Navbar:Cv,Footer:Tv,CustomerService:Hv},setup(){const o=m(!1),t=()=>{o.value=window.pageYOffset>300},r=()=>{window.scrollTo({top:0,behavior:"smooth"})};return F(()=>{window.addEventListener("scroll",t)}),ht(()=>{window.removeEventListener("scroll",t)}),{showBackToTop:o,scrollToTop:r}}},Uv={id:"app"};function Vv(o,t,r,a,g,x){const s=$("Navbar"),l=$("router-view"),d=$("Footer"),w=$("CustomerService");return u(),p("div",Uv,[n(s),n(l),n(d),e("div",{id:"backToTop",class:M(["back-to-top",{visible:a.showBackToTop}]),onClick:t[0]||(t[0]=(...j)=>a.scrollToTop&&a.scrollToTop(...j))},t[1]||(t[1]=[e("i",{class:"fas fa-arrow-up"},null,-1)]),2),n(w)])}const Qv=_(Ov,[["render",Vv]]),gt=Ft(Qv);gt.use(It());gt.use(wt);gt.mount("#app");document.addEventListener("DOMContentLoaded",()=>{N.init({duration:1e3,easing:"ease-out-cubic",once:!0,offset:100})});export{O as P,_};
