# 后台管理系统前端设计

## 技术栈

- **框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **富文本编辑器**: Quill.js 或 TinyMCE
- **图片上传**: Element Plus Upload + 七牛云/阿里云OSS

## 项目结构

```
frontend/admin/
├── src/
│   ├── components/          # 通用组件
│   │   ├── common/         # 公共组件
│   │   │   ├── ImageUpload.vue      # 图片上传组件
│   │   │   ├── FileUpload.vue       # 文件上传组件
│   │   │   ├── RichEditor.vue       # 富文本编辑器
│   │   │   ├── DataTable.vue        # 数据表格组件
│   │   │   └── FormDialog.vue       # 表单对话框
│   │   ├── layout/         # 布局组件
│   │   │   ├── AdminLayout.vue      # 管理后台布局
│   │   │   ├── Sidebar.vue          # 侧边栏
│   │   │   ├── Header.vue           # 顶部导航
│   │   │   └── Breadcrumb.vue       # 面包屑导航
│   │   └── business/       # 业务组件
│   │       ├── ProductForm.vue      # 产品表单
│   │       ├── NewsForm.vue         # 新闻表单
│   │       └── SpecificationTable.vue # 技术规格表格
│   ├── views/              # 页面组件
│   │   ├── Login.vue       # 登录页
│   │   ├── Dashboard.vue   # 仪表板
│   │   ├── home/          # 首页管理
│   │   │   ├── CarouselManage.vue   # 轮播图管理
│   │   │   ├── SolutionManage.vue   # 解决方案管理
│   │   │   ├── CoreProductManage.vue # 核心产品管理
│   │   │   └── HomeNewsManage.vue   # 首页新闻管理
│   │   ├── products/      # 产品管理
│   │   │   ├── CategoryManage.vue   # 分类管理
│   │   │   ├── ProductList.vue      # 产品列表
│   │   │   ├── ProductEdit.vue      # 产品编辑
│   │   │   └── SpecificationEdit.vue # 规格编辑
│   │   ├── news/          # 新闻管理
│   │   │   ├── NewsList.vue         # 新闻列表
│   │   │   ├── NewsEdit.vue         # 新闻编辑
│   │   │   └── NewsCategory.vue     # 新闻分类
│   │   ├── solutions/     # 解决方案管理
│   │   │   ├── SolutionList.vue     # 解决方案列表
│   │   │   └── SolutionEdit.vue     # 解决方案编辑
│   │   ├── about/         # 关于我们管理
│   │   │   └── AboutEdit.vue        # 关于我们编辑
│   │   ├── support/       # 技术支持管理
│   │   │   └── DocumentManage.vue   # 文档管理
│   │   └── system/        # 系统管理
│   │       ├── UserManage.vue       # 用户管理
│   │       ├── ConfigManage.vue     # 配置管理
│   │       └── LogManage.vue        # 日志管理
│   ├── stores/            # Pinia状态管理
│   │   ├── auth.js        # 认证状态
│   │   ├── product.js     # 产品状态
│   │   ├── news.js        # 新闻状态
│   │   └── common.js      # 公共状态
│   ├── api/               # API接口
│   │   ├── auth.js        # 认证接口
│   │   ├── product.js     # 产品接口
│   │   ├── news.js        # 新闻接口
│   │   ├── upload.js      # 上传接口
│   │   └── common.js      # 公共接口
│   ├── utils/             # 工具函数
│   │   ├── request.js     # HTTP请求封装
│   │   ├── auth.js        # 认证工具
│   │   ├── upload.js      # 上传工具
│   │   └── validate.js    # 表单验证
│   ├── router/            # 路由配置
│   │   └── index.js
│   ├── styles/            # 样式文件
│   │   ├── index.scss     # 主样式
│   │   ├── variables.scss # 变量
│   │   └── admin.scss     # 管理后台样式
│   ├── App.vue
│   └── main.js
├── public/
├── package.json
└── vite.config.js
```

## 核心功能页面设计

### 1. 首页管理

#### 1.1 轮播图管理 (CarouselManage.vue)
```vue
<template>
  <div class="carousel-manage">
    <!-- 操作栏 -->
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加轮播图
      </el-button>
    </div>

    <!-- 轮播图列表 -->
    <el-table :data="carouselList" v-loading="loading">
      <el-table-column label="预览" width="120">
        <template #default="{ row }">
          <el-image 
            :src="row.imageUrl" 
            :preview-src-list="[row.imageUrl]"
            style="width: 80px; height: 45px"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="subtitle" label="副标题" />
      <el-table-column prop="sortOrder" label="排序" width="80" />
      <el-table-column prop="isActive" label="状态" width="80">
        <template #default="{ row }">
          <el-switch 
            v-model="row.isActive" 
            @change="handleStatusChange(row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" />
        </el-form-item>
        <el-form-item label="副标题" prop="subtitle">
          <el-input v-model="form.subtitle" />
        </el-form-item>
        <el-form-item label="轮播图片" prop="imageUrl">
          <ImageUpload v-model="form.imageUrl" />
        </el-form-item>
        <el-form-item label="跳转链接" prop="linkUrl">
          <el-input v-model="form.linkUrl" />
        </el-form-item>
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
```

#### 1.2 解决方案管理 (SolutionManage.vue)
- 支持拖拽排序
- 图片上传和预览
- 特性标签的动态添加删除
- 内容图片的批量管理

### 2. 产品管理

#### 2.1 产品分类管理 (CategoryManage.vue)
```vue
<template>
  <div class="category-manage">
    <!-- 分类树形结构 -->
    <el-tree
      :data="categoryTree"
      :props="treeProps"
      node-key="id"
      :expand-on-click-node="false"
      :render-content="renderTreeNode"
    />

    <!-- 分类编辑对话框 -->
    <el-dialog v-model="categoryDialogVisible" :title="categoryDialogTitle">
      <el-form :model="categoryForm" :rules="categoryRules" ref="categoryFormRef">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" />
        </el-form-item>
        <el-form-item label="分类代码" prop="code">
          <el-input v-model="categoryForm.code" />
        </el-form-item>
        <el-form-item label="分类描述" prop="description">
          <el-input type="textarea" v-model="categoryForm.description" />
        </el-form-item>
        <el-form-item label="分类图片" prop="imageUrl">
          <ImageUpload v-model="categoryForm.imageUrl" />
        </el-form-item>
        <el-form-item label="路由路径" prop="routePath">
          <el-input v-model="categoryForm.routePath" />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
```

#### 2.2 产品编辑 (ProductEdit.vue)
- 基于P301模板的产品创建
- 缩略图管理（1-5张）
- 产品标签的动态管理
- 技术规格表格编辑
- 产品详情章节管理

### 3. 新闻管理

#### 3.1 新闻列表 (NewsList.vue)
```vue
<template>
  <div class="news-list">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :model="searchForm" inline>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" clearable>
            <el-option label="公司新闻" value="company" />
            <el-option label="行业新闻" value="industry" />
            <el-option label="技术支持" value="support" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" clearable>
            <el-option label="已发布" value="published" />
            <el-option label="草稿" value="draft" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 新闻表格 -->
    <el-table :data="newsList" v-loading="loading">
      <el-table-column label="封面" width="100">
        <template #default="{ row }">
          <el-image 
            :src="row.imageUrl" 
            style="width: 60px; height: 40px"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="category" label="分类" width="100" />
      <el-table-column label="标签" width="150">
        <template #default="{ row }">
          <el-tag v-if="row.isHot" type="danger" size="small">热门</el-tag>
          <el-tag v-if="row.isFeatured" type="success" size="small">首页</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="viewCount" label="浏览量" width="80" />
      <el-table-column prop="publishDate" label="发布时间" width="150" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button size="small" type="success" @click="handleToggleFeatured(row)">
            {{ row.isFeatured ? '取消首页' : '设为首页' }}
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
```

### 4. 通用组件

#### 4.1 图片上传组件 (ImageUpload.vue)
```vue
<template>
  <div class="image-upload">
    <el-upload
      :action="uploadUrl"
      :headers="uploadHeaders"
      :show-file-list="false"
      :on-success="handleSuccess"
      :before-upload="beforeUpload"
      accept="image/*"
    >
      <div v-if="imageUrl" class="image-preview">
        <el-image :src="imageUrl" fit="cover" />
        <div class="image-overlay">
          <el-icon><Edit /></el-icon>
        </div>
      </div>
      <div v-else class="upload-placeholder">
        <el-icon><Plus /></el-icon>
        <div>点击上传图片</div>
      </div>
    </el-upload>
  </div>
</template>
```

#### 4.2 富文本编辑器 (RichEditor.vue)
```vue
<template>
  <div class="rich-editor">
    <div ref="editorRef" />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

const props = defineProps({
  modelValue: String
})

const emit = defineEmits(['update:modelValue'])

const editorRef = ref()
let quill = null

onMounted(() => {
  quill = new Quill(editorRef.value, {
    theme: 'snow',
    modules: {
      toolbar: [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'font': [] }],
        [{ 'align': [] }],
        ['clean'],
        ['link', 'image']
      ]
    }
  })

  quill.on('text-change', () => {
    emit('update:modelValue', quill.root.innerHTML)
  })

  if (props.modelValue) {
    quill.root.innerHTML = props.modelValue
  }
})

watch(() => props.modelValue, (newVal) => {
  if (quill && newVal !== quill.root.innerHTML) {
    quill.root.innerHTML = newVal || ''
  }
})
</script>
```

## 状态管理设计

### 产品状态管理 (stores/product.js)
```javascript
import { defineStore } from 'pinia'
import { productApi } from '@/api/product'

export const useProductStore = defineStore('product', {
  state: () => ({
    categories: [],
    products: [],
    currentProduct: null,
    loading: false
  }),

  actions: {
    async fetchCategories() {
      this.loading = true
      try {
        const response = await productApi.getCategories()
        this.categories = response.data
      } finally {
        this.loading = false
      }
    },

    async createProduct(productData) {
      const response = await productApi.createProduct(productData)
      this.products.unshift(response.data)
      return response.data
    },

    async updateProduct(id, productData) {
      const response = await productApi.updateProduct(id, productData)
      const index = this.products.findIndex(p => p.id === id)
      if (index !== -1) {
        this.products[index] = response.data
      }
      return response.data
    }
  }
})
```

## 路由配置

```javascript
const routes = [
  {
    path: '/admin/login',
    name: 'AdminLogin',
    component: () => import('@/views/Login.vue')
  },
  {
    path: '/admin',
    component: () => import('@/components/layout/AdminLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue')
      },
      {
        path: 'home',
        name: 'HomeManage',
        children: [
          {
            path: 'carousel',
            name: 'CarouselManage',
            component: () => import('@/views/home/<USER>')
          },
          {
            path: 'solutions',
            name: 'SolutionManage',
            component: () => import('@/views/home/<USER>')
          }
        ]
      },
      {
        path: 'products',
        name: 'ProductManage',
        children: [
          {
            path: 'categories',
            name: 'CategoryManage',
            component: () => import('@/views/products/CategoryManage.vue')
          },
          {
            path: 'list',
            name: 'ProductList',
            component: () => import('@/views/products/ProductList.vue')
          },
          {
            path: 'edit/:id?',
            name: 'ProductEdit',
            component: () => import('@/views/products/ProductEdit.vue')
          }
        ]
      }
    ]
  }
]
```

这个设计提供了完整的后台管理系统前端架构，支持您提到的所有功能需求，包括轮播图管理、产品系列管理、新闻管理、解决方案管理等。每个组件都采用了现代化的Vue 3 + Element Plus技术栈，提供良好的用户体验和开发体验。
