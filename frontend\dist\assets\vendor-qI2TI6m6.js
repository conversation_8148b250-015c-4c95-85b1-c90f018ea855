/**
* @vue/shared v3.5.17
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function As(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const oe={},It=[],qe=()=>{},Ii=()=>!1,Ln=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ps=e=>e.startsWith("onUpdate:"),de=Object.assign,Ts=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Li=Object.prototype.hasOwnProperty,ee=(e,t)=>Li.call(e,t),H=Array.isArra<PERSON>,Lt=e=>Nn(e)==="[object Map]",Zr=e=>Nn(e)==="[object Set]",U=e=>typeof e=="function",ue=e=>typeof e=="string",gt=e=>typeof e=="symbol",ce=e=>e!==null&&typeof e=="object",eo=e=>(ce(e)||U(e))&&U(e.then)&&U(e.catch),to=Object.prototype.toString,Nn=e=>to.call(e),Ni=e=>Nn(e).slice(8,-1),no=e=>Nn(e)==="[object Object]",Os=e=>ue(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ut=As(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Fn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Fi=/-(\w)/g,Ne=Fn(e=>e.replace(Fi,(t,n)=>n?n.toUpperCase():"")),$i=/\B([A-Z])/g,mt=Fn(e=>e.replace($i,"-$1").toLowerCase()),$n=Fn(e=>e.charAt(0).toUpperCase()+e.slice(1)),zn=Fn(e=>e?`on${$n(e)}`:""),at=(e,t)=>!Object.is(e,t),_n=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},us=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},as=e=>{const t=parseFloat(e);return isNaN(t)?e:t},ji=e=>{const t=ue(e)?Number(e):NaN;return isNaN(t)?e:t};let Xs;const jn=()=>Xs||(Xs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ms(e){if(H(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ue(s)?ki(s):Ms(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ue(e)||ce(e))return e}const Di=/;(?![^(]*\))/g,Hi=/:([^]+)/,Bi=/\/\*[^]*?\*\//g;function ki(e){const t={};return e.replace(Bi,"").split(Di).forEach(n=>{if(n){const s=n.split(Hi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Is(e){let t="";if(ue(e))t=e;else if(H(e))for(let n=0;n<e.length;n++){const s=Is(e[n]);s&&(t+=s+" ")}else if(ce(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Vi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ki=As(Vi);function so(e){return!!e||e===""}const ro=e=>!!(e&&e.__v_isRef===!0),Ui=e=>ue(e)?e:e==null?"":H(e)||ce(e)&&(e.toString===to||!U(e.toString))?ro(e)?Ui(e.value):JSON.stringify(e,oo,2):String(e),oo=(e,t)=>ro(t)?oo(e,t.value):Lt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Jn(s,o)+" =>"]=r,n),{})}:Zr(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Jn(n))}:gt(t)?Jn(t):ce(t)&&!H(t)&&!no(t)?String(t):t,Jn=(e,t="")=>{var n;return gt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ge;class io{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ge,!t&&ge&&(this.index=(ge.scopes||(ge.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ge;try{return ge=this,t()}finally{ge=n}}}on(){++this._on===1&&(this.prevScope=ge,ge=this)}off(){this._on>0&&--this._on===0&&(ge=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function lo(e){return new io(e)}function co(){return ge}function Wi(e,t=!1){ge&&ge.cleanups.push(e)}let le;const Qn=new WeakSet;class fo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ge&&ge.active&&ge.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Qn.has(this)&&(Qn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ao(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Zs(this),ho(this);const t=le,n=Fe;le=this,Fe=!0;try{return this.fn()}finally{po(this),le=t,Fe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Fs(t);this.deps=this.depsTail=void 0,Zs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Qn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){hs(this)&&this.run()}get dirty(){return hs(this)}}let uo=0,Wt,qt;function ao(e,t=!1){if(e.flags|=8,t){e.next=qt,qt=e;return}e.next=Wt,Wt=e}function Ls(){uo++}function Ns(){if(--uo>0)return;if(qt){let t=qt;for(qt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Wt;){let t=Wt;for(Wt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function ho(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function po(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Fs(s),qi(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function hs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(go(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function go(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===tn)||(e.globalVersion=tn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!hs(e))))return;e.flags|=2;const t=e.dep,n=le,s=Fe;le=e,Fe=!0;try{ho(e);const r=e.fn(e._value);(t.version===0||at(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{le=n,Fe=s,po(e),e.flags&=-3}}function Fs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Fs(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function qi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Fe=!0;const mo=[];function et(){mo.push(Fe),Fe=!1}function tt(){const e=mo.pop();Fe=e===void 0?!0:e}function Zs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=le;le=void 0;try{t()}finally{le=n}}}let tn=0;class Gi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class $s{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!le||!Fe||le===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==le)n=this.activeLink=new Gi(le,this),le.deps?(n.prevDep=le.depsTail,le.depsTail.nextDep=n,le.depsTail=n):le.deps=le.depsTail=n,yo(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=le.depsTail,n.nextDep=void 0,le.depsTail.nextDep=n,le.depsTail=n,le.deps===n&&(le.deps=s)}return n}trigger(t){this.version++,tn++,this.notify(t)}notify(t){Ls();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ns()}}}function yo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)yo(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const wn=new WeakMap,xt=Symbol(""),ds=Symbol(""),nn=Symbol("");function me(e,t,n){if(Fe&&le){let s=wn.get(e);s||wn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new $s),r.map=s,r.key=n),r.track()}}function Xe(e,t,n,s,r,o){const i=wn.get(e);if(!i){tn++;return}const l=c=>{c&&c.trigger()};if(Ls(),t==="clear")i.forEach(l);else{const c=H(e),d=c&&Os(n);if(c&&n==="length"){const f=Number(s);i.forEach((a,p)=>{(p==="length"||p===nn||!gt(p)&&p>=f)&&l(a)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),d&&l(i.get(nn)),t){case"add":c?d&&l(i.get("length")):(l(i.get(xt)),Lt(e)&&l(i.get(ds)));break;case"delete":c||(l(i.get(xt)),Lt(e)&&l(i.get(ds)));break;case"set":Lt(e)&&l(i.get(xt));break}}Ns()}function zi(e,t){const n=wn.get(e);return n&&n.get(t)}function At(e){const t=Y(e);return t===e?t:(me(t,"iterate",nn),Ie(e)?t:t.map(pe))}function Dn(e){return me(e=Y(e),"iterate",nn),e}const Ji={__proto__:null,[Symbol.iterator](){return Yn(this,Symbol.iterator,pe)},concat(...e){return At(this).concat(...e.map(t=>H(t)?At(t):t))},entries(){return Yn(this,"entries",e=>(e[1]=pe(e[1]),e))},every(e,t){return ze(this,"every",e,t,void 0,arguments)},filter(e,t){return ze(this,"filter",e,t,n=>n.map(pe),arguments)},find(e,t){return ze(this,"find",e,t,pe,arguments)},findIndex(e,t){return ze(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return ze(this,"findLast",e,t,pe,arguments)},findLastIndex(e,t){return ze(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return ze(this,"forEach",e,t,void 0,arguments)},includes(...e){return Xn(this,"includes",e)},indexOf(...e){return Xn(this,"indexOf",e)},join(e){return At(this).join(e)},lastIndexOf(...e){return Xn(this,"lastIndexOf",e)},map(e,t){return ze(this,"map",e,t,void 0,arguments)},pop(){return Ht(this,"pop")},push(...e){return Ht(this,"push",e)},reduce(e,...t){return er(this,"reduce",e,t)},reduceRight(e,...t){return er(this,"reduceRight",e,t)},shift(){return Ht(this,"shift")},some(e,t){return ze(this,"some",e,t,void 0,arguments)},splice(...e){return Ht(this,"splice",e)},toReversed(){return At(this).toReversed()},toSorted(e){return At(this).toSorted(e)},toSpliced(...e){return At(this).toSpliced(...e)},unshift(...e){return Ht(this,"unshift",e)},values(){return Yn(this,"values",pe)}};function Yn(e,t,n){const s=Dn(e),r=s[t]();return s!==e&&!Ie(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Qi=Array.prototype;function ze(e,t,n,s,r,o){const i=Dn(e),l=i!==e&&!Ie(e),c=i[t];if(c!==Qi[t]){const a=c.apply(e,o);return l?pe(a):a}let d=n;i!==e&&(l?d=function(a,p){return n.call(this,pe(a),p,e)}:n.length>2&&(d=function(a,p){return n.call(this,a,p,e)}));const f=c.call(i,d,s);return l&&r?r(f):f}function er(e,t,n,s){const r=Dn(e);let o=n;return r!==e&&(Ie(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,pe(l),c,e)}),r[t](o,...s)}function Xn(e,t,n){const s=Y(e);me(s,"iterate",nn);const r=s[t](...n);return(r===-1||r===!1)&&Hs(n[0])?(n[0]=Y(n[0]),s[t](...n)):r}function Ht(e,t,n=[]){et(),Ls();const s=Y(e)[t].apply(e,n);return Ns(),tt(),s}const Yi=As("__proto__,__v_isRef,__isVue"),vo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(gt));function Xi(e){gt(e)||(e=String(e));const t=Y(this);return me(t,"has",e),t.hasOwnProperty(e)}class _o{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?cl:xo:o?Eo:So).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=H(t);if(!r){let c;if(i&&(c=Ji[n]))return c;if(n==="hasOwnProperty")return Xi}const l=Reflect.get(t,n,fe(t)?t:s);return(gt(n)?vo.has(n):Yi(n))||(r||me(t,"get",n),o)?l:fe(l)?i&&Os(n)?l:l.value:ce(l)?r?Co(l):an(l):l}}class bo extends _o{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=dt(o);if(!Ie(s)&&!dt(s)&&(o=Y(o),s=Y(s)),!H(t)&&fe(o)&&!fe(s))return c?!1:(o.value=s,!0)}const i=H(t)&&Os(n)?Number(n)<t.length:ee(t,n),l=Reflect.set(t,n,s,fe(t)?t:r);return t===Y(r)&&(i?at(s,o)&&Xe(t,"set",n,s):Xe(t,"add",n,s)),l}deleteProperty(t,n){const s=ee(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Xe(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!gt(n)||!vo.has(n))&&me(t,"has",n),s}ownKeys(t){return me(t,"iterate",H(t)?"length":xt),Reflect.ownKeys(t)}}class Zi extends _o{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const el=new bo,tl=new Zi,nl=new bo(!0);const ps=e=>e,gn=e=>Reflect.getPrototypeOf(e);function sl(e,t,n){return function(...s){const r=this.__v_raw,o=Y(r),i=Lt(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,d=r[e](...s),f=n?ps:t?Cn:pe;return!t&&me(o,"iterate",c?ds:xt),{next(){const{value:a,done:p}=d.next();return p?{value:a,done:p}:{value:l?[f(a[0]),f(a[1])]:f(a),done:p}},[Symbol.iterator](){return this}}}}function mn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function rl(e,t){const n={get(r){const o=this.__v_raw,i=Y(o),l=Y(r);e||(at(r,l)&&me(i,"get",r),me(i,"get",l));const{has:c}=gn(i),d=t?ps:e?Cn:pe;if(c.call(i,r))return d(o.get(r));if(c.call(i,l))return d(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&me(Y(r),"iterate",xt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=Y(o),l=Y(r);return e||(at(r,l)&&me(i,"has",r),me(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=Y(l),d=t?ps:e?Cn:pe;return!e&&me(c,"iterate",xt),l.forEach((f,a)=>r.call(o,d(f),d(a),i))}};return de(n,e?{add:mn("add"),set:mn("set"),delete:mn("delete"),clear:mn("clear")}:{add(r){!t&&!Ie(r)&&!dt(r)&&(r=Y(r));const o=Y(this);return gn(o).has.call(o,r)||(o.add(r),Xe(o,"add",r,r)),this},set(r,o){!t&&!Ie(o)&&!dt(o)&&(o=Y(o));const i=Y(this),{has:l,get:c}=gn(i);let d=l.call(i,r);d||(r=Y(r),d=l.call(i,r));const f=c.call(i,r);return i.set(r,o),d?at(o,f)&&Xe(i,"set",r,o):Xe(i,"add",r,o),this},delete(r){const o=Y(this),{has:i,get:l}=gn(o);let c=i.call(o,r);c||(r=Y(r),c=i.call(o,r)),l&&l.call(o,r);const d=o.delete(r);return c&&Xe(o,"delete",r,void 0),d},clear(){const r=Y(this),o=r.size!==0,i=r.clear();return o&&Xe(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=sl(r,e,t)}),n}function js(e,t){const n=rl(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(ee(n,r)&&r in s?n:s,r,o)}const ol={get:js(!1,!1)},il={get:js(!1,!0)},ll={get:js(!0,!1)};const So=new WeakMap,Eo=new WeakMap,xo=new WeakMap,cl=new WeakMap;function fl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ul(e){return e.__v_skip||!Object.isExtensible(e)?0:fl(Ni(e))}function an(e){return dt(e)?e:Ds(e,!1,el,ol,So)}function wo(e){return Ds(e,!1,nl,il,Eo)}function Co(e){return Ds(e,!0,tl,ll,xo)}function Ds(e,t,n,s,r){if(!ce(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=ul(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function ht(e){return dt(e)?ht(e.__v_raw):!!(e&&e.__v_isReactive)}function dt(e){return!!(e&&e.__v_isReadonly)}function Ie(e){return!!(e&&e.__v_isShallow)}function Hs(e){return e?!!e.__v_raw:!1}function Y(e){const t=e&&e.__v_raw;return t?Y(t):e}function Bs(e){return!ee(e,"__v_skip")&&Object.isExtensible(e)&&us(e,"__v_skip",!0),e}const pe=e=>ce(e)?an(e):e,Cn=e=>ce(e)?Co(e):e;function fe(e){return e?e.__v_isRef===!0:!1}function ks(e){return Ro(e,!1)}function al(e){return Ro(e,!0)}function Ro(e,t){return fe(e)?e:new hl(e,t)}class hl{constructor(t,n){this.dep=new $s,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Y(t),this._value=n?t:pe(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ie(t)||dt(t);t=s?t:Y(t),at(t,n)&&(this._rawValue=t,this._value=s?t:pe(t),this.dep.trigger())}}function Nt(e){return fe(e)?e.value:e}const dl={get:(e,t,n)=>t==="__v_raw"?e:Nt(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return fe(r)&&!fe(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Ao(e){return ht(e)?e:new Proxy(e,dl)}function pl(e){const t=H(e)?new Array(e.length):{};for(const n in e)t[n]=ml(e,n);return t}class gl{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return zi(Y(this._object),this._key)}}function ml(e,t,n){const s=e[t];return fe(s)?s:new gl(e,t,n)}class yl{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new $s(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=tn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&le!==this)return ao(this,!0),!0}get value(){const t=this.dep.track();return go(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function vl(e,t,n=!1){let s,r;return U(e)?s=e:(s=e.get,r=e.set),new yl(s,r,n)}const yn={},Rn=new WeakMap;let St;function _l(e,t=!1,n=St){if(n){let s=Rn.get(n);s||Rn.set(n,s=[]),s.push(e)}}function bl(e,t,n=oe){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,d=I=>r?I:Ie(I)||r===!1||r===0?Ze(I,1):Ze(I);let f,a,p,m,E=!1,P=!1;if(fe(e)?(a=()=>e.value,E=Ie(e)):ht(e)?(a=()=>d(e),E=!0):H(e)?(P=!0,E=e.some(I=>ht(I)||Ie(I)),a=()=>e.map(I=>{if(fe(I))return I.value;if(ht(I))return d(I);if(U(I))return c?c(I,2):I()})):U(e)?t?a=c?()=>c(e,2):e:a=()=>{if(p){et();try{p()}finally{tt()}}const I=St;St=f;try{return c?c(e,3,[m]):e(m)}finally{St=I}}:a=qe,t&&r){const I=a,k=r===!0?1/0:r;a=()=>Ze(I(),k)}const B=co(),F=()=>{f.stop(),B&&B.active&&Ts(B.effects,f)};if(o&&t){const I=t;t=(...k)=>{I(...k),F()}}let L=P?new Array(e.length).fill(yn):yn;const N=I=>{if(!(!(f.flags&1)||!f.dirty&&!I))if(t){const k=f.run();if(r||E||(P?k.some((ne,G)=>at(ne,L[G])):at(k,L))){p&&p();const ne=St;St=f;try{const G=[k,L===yn?void 0:P&&L[0]===yn?[]:L,m];L=k,c?c(t,3,G):t(...G)}finally{St=ne}}}else f.run()};return l&&l(N),f=new fo(a),f.scheduler=i?()=>i(N,!1):N,m=I=>_l(I,!1,f),p=f.onStop=()=>{const I=Rn.get(f);if(I){if(c)c(I,4);else for(const k of I)k();Rn.delete(f)}},t?s?N(!0):L=f.run():i?i(N.bind(null,!0),!0):f.run(),F.pause=f.pause.bind(f),F.resume=f.resume.bind(f),F.stop=F,F}function Ze(e,t=1/0,n){if(t<=0||!ce(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,fe(e))Ze(e.value,t,n);else if(H(e))for(let s=0;s<e.length;s++)Ze(e[s],t,n);else if(Zr(e)||Lt(e))e.forEach(s=>{Ze(s,t,n)});else if(no(e)){for(const s in e)Ze(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Ze(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function hn(e,t,n,s){try{return s?e(...s):e()}catch(r){Hn(r,t,n)}}function $e(e,t,n,s){if(U(e)){const r=hn(e,t,n,s);return r&&eo(r)&&r.catch(o=>{Hn(o,t,n)}),r}if(H(e)){const r=[];for(let o=0;o<e.length;o++)r.push($e(e[o],t,n,s));return r}}function Hn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||oe;if(t){let l=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const f=l.ec;if(f){for(let a=0;a<f.length;a++)if(f[a](e,c,d)===!1)return}l=l.parent}if(o){et(),hn(o,null,10,[e,c,d]),tt();return}}Sl(e,n,r,s,i)}function Sl(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const _e=[];let Ke=-1;const Ft=[];let lt=null,Tt=0;const Po=Promise.resolve();let An=null;function Vs(e){const t=An||Po;return e?t.then(this?e.bind(this):e):t}function El(e){let t=Ke+1,n=_e.length;for(;t<n;){const s=t+n>>>1,r=_e[s],o=sn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Ks(e){if(!(e.flags&1)){const t=sn(e),n=_e[_e.length-1];!n||!(e.flags&2)&&t>=sn(n)?_e.push(e):_e.splice(El(t),0,e),e.flags|=1,To()}}function To(){An||(An=Po.then(Mo))}function xl(e){H(e)?Ft.push(...e):lt&&e.id===-1?lt.splice(Tt+1,0,e):e.flags&1||(Ft.push(e),e.flags|=1),To()}function tr(e,t,n=Ke+1){for(;n<_e.length;n++){const s=_e[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;_e.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Oo(e){if(Ft.length){const t=[...new Set(Ft)].sort((n,s)=>sn(n)-sn(s));if(Ft.length=0,lt){lt.push(...t);return}for(lt=t,Tt=0;Tt<lt.length;Tt++){const n=lt[Tt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}lt=null,Tt=0}}const sn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Mo(e){try{for(Ke=0;Ke<_e.length;Ke++){const t=_e[Ke];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),hn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ke<_e.length;Ke++){const t=_e[Ke];t&&(t.flags&=-2)}Ke=-1,_e.length=0,Oo(),An=null,(_e.length||Ft.length)&&Mo()}}let Se=null,Io=null;function Pn(e){const t=Se;return Se=e,Io=e&&e.type.__scopeId||null,t}function wl(e,t=Se,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&hr(-1);const o=Pn(t);let i;try{i=e(...r)}finally{Pn(o),s._d&&hr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function _u(e,t){if(Se===null)return e;const n=Un(Se),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=oe]=t[r];o&&(U(o)&&(o={mounted:o,updated:o}),o.deep&&Ze(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function yt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(et(),$e(c,n,8,[e.el,l,e,t]),tt())}}const Cl=Symbol("_vte"),Lo=e=>e.__isTeleport,ct=Symbol("_leaveCb"),vn=Symbol("_enterCb");function Rl(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Vo(()=>{e.isMounted=!0}),Ko(()=>{e.isUnmounting=!0}),e}const Oe=[Function,Array],No={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Oe,onEnter:Oe,onAfterEnter:Oe,onEnterCancelled:Oe,onBeforeLeave:Oe,onLeave:Oe,onAfterLeave:Oe,onLeaveCancelled:Oe,onBeforeAppear:Oe,onAppear:Oe,onAfterAppear:Oe,onAppearCancelled:Oe},Fo=e=>{const t=e.subTree;return t.component?Fo(t.component):t},Al={name:"BaseTransition",props:No,setup(e,{slots:t}){const n=Ac(),s=Rl();return()=>{const r=t.default&&Do(t.default(),!0);if(!r||!r.length)return;const o=$o(r),i=Y(e),{mode:l}=i;if(s.isLeaving)return Zn(o);const c=nr(o);if(!c)return Zn(o);let d=gs(c,i,s,n,a=>d=a);c.type!==be&&rn(c,d);let f=n.subTree&&nr(n.subTree);if(f&&f.type!==be&&!Et(c,f)&&Fo(n).type!==be){let a=gs(f,i,s,n);if(rn(f,a),l==="out-in"&&c.type!==be)return s.isLeaving=!0,a.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete a.afterLeave,f=void 0},Zn(o);l==="in-out"&&c.type!==be?a.delayLeave=(p,m,E)=>{const P=jo(s,f);P[String(f.key)]=f,p[ct]=()=>{m(),p[ct]=void 0,delete d.delayedLeave,f=void 0},d.delayedLeave=()=>{E(),delete d.delayedLeave,f=void 0}}:f=void 0}else f&&(f=void 0);return o}}};function $o(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==be){t=n;break}}return t}const Pl=Al;function jo(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function gs(e,t,n,s,r){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:c,onEnter:d,onAfterEnter:f,onEnterCancelled:a,onBeforeLeave:p,onLeave:m,onAfterLeave:E,onLeaveCancelled:P,onBeforeAppear:B,onAppear:F,onAfterAppear:L,onAppearCancelled:N}=t,I=String(e.key),k=jo(n,e),ne=(T,K)=>{T&&$e(T,s,9,K)},G=(T,K)=>{const Q=K[1];ne(T,K),H(T)?T.every(O=>O.length<=1)&&Q():T.length<=1&&Q()},W={mode:i,persisted:l,beforeEnter(T){let K=c;if(!n.isMounted)if(o)K=B||c;else return;T[ct]&&T[ct](!0);const Q=k[I];Q&&Et(e,Q)&&Q.el[ct]&&Q.el[ct](),ne(K,[T])},enter(T){let K=d,Q=f,O=a;if(!n.isMounted)if(o)K=F||d,Q=L||f,O=N||a;else return;let z=!1;const ae=T[vn]=ye=>{z||(z=!0,ye?ne(O,[T]):ne(Q,[T]),W.delayedLeave&&W.delayedLeave(),T[vn]=void 0)};K?G(K,[T,ae]):ae()},leave(T,K){const Q=String(e.key);if(T[vn]&&T[vn](!0),n.isUnmounting)return K();ne(p,[T]);let O=!1;const z=T[ct]=ae=>{O||(O=!0,K(),ae?ne(P,[T]):ne(E,[T]),T[ct]=void 0,k[Q]===e&&delete k[Q])};k[Q]=e,m?G(m,[T,z]):z()},clone(T){const K=gs(T,t,n,s,r);return r&&r(K),K}};return W}function Zn(e){if(Bn(e))return e=pt(e),e.children=null,e}function nr(e){if(!Bn(e))return Lo(e.type)&&e.children?$o(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&U(n.default))return n.default()}}function rn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,rn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Do(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===Ue?(i.patchFlag&128&&r++,s=s.concat(Do(i.children,t,l))):(t||i.type!==be)&&s.push(l!=null?pt(i,{key:l}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Ho(e,t){return U(e)?de({name:e.name},t,{setup:e}):e}function Bo(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Gt(e,t,n,s,r=!1){if(H(e)){e.forEach((E,P)=>Gt(E,t&&(H(t)?t[P]:t),n,s,r));return}if(zt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Gt(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Un(s.component):s.el,i=r?null:o,{i:l,r:c}=e,d=t&&t.r,f=l.refs===oe?l.refs={}:l.refs,a=l.setupState,p=Y(a),m=a===oe?()=>!1:E=>ee(p,E);if(d!=null&&d!==c&&(ue(d)?(f[d]=null,m(d)&&(a[d]=null)):fe(d)&&(d.value=null)),U(c))hn(c,l,12,[i,f]);else{const E=ue(c),P=fe(c);if(E||P){const B=()=>{if(e.f){const F=E?m(c)?a[c]:f[c]:c.value;r?H(F)&&Ts(F,o):H(F)?F.includes(o)||F.push(o):E?(f[c]=[o],m(c)&&(a[c]=f[c])):(c.value=[o],e.k&&(f[e.k]=c.value))}else E?(f[c]=i,m(c)&&(a[c]=i)):P&&(c.value=i,e.k&&(f[e.k]=i))};i?(B.id=-1,Ae(B,n)):B()}}}jn().requestIdleCallback;jn().cancelIdleCallback;const zt=e=>!!e.type.__asyncLoader,Bn=e=>e.type.__isKeepAlive;function Tl(e,t){ko(e,"a",t)}function Ol(e,t){ko(e,"da",t)}function ko(e,t,n=he){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(kn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Bn(r.parent.vnode)&&Ml(s,t,n,r),r=r.parent}}function Ml(e,t,n,s){const r=kn(t,e,s,!0);Uo(()=>{Ts(s[t],r)},n)}function kn(e,t,n=he,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{et();const l=dn(n),c=$e(t,n,e,i);return l(),tt(),c});return s?r.unshift(o):r.push(o),o}}const nt=e=>(t,n=he)=>{(!ln||e==="sp")&&kn(e,(...s)=>t(...s),n)},Il=nt("bm"),Vo=nt("m"),Ll=nt("bu"),Nl=nt("u"),Ko=nt("bum"),Uo=nt("um"),Fl=nt("sp"),$l=nt("rtg"),jl=nt("rtc");function Dl(e,t=he){kn("ec",e,t)}const Hl="components";function bu(e,t){return kl(Hl,e,!0,t)||e}const Bl=Symbol.for("v-ndc");function kl(e,t,n=!0,s=!1){const r=Se||he;if(r){const o=r.type;{const l=Ic(o,!1);if(l&&(l===t||l===Ne(t)||l===$n(Ne(t))))return o}const i=sr(r[e]||o[e],t)||sr(r.appContext[e],t);return!i&&s?o:i}}function sr(e,t){return e&&(e[t]||e[Ne(t)]||e[$n(Ne(t))])}function Su(e,t,n,s){let r;const o=n,i=H(e);if(i||ue(e)){const l=i&&ht(e);let c=!1,d=!1;l&&(c=!Ie(e),d=dt(e),e=Dn(e)),r=new Array(e.length);for(let f=0,a=e.length;f<a;f++)r[f]=t(c?d?Cn(pe(e[f])):pe(e[f]):e[f],f,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ce(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,d=l.length;c<d;c++){const f=l[c];r[c]=t(e[f],f,c,o)}}else r=[];return r}const ms=e=>e?ai(e)?Un(e):ms(e.parent):null,Jt=de(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ms(e.parent),$root:e=>ms(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>qo(e),$forceUpdate:e=>e.f||(e.f=()=>{Ks(e.update)}),$nextTick:e=>e.n||(e.n=Vs.bind(e.proxy)),$watch:e=>fc.bind(e)}),es=(e,t)=>e!==oe&&!e.__isScriptSetup&&ee(e,t),Vl={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(es(s,t))return i[t]=1,s[t];if(r!==oe&&ee(r,t))return i[t]=2,r[t];if((d=e.propsOptions[0])&&ee(d,t))return i[t]=3,o[t];if(n!==oe&&ee(n,t))return i[t]=4,n[t];ys&&(i[t]=0)}}const f=Jt[t];let a,p;if(f)return t==="$attrs"&&me(e.attrs,"get",""),f(e);if((a=l.__cssModules)&&(a=a[t]))return a;if(n!==oe&&ee(n,t))return i[t]=4,n[t];if(p=c.config.globalProperties,ee(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return es(r,t)?(r[t]=n,!0):s!==oe&&ee(s,t)?(s[t]=n,!0):ee(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==oe&&ee(e,i)||es(t,i)||(l=o[0])&&ee(l,i)||ee(s,i)||ee(Jt,i)||ee(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ee(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function rr(e){return H(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let ys=!0;function Kl(e){const t=qo(e),n=e.proxy,s=e.ctx;ys=!1,t.beforeCreate&&or(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:d,created:f,beforeMount:a,mounted:p,beforeUpdate:m,updated:E,activated:P,deactivated:B,beforeDestroy:F,beforeUnmount:L,destroyed:N,unmounted:I,render:k,renderTracked:ne,renderTriggered:G,errorCaptured:W,serverPrefetch:T,expose:K,inheritAttrs:Q,components:O,directives:z,filters:ae}=t;if(d&&Ul(d,s,null),i)for(const q in i){const X=i[q];U(X)&&(s[q]=X.bind(n))}if(r){const q=r.call(n,n);ce(q)&&(e.data=an(q))}if(ys=!0,o)for(const q in o){const X=o[q],Ge=U(X)?X.bind(n,n):U(X.get)?X.get.bind(n,n):qe,st=!U(X)&&U(X.set)?X.set.bind(n):qe,De=Me({get:Ge,set:st});Object.defineProperty(s,q,{enumerable:!0,configurable:!0,get:()=>De.value,set:Ee=>De.value=Ee})}if(l)for(const q in l)Wo(l[q],s,n,q);if(c){const q=U(c)?c.call(n):c;Reflect.ownKeys(q).forEach(X=>{bn(X,q[X])})}f&&or(f,e,"c");function se(q,X){H(X)?X.forEach(Ge=>q(Ge.bind(n))):X&&q(X.bind(n))}if(se(Il,a),se(Vo,p),se(Ll,m),se(Nl,E),se(Tl,P),se(Ol,B),se(Dl,W),se(jl,ne),se($l,G),se(Ko,L),se(Uo,I),se(Fl,T),H(K))if(K.length){const q=e.exposed||(e.exposed={});K.forEach(X=>{Object.defineProperty(q,X,{get:()=>n[X],set:Ge=>n[X]=Ge})})}else e.exposed||(e.exposed={});k&&e.render===qe&&(e.render=k),Q!=null&&(e.inheritAttrs=Q),O&&(e.components=O),z&&(e.directives=z),T&&Bo(e)}function Ul(e,t,n=qe){H(e)&&(e=vs(e));for(const s in e){const r=e[s];let o;ce(r)?"default"in r?o=Le(r.from||s,r.default,!0):o=Le(r.from||s):o=Le(r),fe(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function or(e,t,n){$e(H(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Wo(e,t,n,s){let r=s.includes(".")?oi(n,s):()=>n[s];if(ue(e)){const o=t[e];U(o)&&Qt(r,o)}else if(U(e))Qt(r,e.bind(n));else if(ce(e))if(H(e))e.forEach(o=>Wo(o,t,n,s));else{const o=U(e.handler)?e.handler.bind(n):t[e.handler];U(o)&&Qt(r,o,e)}}function qo(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(d=>Tn(c,d,i,!0)),Tn(c,t,i)),ce(t)&&o.set(t,c),c}function Tn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Tn(e,o,n,!0),r&&r.forEach(i=>Tn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=Wl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Wl={data:ir,props:lr,emits:lr,methods:Kt,computed:Kt,beforeCreate:ve,created:ve,beforeMount:ve,mounted:ve,beforeUpdate:ve,updated:ve,beforeDestroy:ve,beforeUnmount:ve,destroyed:ve,unmounted:ve,activated:ve,deactivated:ve,errorCaptured:ve,serverPrefetch:ve,components:Kt,directives:Kt,watch:Gl,provide:ir,inject:ql};function ir(e,t){return t?e?function(){return de(U(e)?e.call(this,this):e,U(t)?t.call(this,this):t)}:t:e}function ql(e,t){return Kt(vs(e),vs(t))}function vs(e){if(H(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ve(e,t){return e?[...new Set([].concat(e,t))]:t}function Kt(e,t){return e?de(Object.create(null),e,t):t}function lr(e,t){return e?H(e)&&H(t)?[...new Set([...e,...t])]:de(Object.create(null),rr(e),rr(t??{})):t}function Gl(e,t){if(!e)return t;if(!t)return e;const n=de(Object.create(null),e);for(const s in t)n[s]=ve(e[s],t[s]);return n}function Go(){return{app:null,config:{isNativeTag:Ii,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let zl=0;function Jl(e,t){return function(s,r=null){U(s)||(s=de({},s)),r!=null&&!ce(r)&&(r=null);const o=Go(),i=new WeakSet,l=[];let c=!1;const d=o.app={_uid:zl++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Nc,get config(){return o.config},set config(f){},use(f,...a){return i.has(f)||(f&&U(f.install)?(i.add(f),f.install(d,...a)):U(f)&&(i.add(f),f(d,...a))),d},mixin(f){return o.mixins.includes(f)||o.mixins.push(f),d},component(f,a){return a?(o.components[f]=a,d):o.components[f]},directive(f,a){return a?(o.directives[f]=a,d):o.directives[f]},mount(f,a,p){if(!c){const m=d._ceVNode||Ce(s,r);return m.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(m,f,p),c=!0,d._container=f,f.__vue_app__=d,Un(m.component)}},onUnmount(f){l.push(f)},unmount(){c&&($e(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(f,a){return o.provides[f]=a,d},runWithContext(f){const a=wt;wt=d;try{return f()}finally{wt=a}}};return d}}let wt=null;function bn(e,t){if(he){let n=he.provides;const s=he.parent&&he.parent.provides;s===n&&(n=he.provides=Object.create(s)),n[e]=t}}function Le(e,t,n=!1){const s=he||Se;if(s||wt){let r=wt?wt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&U(t)?t.call(s&&s.proxy):t}}function Ql(){return!!(he||Se||wt)}const zo={},Jo=()=>Object.create(zo),Qo=e=>Object.getPrototypeOf(e)===zo;function Yl(e,t,n,s=!1){const r={},o=Jo();e.propsDefaults=Object.create(null),Yo(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:wo(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Xl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=Y(r),[c]=e.propsOptions;let d=!1;if((s||i>0)&&!(i&16)){if(i&8){const f=e.vnode.dynamicProps;for(let a=0;a<f.length;a++){let p=f[a];if(Vn(e.emitsOptions,p))continue;const m=t[p];if(c)if(ee(o,p))m!==o[p]&&(o[p]=m,d=!0);else{const E=Ne(p);r[E]=_s(c,l,E,m,e,!1)}else m!==o[p]&&(o[p]=m,d=!0)}}}else{Yo(e,t,r,o)&&(d=!0);let f;for(const a in l)(!t||!ee(t,a)&&((f=mt(a))===a||!ee(t,f)))&&(c?n&&(n[a]!==void 0||n[f]!==void 0)&&(r[a]=_s(c,l,a,void 0,e,!0)):delete r[a]);if(o!==l)for(const a in o)(!t||!ee(t,a))&&(delete o[a],d=!0)}d&&Xe(e.attrs,"set","")}function Yo(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(Ut(c))continue;const d=t[c];let f;r&&ee(r,f=Ne(c))?!o||!o.includes(f)?n[f]=d:(l||(l={}))[f]=d:Vn(e.emitsOptions,c)||(!(c in s)||d!==s[c])&&(s[c]=d,i=!0)}if(o){const c=Y(n),d=l||oe;for(let f=0;f<o.length;f++){const a=o[f];n[a]=_s(r,c,a,d[a],e,!ee(d,a))}}return i}function _s(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=ee(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&U(c)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const f=dn(r);s=d[n]=c.call(null,t),f()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===mt(n))&&(s=!0))}return s}const Zl=new WeakMap;function Xo(e,t,n=!1){const s=n?Zl:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!U(e)){const f=a=>{c=!0;const[p,m]=Xo(a,t,!0);de(i,p),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!o&&!c)return ce(e)&&s.set(e,It),It;if(H(o))for(let f=0;f<o.length;f++){const a=Ne(o[f]);cr(a)&&(i[a]=oe)}else if(o)for(const f in o){const a=Ne(f);if(cr(a)){const p=o[f],m=i[a]=H(p)||U(p)?{type:p}:de({},p),E=m.type;let P=!1,B=!0;if(H(E))for(let F=0;F<E.length;++F){const L=E[F],N=U(L)&&L.name;if(N==="Boolean"){P=!0;break}else N==="String"&&(B=!1)}else P=U(E)&&E.name==="Boolean";m[0]=P,m[1]=B,(P||ee(m,"default"))&&l.push(a)}}const d=[i,l];return ce(e)&&s.set(e,d),d}function cr(e){return e[0]!=="$"&&!Ut(e)}const Us=e=>e[0]==="_"||e==="$stable",Ws=e=>H(e)?e.map(We):[We(e)],ec=(e,t,n)=>{if(t._n)return t;const s=wl((...r)=>Ws(t(...r)),n);return s._c=!1,s},Zo=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Us(r))continue;const o=e[r];if(U(o))t[r]=ec(r,o,s);else if(o!=null){const i=Ws(o);t[r]=()=>i}}},ei=(e,t)=>{const n=Ws(t);e.slots.default=()=>n},ti=(e,t,n)=>{for(const s in t)(n||!Us(s))&&(e[s]=t[s])},tc=(e,t,n)=>{const s=e.slots=Jo();if(e.vnode.shapeFlag&32){const r=t.__;r&&us(s,"__",r,!0);const o=t._;o?(ti(s,t,n),n&&us(s,"_",o,!0)):Zo(t,s)}else t&&ei(e,t)},nc=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=oe;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:ti(r,t,n):(o=!t.$stable,Zo(t,r)),i=t}else t&&(ei(e,t),i={default:1});if(o)for(const l in r)!Us(l)&&i[l]==null&&delete r[l]},Ae=mc;function sc(e){return rc(e)}function rc(e,t){const n=jn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:d,setElementText:f,parentNode:a,nextSibling:p,setScopeId:m=qe,insertStaticContent:E}=e,P=(u,h,g,y=null,b=null,_=null,C=void 0,w=null,x=!!h.dynamicChildren)=>{if(u===h)return;u&&!Et(u,h)&&(y=v(u),Ee(u,b,_,!0),u=null),h.patchFlag===-2&&(x=!1,h.dynamicChildren=null);const{type:S,ref:D,shapeFlag:A}=h;switch(S){case Kn:B(u,h,g,y);break;case be:F(u,h,g,y);break;case Sn:u==null&&L(h,g,y,C);break;case Ue:O(u,h,g,y,b,_,C,w,x);break;default:A&1?k(u,h,g,y,b,_,C,w,x):A&6?z(u,h,g,y,b,_,C,w,x):(A&64||A&128)&&S.process(u,h,g,y,b,_,C,w,x,$)}D!=null&&b?Gt(D,u&&u.ref,_,h||u,!h):D==null&&u&&u.ref!=null&&Gt(u.ref,null,_,u,!0)},B=(u,h,g,y)=>{if(u==null)s(h.el=l(h.children),g,y);else{const b=h.el=u.el;h.children!==u.children&&d(b,h.children)}},F=(u,h,g,y)=>{u==null?s(h.el=c(h.children||""),g,y):h.el=u.el},L=(u,h,g,y)=>{[u.el,u.anchor]=E(u.children,h,g,y,u.el,u.anchor)},N=({el:u,anchor:h},g,y)=>{let b;for(;u&&u!==h;)b=p(u),s(u,g,y),u=b;s(h,g,y)},I=({el:u,anchor:h})=>{let g;for(;u&&u!==h;)g=p(u),r(u),u=g;r(h)},k=(u,h,g,y,b,_,C,w,x)=>{h.type==="svg"?C="svg":h.type==="math"&&(C="mathml"),u==null?ne(h,g,y,b,_,C,w,x):T(u,h,b,_,C,w,x)},ne=(u,h,g,y,b,_,C,w)=>{let x,S;const{props:D,shapeFlag:A,transition:j,dirs:V}=u;if(x=u.el=i(u.type,_,D&&D.is,D),A&8?f(x,u.children):A&16&&W(u.children,x,null,y,b,ts(u,_),C,w),V&&yt(u,null,y,"created"),G(x,u,u.scopeId,C,y),D){for(const ie in D)ie!=="value"&&!Ut(ie)&&o(x,ie,null,D[ie],_,y);"value"in D&&o(x,"value",null,D.value,_),(S=D.onVnodeBeforeMount)&&Ve(S,y,u)}V&&yt(u,null,y,"beforeMount");const J=oc(b,j);J&&j.beforeEnter(x),s(x,h,g),((S=D&&D.onVnodeMounted)||J||V)&&Ae(()=>{S&&Ve(S,y,u),J&&j.enter(x),V&&yt(u,null,y,"mounted")},b)},G=(u,h,g,y,b)=>{if(g&&m(u,g),y)for(let _=0;_<y.length;_++)m(u,y[_]);if(b){let _=b.subTree;if(h===_||li(_.type)&&(_.ssContent===h||_.ssFallback===h)){const C=b.vnode;G(u,C,C.scopeId,C.slotScopeIds,b.parent)}}},W=(u,h,g,y,b,_,C,w,x=0)=>{for(let S=x;S<u.length;S++){const D=u[S]=w?ft(u[S]):We(u[S]);P(null,D,h,g,y,b,_,C,w)}},T=(u,h,g,y,b,_,C)=>{const w=h.el=u.el;let{patchFlag:x,dynamicChildren:S,dirs:D}=h;x|=u.patchFlag&16;const A=u.props||oe,j=h.props||oe;let V;if(g&&vt(g,!1),(V=j.onVnodeBeforeUpdate)&&Ve(V,g,h,u),D&&yt(h,u,g,"beforeUpdate"),g&&vt(g,!0),(A.innerHTML&&j.innerHTML==null||A.textContent&&j.textContent==null)&&f(w,""),S?K(u.dynamicChildren,S,w,g,y,ts(h,b),_):C||X(u,h,w,null,g,y,ts(h,b),_,!1),x>0){if(x&16)Q(w,A,j,g,b);else if(x&2&&A.class!==j.class&&o(w,"class",null,j.class,b),x&4&&o(w,"style",A.style,j.style,b),x&8){const J=h.dynamicProps;for(let ie=0;ie<J.length;ie++){const te=J[ie],xe=A[te],we=j[te];(we!==xe||te==="value")&&o(w,te,xe,we,b,g)}}x&1&&u.children!==h.children&&f(w,h.children)}else!C&&S==null&&Q(w,A,j,g,b);((V=j.onVnodeUpdated)||D)&&Ae(()=>{V&&Ve(V,g,h,u),D&&yt(h,u,g,"updated")},y)},K=(u,h,g,y,b,_,C)=>{for(let w=0;w<h.length;w++){const x=u[w],S=h[w],D=x.el&&(x.type===Ue||!Et(x,S)||x.shapeFlag&198)?a(x.el):g;P(x,S,D,null,y,b,_,C,!0)}},Q=(u,h,g,y,b)=>{if(h!==g){if(h!==oe)for(const _ in h)!Ut(_)&&!(_ in g)&&o(u,_,h[_],null,b,y);for(const _ in g){if(Ut(_))continue;const C=g[_],w=h[_];C!==w&&_!=="value"&&o(u,_,w,C,b,y)}"value"in g&&o(u,"value",h.value,g.value,b)}},O=(u,h,g,y,b,_,C,w,x)=>{const S=h.el=u?u.el:l(""),D=h.anchor=u?u.anchor:l("");let{patchFlag:A,dynamicChildren:j,slotScopeIds:V}=h;V&&(w=w?w.concat(V):V),u==null?(s(S,g,y),s(D,g,y),W(h.children||[],g,D,b,_,C,w,x)):A>0&&A&64&&j&&u.dynamicChildren?(K(u.dynamicChildren,j,g,b,_,C,w),(h.key!=null||b&&h===b.subTree)&&ni(u,h,!0)):X(u,h,g,D,b,_,C,w,x)},z=(u,h,g,y,b,_,C,w,x)=>{h.slotScopeIds=w,u==null?h.shapeFlag&512?b.ctx.activate(h,g,y,C,x):ae(h,g,y,b,_,C,x):ye(u,h,x)},ae=(u,h,g,y,b,_,C)=>{const w=u.component=Rc(u,y,b);if(Bn(u)&&(w.ctx.renderer=$),Pc(w,!1,C),w.asyncDep){if(b&&b.registerDep(w,se,C),!u.el){const x=w.subTree=Ce(be);F(null,x,h,g)}}else se(w,u,h,g,b,_,C)},ye=(u,h,g)=>{const y=h.component=u.component;if(pc(u,h,g))if(y.asyncDep&&!y.asyncResolved){q(y,h,g);return}else y.next=h,y.update();else h.el=u.el,y.vnode=h},se=(u,h,g,y,b,_,C)=>{const w=()=>{if(u.isMounted){let{next:A,bu:j,u:V,parent:J,vnode:ie}=u;{const Be=si(u);if(Be){A&&(A.el=ie.el,q(u,A,C)),Be.asyncDep.then(()=>{u.isUnmounted||w()});return}}let te=A,xe;vt(u,!1),A?(A.el=ie.el,q(u,A,C)):A=ie,j&&_n(j),(xe=A.props&&A.props.onVnodeBeforeUpdate)&&Ve(xe,J,A,ie),vt(u,!0);const we=ur(u),He=u.subTree;u.subTree=we,P(He,we,a(He.el),v(He),u,b,_),A.el=we.el,te===null&&gc(u,we.el),V&&Ae(V,b),(xe=A.props&&A.props.onVnodeUpdated)&&Ae(()=>Ve(xe,J,A,ie),b)}else{let A;const{el:j,props:V}=h,{bm:J,m:ie,parent:te,root:xe,type:we}=u,He=zt(h);vt(u,!1),J&&_n(J),!He&&(A=V&&V.onVnodeBeforeMount)&&Ve(A,te,h),vt(u,!0);{xe.ce&&xe.ce._def.shadowRoot!==!1&&xe.ce._injectChildStyle(we);const Be=u.subTree=ur(u);P(null,Be,g,y,u,b,_),h.el=Be.el}if(ie&&Ae(ie,b),!He&&(A=V&&V.onVnodeMounted)){const Be=h;Ae(()=>Ve(A,te,Be),b)}(h.shapeFlag&256||te&&zt(te.vnode)&&te.vnode.shapeFlag&256)&&u.a&&Ae(u.a,b),u.isMounted=!0,h=g=y=null}};u.scope.on();const x=u.effect=new fo(w);u.scope.off();const S=u.update=x.run.bind(x),D=u.job=x.runIfDirty.bind(x);D.i=u,D.id=u.uid,x.scheduler=()=>Ks(D),vt(u,!0),S()},q=(u,h,g)=>{h.component=u;const y=u.vnode.props;u.vnode=h,u.next=null,Xl(u,h.props,y,g),nc(u,h.children,g),et(),tr(u),tt()},X=(u,h,g,y,b,_,C,w,x=!1)=>{const S=u&&u.children,D=u?u.shapeFlag:0,A=h.children,{patchFlag:j,shapeFlag:V}=h;if(j>0){if(j&128){st(S,A,g,y,b,_,C,w,x);return}else if(j&256){Ge(S,A,g,y,b,_,C,w,x);return}}V&8?(D&16&&Te(S,b,_),A!==S&&f(g,A)):D&16?V&16?st(S,A,g,y,b,_,C,w,x):Te(S,b,_,!0):(D&8&&f(g,""),V&16&&W(A,g,y,b,_,C,w,x))},Ge=(u,h,g,y,b,_,C,w,x)=>{u=u||It,h=h||It;const S=u.length,D=h.length,A=Math.min(S,D);let j;for(j=0;j<A;j++){const V=h[j]=x?ft(h[j]):We(h[j]);P(u[j],V,g,null,b,_,C,w,x)}S>D?Te(u,b,_,!0,!1,A):W(h,g,y,b,_,C,w,x,A)},st=(u,h,g,y,b,_,C,w,x)=>{let S=0;const D=h.length;let A=u.length-1,j=D-1;for(;S<=A&&S<=j;){const V=u[S],J=h[S]=x?ft(h[S]):We(h[S]);if(Et(V,J))P(V,J,g,null,b,_,C,w,x);else break;S++}for(;S<=A&&S<=j;){const V=u[A],J=h[j]=x?ft(h[j]):We(h[j]);if(Et(V,J))P(V,J,g,null,b,_,C,w,x);else break;A--,j--}if(S>A){if(S<=j){const V=j+1,J=V<D?h[V].el:y;for(;S<=j;)P(null,h[S]=x?ft(h[S]):We(h[S]),g,J,b,_,C,w,x),S++}}else if(S>j)for(;S<=A;)Ee(u[S],b,_,!0),S++;else{const V=S,J=S,ie=new Map;for(S=J;S<=j;S++){const Re=h[S]=x?ft(h[S]):We(h[S]);Re.key!=null&&ie.set(Re.key,S)}let te,xe=0;const we=j-J+1;let He=!1,Be=0;const Dt=new Array(we);for(S=0;S<we;S++)Dt[S]=0;for(S=V;S<=A;S++){const Re=u[S];if(xe>=we){Ee(Re,b,_,!0);continue}let ke;if(Re.key!=null)ke=ie.get(Re.key);else for(te=J;te<=j;te++)if(Dt[te-J]===0&&Et(Re,h[te])){ke=te;break}ke===void 0?Ee(Re,b,_,!0):(Dt[ke-J]=S+1,ke>=Be?Be=ke:He=!0,P(Re,h[ke],g,null,b,_,C,w,x),xe++)}const Qs=He?ic(Dt):It;for(te=Qs.length-1,S=we-1;S>=0;S--){const Re=J+S,ke=h[Re],Ys=Re+1<D?h[Re+1].el:y;Dt[S]===0?P(null,ke,g,Ys,b,_,C,w,x):He&&(te<0||S!==Qs[te]?De(ke,g,Ys,2):te--)}}},De=(u,h,g,y,b=null)=>{const{el:_,type:C,transition:w,children:x,shapeFlag:S}=u;if(S&6){De(u.component.subTree,h,g,y);return}if(S&128){u.suspense.move(h,g,y);return}if(S&64){C.move(u,h,g,$);return}if(C===Ue){s(_,h,g);for(let A=0;A<x.length;A++)De(x[A],h,g,y);s(u.anchor,h,g);return}if(C===Sn){N(u,h,g);return}if(y!==2&&S&1&&w)if(y===0)w.beforeEnter(_),s(_,h,g),Ae(()=>w.enter(_),b);else{const{leave:A,delayLeave:j,afterLeave:V}=w,J=()=>{u.ctx.isUnmounted?r(_):s(_,h,g)},ie=()=>{A(_,()=>{J(),V&&V()})};j?j(_,J,ie):ie()}else s(_,h,g)},Ee=(u,h,g,y=!1,b=!1)=>{const{type:_,props:C,ref:w,children:x,dynamicChildren:S,shapeFlag:D,patchFlag:A,dirs:j,cacheIndex:V}=u;if(A===-2&&(b=!1),w!=null&&(et(),Gt(w,null,g,u,!0),tt()),V!=null&&(h.renderCache[V]=void 0),D&256){h.ctx.deactivate(u);return}const J=D&1&&j,ie=!zt(u);let te;if(ie&&(te=C&&C.onVnodeBeforeUnmount)&&Ve(te,h,u),D&6)pn(u.component,g,y);else{if(D&128){u.suspense.unmount(g,y);return}J&&yt(u,null,h,"beforeUnmount"),D&64?u.type.remove(u,h,g,$,y):S&&!S.hasOnce&&(_!==Ue||A>0&&A&64)?Te(S,h,g,!1,!0):(_===Ue&&A&384||!b&&D&16)&&Te(x,h,g),y&&Ct(u)}(ie&&(te=C&&C.onVnodeUnmounted)||J)&&Ae(()=>{te&&Ve(te,h,u),J&&yt(u,null,h,"unmounted")},g)},Ct=u=>{const{type:h,el:g,anchor:y,transition:b}=u;if(h===Ue){Rt(g,y);return}if(h===Sn){I(u);return}const _=()=>{r(g),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(u.shapeFlag&1&&b&&!b.persisted){const{leave:C,delayLeave:w}=b,x=()=>C(g,_);w?w(u.el,_,x):x()}else _()},Rt=(u,h)=>{let g;for(;u!==h;)g=p(u),r(u),u=g;r(h)},pn=(u,h,g)=>{const{bum:y,scope:b,job:_,subTree:C,um:w,m:x,a:S,parent:D,slots:{__:A}}=u;fr(x),fr(S),y&&_n(y),D&&H(A)&&A.forEach(j=>{D.renderCache[j]=void 0}),b.stop(),_&&(_.flags|=8,Ee(C,u,h,g)),w&&Ae(w,h),Ae(()=>{u.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Te=(u,h,g,y=!1,b=!1,_=0)=>{for(let C=_;C<u.length;C++)Ee(u[C],h,g,y,b)},v=u=>{if(u.shapeFlag&6)return v(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const h=p(u.anchor||u.el),g=h&&h[Cl];return g?p(g):h};let M=!1;const R=(u,h,g)=>{u==null?h._vnode&&Ee(h._vnode,null,null,!0):P(h._vnode||null,u,h,null,null,null,g),h._vnode=u,M||(M=!0,tr(),Oo(),M=!1)},$={p:P,um:Ee,m:De,r:Ct,mt:ae,mc:W,pc:X,pbc:K,n:v,o:e};return{render:R,hydrate:void 0,createApp:Jl(R)}}function ts({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function vt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function oc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ni(e,t,n=!1){const s=e.children,r=t.children;if(H(s)&&H(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=ft(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&ni(i,l)),l.type===Kn&&(l.el=i.el),l.type===be&&!l.el&&(l.el=i.el)}}function ic(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<d?o=l+1:i=l;d<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function si(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:si(t)}function fr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const lc=Symbol.for("v-scx"),cc=()=>Le(lc);function Qt(e,t,n){return ri(e,t,n)}function ri(e,t,n=oe){const{immediate:s,deep:r,flush:o,once:i}=n,l=de({},n),c=t&&s||!t&&o!=="post";let d;if(ln){if(o==="sync"){const m=cc();d=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=qe,m.resume=qe,m.pause=qe,m}}const f=he;l.call=(m,E,P)=>$e(m,f,E,P);let a=!1;o==="post"?l.scheduler=m=>{Ae(m,f&&f.suspense)}:o!=="sync"&&(a=!0,l.scheduler=(m,E)=>{E?m():Ks(m)}),l.augmentJob=m=>{t&&(m.flags|=4),a&&(m.flags|=2,f&&(m.id=f.uid,m.i=f))};const p=bl(e,t,l);return ln&&(d?d.push(p):c&&p()),p}function fc(e,t,n){const s=this.proxy,r=ue(e)?e.includes(".")?oi(s,e):()=>s[e]:e.bind(s,s);let o;U(t)?o=t:(o=t.handler,n=t);const i=dn(this),l=ri(r,o.bind(s),n);return i(),l}function oi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const uc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ne(t)}Modifiers`]||e[`${mt(t)}Modifiers`];function ac(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||oe;let r=n;const o=t.startsWith("update:"),i=o&&uc(s,t.slice(7));i&&(i.trim&&(r=n.map(f=>ue(f)?f.trim():f)),i.number&&(r=n.map(as)));let l,c=s[l=zn(t)]||s[l=zn(Ne(t))];!c&&o&&(c=s[l=zn(mt(t))]),c&&$e(c,e,6,r);const d=s[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,$e(d,e,6,r)}}function ii(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!U(e)){const c=d=>{const f=ii(d,t,!0);f&&(l=!0,de(i,f))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(ce(e)&&s.set(e,null),null):(H(o)?o.forEach(c=>i[c]=null):de(i,o),ce(e)&&s.set(e,i),i)}function Vn(e,t){return!e||!Ln(t)?!1:(t=t.slice(2).replace(/Once$/,""),ee(e,t[0].toLowerCase()+t.slice(1))||ee(e,mt(t))||ee(e,t))}function ur(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:d,renderCache:f,props:a,data:p,setupState:m,ctx:E,inheritAttrs:P}=e,B=Pn(e);let F,L;try{if(n.shapeFlag&4){const I=r||s,k=I;F=We(d.call(k,I,f,a,m,p,E)),L=l}else{const I=t;F=We(I.length>1?I(a,{attrs:l,slots:i,emit:c}):I(a,null)),L=t.props?l:hc(l)}}catch(I){Yt.length=0,Hn(I,e,1),F=Ce(be)}let N=F;if(L&&P!==!1){const I=Object.keys(L),{shapeFlag:k}=N;I.length&&k&7&&(o&&I.some(Ps)&&(L=dc(L,o)),N=pt(N,L,!1,!0))}return n.dirs&&(N=pt(N,null,!1,!0),N.dirs=N.dirs?N.dirs.concat(n.dirs):n.dirs),n.transition&&rn(N,n.transition),F=N,Pn(B),F}const hc=e=>{let t;for(const n in e)(n==="class"||n==="style"||Ln(n))&&((t||(t={}))[n]=e[n]);return t},dc=(e,t)=>{const n={};for(const s in e)(!Ps(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function pc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,d=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?ar(s,i,d):!!i;if(c&8){const f=t.dynamicProps;for(let a=0;a<f.length;a++){const p=f[a];if(i[p]!==s[p]&&!Vn(d,p))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?ar(s,i,d):!0:!!i;return!1}function ar(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Vn(n,o))return!0}return!1}function gc({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const li=e=>e.__isSuspense;function mc(e,t){t&&t.pendingBranch?H(e)?t.effects.push(...e):t.effects.push(e):xl(e)}const Ue=Symbol.for("v-fgt"),Kn=Symbol.for("v-txt"),be=Symbol.for("v-cmt"),Sn=Symbol.for("v-stc"),Yt=[];let Pe=null;function yc(e=!1){Yt.push(Pe=e?null:[])}function vc(){Yt.pop(),Pe=Yt[Yt.length-1]||null}let on=1;function hr(e,t=!1){on+=e,e<0&&Pe&&t&&(Pe.hasOnce=!0)}function ci(e){return e.dynamicChildren=on>0?Pe||It:null,vc(),on>0&&Pe&&Pe.push(e),e}function Eu(e,t,n,s,r,o){return ci(ui(e,t,n,s,r,o,!0))}function _c(e,t,n,s,r){return ci(Ce(e,t,n,s,r,!0))}function On(e){return e?e.__v_isVNode===!0:!1}function Et(e,t){return e.type===t.type&&e.key===t.key}const fi=({key:e})=>e??null,En=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ue(e)||fe(e)||U(e)?{i:Se,r:e,k:t,f:!!n}:e:null);function ui(e,t=null,n=null,s=0,r=null,o=e===Ue?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&fi(t),ref:t&&En(t),scopeId:Io,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Se};return l?(qs(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=ue(n)?8:16),on>0&&!i&&Pe&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Pe.push(c),c}const Ce=bc;function bc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Bl)&&(e=be),On(e)){const l=pt(e,t,!0);return n&&qs(l,n),on>0&&!o&&Pe&&(l.shapeFlag&6?Pe[Pe.indexOf(e)]=l:Pe.push(l)),l.patchFlag=-2,l}if(Lc(e)&&(e=e.__vccOpts),t){t=Sc(t);let{class:l,style:c}=t;l&&!ue(l)&&(t.class=Is(l)),ce(c)&&(Hs(c)&&!H(c)&&(c=de({},c)),t.style=Ms(c))}const i=ue(e)?1:li(e)?128:Lo(e)?64:ce(e)?4:U(e)?2:0;return ui(e,t,n,s,r,i,o,!0)}function Sc(e){return e?Hs(e)||Qo(e)?de({},e):e:null}function pt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,d=t?xc(r||{},t):r,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&fi(d),ref:t&&t.ref?n&&o?H(o)?o.concat(En(t)):[o,En(t)]:En(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ue?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&pt(e.ssContent),ssFallback:e.ssFallback&&pt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&rn(f,c.clone(f)),f}function Ec(e=" ",t=0){return Ce(Kn,null,e,t)}function xu(e,t){const n=Ce(Sn,null,e);return n.staticCount=t,n}function wu(e="",t=!1){return t?(yc(),_c(be,null,e)):Ce(be,null,e)}function We(e){return e==null||typeof e=="boolean"?Ce(be):H(e)?Ce(Ue,null,e.slice()):On(e)?ft(e):Ce(Kn,null,String(e))}function ft(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:pt(e)}function qs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(H(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),qs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Qo(t)?t._ctx=Se:r===3&&Se&&(Se.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else U(t)?(t={default:t,_ctx:Se},n=32):(t=String(t),s&64?(n=16,t=[Ec(t)]):n=8);e.children=t,e.shapeFlag|=n}function xc(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Is([t.class,s.class]));else if(r==="style")t.style=Ms([t.style,s.style]);else if(Ln(r)){const o=t[r],i=s[r];i&&o!==i&&!(H(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Ve(e,t,n,s=null){$e(e,t,7,[n,s])}const wc=Go();let Cc=0;function Rc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||wc,o={uid:Cc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new io(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Xo(s,r),emitsOptions:ii(s,r),emit:null,emitted:null,propsDefaults:oe,inheritAttrs:s.inheritAttrs,ctx:oe,data:oe,props:oe,attrs:oe,slots:oe,refs:oe,setupState:oe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ac.bind(null,o),e.ce&&e.ce(o),o}let he=null;const Ac=()=>he||Se;let Mn,bs;{const e=jn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Mn=t("__VUE_INSTANCE_SETTERS__",n=>he=n),bs=t("__VUE_SSR_SETTERS__",n=>ln=n)}const dn=e=>{const t=he;return Mn(e),e.scope.on(),()=>{e.scope.off(),Mn(t)}},dr=()=>{he&&he.scope.off(),Mn(null)};function ai(e){return e.vnode.shapeFlag&4}let ln=!1;function Pc(e,t=!1,n=!1){t&&bs(t);const{props:s,children:r}=e.vnode,o=ai(e);Yl(e,s,o,t),tc(e,r,n||t);const i=o?Tc(e,t):void 0;return t&&bs(!1),i}function Tc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Vl);const{setup:s}=n;if(s){et();const r=e.setupContext=s.length>1?Mc(e):null,o=dn(e),i=hn(s,e,0,[e.props,r]),l=eo(i);if(tt(),o(),(l||e.sp)&&!zt(e)&&Bo(e),l){if(i.then(dr,dr),t)return i.then(c=>{pr(e,c)}).catch(c=>{Hn(c,e,0)});e.asyncDep=i}else pr(e,i)}else hi(e)}function pr(e,t,n){U(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ce(t)&&(e.setupState=Ao(t)),hi(e)}function hi(e,t,n){const s=e.type;e.render||(e.render=s.render||qe);{const r=dn(e);et();try{Kl(e)}finally{tt(),r()}}}const Oc={get(e,t){return me(e,"get",""),e[t]}};function Mc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Oc),slots:e.slots,emit:e.emit,expose:t}}function Un(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Ao(Bs(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Jt)return Jt[n](e)},has(t,n){return n in t||n in Jt}})):e.proxy}function Ic(e,t=!0){return U(e)?e.displayName||e.name:e.name||t&&e.__name}function Lc(e){return U(e)&&"__vccOpts"in e}const Me=(e,t)=>vl(e,t,ln);function Gs(e,t,n){const s=arguments.length;return s===2?ce(t)&&!H(t)?On(t)?Ce(e,null,[t]):Ce(e,t):Ce(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&On(n)&&(n=[n]),Ce(e,t,n))}const Nc="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ss;const gr=typeof window<"u"&&window.trustedTypes;if(gr)try{Ss=gr.createPolicy("vue",{createHTML:e=>e})}catch{}const di=Ss?e=>Ss.createHTML(e):e=>e,Fc="http://www.w3.org/2000/svg",$c="http://www.w3.org/1998/Math/MathML",Ye=typeof document<"u"?document:null,mr=Ye&&Ye.createElement("template"),jc={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Ye.createElementNS(Fc,e):t==="mathml"?Ye.createElementNS($c,e):n?Ye.createElement(e,{is:n}):Ye.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Ye.createTextNode(e),createComment:e=>Ye.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ye.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{mr.innerHTML=di(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=mr.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},rt="transition",Bt="animation",cn=Symbol("_vtc"),pi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Dc=de({},No,pi),Hc=e=>(e.displayName="Transition",e.props=Dc,e),Cu=Hc((e,{slots:t})=>Gs(Pl,Bc(e),t)),_t=(e,t=[])=>{H(e)?e.forEach(n=>n(...t)):e&&e(...t)},yr=e=>e?H(e)?e.some(t=>t.length>1):e.length>1:!1;function Bc(e){const t={};for(const O in e)O in pi||(t[O]=e[O]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:d=i,appearToClass:f=l,leaveFromClass:a=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:m=`${n}-leave-to`}=e,E=kc(r),P=E&&E[0],B=E&&E[1],{onBeforeEnter:F,onEnter:L,onEnterCancelled:N,onLeave:I,onLeaveCancelled:k,onBeforeAppear:ne=F,onAppear:G=L,onAppearCancelled:W=N}=t,T=(O,z,ae,ye)=>{O._enterCancelled=ye,bt(O,z?f:l),bt(O,z?d:i),ae&&ae()},K=(O,z)=>{O._isLeaving=!1,bt(O,a),bt(O,m),bt(O,p),z&&z()},Q=O=>(z,ae)=>{const ye=O?G:L,se=()=>T(z,O,ae);_t(ye,[z,se]),vr(()=>{bt(z,O?c:o),Je(z,O?f:l),yr(ye)||_r(z,s,P,se)})};return de(t,{onBeforeEnter(O){_t(F,[O]),Je(O,o),Je(O,i)},onBeforeAppear(O){_t(ne,[O]),Je(O,c),Je(O,d)},onEnter:Q(!1),onAppear:Q(!0),onLeave(O,z){O._isLeaving=!0;const ae=()=>K(O,z);Je(O,a),O._enterCancelled?(Je(O,p),Er()):(Er(),Je(O,p)),vr(()=>{O._isLeaving&&(bt(O,a),Je(O,m),yr(I)||_r(O,s,B,ae))}),_t(I,[O,ae])},onEnterCancelled(O){T(O,!1,void 0,!0),_t(N,[O])},onAppearCancelled(O){T(O,!0,void 0,!0),_t(W,[O])},onLeaveCancelled(O){K(O),_t(k,[O])}})}function kc(e){if(e==null)return null;if(ce(e))return[ns(e.enter),ns(e.leave)];{const t=ns(e);return[t,t]}}function ns(e){return ji(e)}function Je(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[cn]||(e[cn]=new Set)).add(t)}function bt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[cn];n&&(n.delete(t),n.size||(e[cn]=void 0))}function vr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Vc=0;function _r(e,t,n,s){const r=e._endId=++Vc,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:l,propCount:c}=Kc(e,t);if(!i)return s();const d=i+"end";let f=0;const a=()=>{e.removeEventListener(d,p),o()},p=m=>{m.target===e&&++f>=c&&a()};setTimeout(()=>{f<c&&a()},l+1),e.addEventListener(d,p)}function Kc(e,t){const n=window.getComputedStyle(e),s=E=>(n[E]||"").split(", "),r=s(`${rt}Delay`),o=s(`${rt}Duration`),i=br(r,o),l=s(`${Bt}Delay`),c=s(`${Bt}Duration`),d=br(l,c);let f=null,a=0,p=0;t===rt?i>0&&(f=rt,a=i,p=o.length):t===Bt?d>0&&(f=Bt,a=d,p=c.length):(a=Math.max(i,d),f=a>0?i>d?rt:Bt:null,p=f?f===rt?o.length:c.length:0);const m=f===rt&&/\b(transform|all)(,|$)/.test(s(`${rt}Property`).toString());return{type:f,timeout:a,propCount:p,hasTransform:m}}function br(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Sr(n)+Sr(e[s])))}function Sr(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Er(){return document.body.offsetHeight}function Uc(e,t,n){const s=e[cn];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const In=Symbol("_vod"),gi=Symbol("_vsh"),Ru={beforeMount(e,{value:t},{transition:n}){e[In]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):kt(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),kt(e,!0),s.enter(e)):s.leave(e,()=>{kt(e,!1)}):kt(e,t))},beforeUnmount(e,{value:t}){kt(e,t)}};function kt(e,t){e.style.display=t?e[In]:"none",e[gi]=!t}const Wc=Symbol(""),qc=/(^|;)\s*display\s*:/;function Gc(e,t,n){const s=e.style,r=ue(n);let o=!1;if(n&&!r){if(t)if(ue(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&xn(s,l,"")}else for(const i in t)n[i]==null&&xn(s,i,"");for(const i in n)i==="display"&&(o=!0),xn(s,i,n[i])}else if(r){if(t!==n){const i=s[Wc];i&&(n+=";"+i),s.cssText=n,o=qc.test(n)}}else t&&e.removeAttribute("style");In in e&&(e[In]=o?s.display:"",e[gi]&&(s.display="none"))}const xr=/\s*!important$/;function xn(e,t,n){if(H(n))n.forEach(s=>xn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=zc(e,t);xr.test(n)?e.setProperty(mt(s),n.replace(xr,""),"important"):e[s]=n}}const wr=["Webkit","Moz","ms"],ss={};function zc(e,t){const n=ss[t];if(n)return n;let s=Ne(t);if(s!=="filter"&&s in e)return ss[t]=s;s=$n(s);for(let r=0;r<wr.length;r++){const o=wr[r]+s;if(o in e)return ss[t]=o}return t}const Cr="http://www.w3.org/1999/xlink";function Rr(e,t,n,s,r,o=Ki(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Cr,t.slice(6,t.length)):e.setAttributeNS(Cr,t,n):n==null||o&&!so(n)?e.removeAttribute(t):e.setAttribute(t,o?"":gt(n)?String(n):n)}function Ar(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?di(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=so(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Ot(e,t,n,s){e.addEventListener(t,n,s)}function Jc(e,t,n,s){e.removeEventListener(t,n,s)}const Pr=Symbol("_vei");function Qc(e,t,n,s,r=null){const o=e[Pr]||(e[Pr]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=Yc(t);if(s){const d=o[t]=ef(s,r);Ot(e,l,d,c)}else i&&(Jc(e,l,i,c),o[t]=void 0)}}const Tr=/(?:Once|Passive|Capture)$/;function Yc(e){let t;if(Tr.test(e)){t={};let s;for(;s=e.match(Tr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):mt(e.slice(2)),t]}let rs=0;const Xc=Promise.resolve(),Zc=()=>rs||(Xc.then(()=>rs=0),rs=Date.now());function ef(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;$e(tf(s,n.value),t,5,[s])};return n.value=e,n.attached=Zc(),n}function tf(e,t){if(H(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Or=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,nf=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Uc(e,s,i):t==="style"?Gc(e,n,s):Ln(t)?Ps(t)||Qc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):sf(e,t,s,i))?(Ar(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Rr(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ue(s))?Ar(e,Ne(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Rr(e,t,s,i))};function sf(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Or(t)&&U(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Or(t)&&ue(n)?!1:t in e}const Mr=e=>{const t=e.props["onUpdate:modelValue"]||!1;return H(t)?n=>_n(t,n):t};function rf(e){e.target.composing=!0}function Ir(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const os=Symbol("_assign"),Au={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[os]=Mr(r);const o=s||r.props&&r.props.type==="number";Ot(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=as(l)),e[os](l)}),n&&Ot(e,"change",()=>{e.value=e.value.trim()}),t||(Ot(e,"compositionstart",rf),Ot(e,"compositionend",Ir),Ot(e,"change",Ir))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[os]=Mr(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?as(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},of=["ctrl","shift","alt","meta"],lf={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>of.some(n=>e[`${n}Key`]&&!t.includes(n))},Pu=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=lf[t[i]];if(l&&l(r,t))return}return e(r,...o)})},cf={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Tu=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=mt(r.key);if(t.some(i=>i===o||cf[i]===o))return e(r)})},ff=de({patchProp:nf},jc);let Lr;function uf(){return Lr||(Lr=sc(ff))}const Ou=(...e)=>{const t=uf().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=hf(s);if(!r)return;const o=t._component;!U(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,af(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function af(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function hf(e){return ue(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let mi;const Wn=e=>mi=e,yi=Symbol();function Es(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Xt;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Xt||(Xt={}));function Mu(){const e=lo(!0),t=e.run(()=>ks({}));let n=[],s=[];const r=Bs({install(o){Wn(r),r._a=o,o.provide(yi,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const vi=()=>{};function Nr(e,t,n,s=vi){e.push(t);const r=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&co()&&Wi(r),r}function Pt(e,...t){e.slice().forEach(n=>{n(...t)})}const df=e=>e(),Fr=Symbol(),is=Symbol();function xs(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];Es(r)&&Es(s)&&e.hasOwnProperty(n)&&!fe(s)&&!ht(s)?e[n]=xs(r,s):e[n]=s}return e}const pf=Symbol();function gf(e){return!Es(e)||!e.hasOwnProperty(pf)}const{assign:it}=Object;function mf(e){return!!(fe(e)&&e.effect)}function yf(e,t,n,s){const{state:r,actions:o,getters:i}=t,l=n.state.value[e];let c;function d(){l||(n.state.value[e]=r?r():{});const f=pl(n.state.value[e]);return it(f,o,Object.keys(i||{}).reduce((a,p)=>(a[p]=Bs(Me(()=>{Wn(n);const m=n._s.get(e);return i[p].call(m,m)})),a),{}))}return c=_i(e,d,t,n,s,!0),c}function _i(e,t,n={},s,r,o){let i;const l=it({actions:{}},n),c={deep:!0};let d,f,a=[],p=[],m;const E=s.state.value[e];!o&&!E&&(s.state.value[e]={}),ks({});let P;function B(W){let T;d=f=!1,typeof W=="function"?(W(s.state.value[e]),T={type:Xt.patchFunction,storeId:e,events:m}):(xs(s.state.value[e],W),T={type:Xt.patchObject,payload:W,storeId:e,events:m});const K=P=Symbol();Vs().then(()=>{P===K&&(d=!0)}),f=!0,Pt(a,T,s.state.value[e])}const F=o?function(){const{state:T}=n,K=T?T():{};this.$patch(Q=>{it(Q,K)})}:vi;function L(){i.stop(),a=[],p=[],s._s.delete(e)}const N=(W,T="")=>{if(Fr in W)return W[is]=T,W;const K=function(){Wn(s);const Q=Array.from(arguments),O=[],z=[];function ae(q){O.push(q)}function ye(q){z.push(q)}Pt(p,{args:Q,name:K[is],store:k,after:ae,onError:ye});let se;try{se=W.apply(this&&this.$id===e?this:k,Q)}catch(q){throw Pt(z,q),q}return se instanceof Promise?se.then(q=>(Pt(O,q),q)).catch(q=>(Pt(z,q),Promise.reject(q))):(Pt(O,se),se)};return K[Fr]=!0,K[is]=T,K},I={_p:s,$id:e,$onAction:Nr.bind(null,p),$patch:B,$reset:F,$subscribe(W,T={}){const K=Nr(a,W,T.detached,()=>Q()),Q=i.run(()=>Qt(()=>s.state.value[e],O=>{(T.flush==="sync"?f:d)&&W({storeId:e,type:Xt.direct,events:m},O)},it({},c,T)));return K},$dispose:L},k=an(I);s._s.set(e,k);const G=(s._a&&s._a.runWithContext||df)(()=>s._e.run(()=>(i=lo()).run(()=>t({action:N}))));for(const W in G){const T=G[W];if(fe(T)&&!mf(T)||ht(T))o||(E&&gf(T)&&(fe(T)?T.value=E[W]:xs(T,E[W])),s.state.value[e][W]=T);else if(typeof T=="function"){const K=N(T,W);G[W]=K,l.actions[W]=T}}return it(k,G),it(Y(k),G),Object.defineProperty(k,"$state",{get:()=>s.state.value[e],set:W=>{B(T=>{it(T,W)})}}),s._p.forEach(W=>{it(k,i.run(()=>W({store:k,app:s._a,pinia:s,options:l})))}),E&&o&&n.hydrate&&n.hydrate(k.$state,E),d=!0,f=!0,k}/*! #__NO_SIDE_EFFECTS__ */function Iu(e,t,n){let s,r;const o=typeof t=="function";s=e,r=o?n:t;function i(l,c){const d=Ql();return l=l||(d?Le(yi,null):null),l&&Wn(l),l=mi,l._s.has(s)||(o?_i(s,t,r,l):yf(s,r,l)),l._s.get(s)}return i.$id=s,i}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Mt=typeof document<"u";function bi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function vf(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&bi(e.default)}const Z=Object.assign;function ls(e,t){const n={};for(const s in t){const r=t[s];n[s]=je(r)?r.map(e):e(r)}return n}const Zt=()=>{},je=Array.isArray,Si=/#/g,_f=/&/g,bf=/\//g,Sf=/=/g,Ef=/\?/g,Ei=/\+/g,xf=/%5B/g,wf=/%5D/g,xi=/%5E/g,Cf=/%60/g,wi=/%7B/g,Rf=/%7C/g,Ci=/%7D/g,Af=/%20/g;function zs(e){return encodeURI(""+e).replace(Rf,"|").replace(xf,"[").replace(wf,"]")}function Pf(e){return zs(e).replace(wi,"{").replace(Ci,"}").replace(xi,"^")}function ws(e){return zs(e).replace(Ei,"%2B").replace(Af,"+").replace(Si,"%23").replace(_f,"%26").replace(Cf,"`").replace(wi,"{").replace(Ci,"}").replace(xi,"^")}function Tf(e){return ws(e).replace(Sf,"%3D")}function Of(e){return zs(e).replace(Si,"%23").replace(Ef,"%3F")}function Mf(e){return e==null?"":Of(e).replace(bf,"%2F")}function fn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const If=/\/$/,Lf=e=>e.replace(If,"");function cs(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=jf(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:fn(i)}}function Nf(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function $r(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ff(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&$t(t.matched[s],n.matched[r])&&Ri(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function $t(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ri(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!$f(e[n],t[n]))return!1;return!0}function $f(e,t){return je(e)?jr(e,t):je(t)?jr(t,e):e===t}function jr(e,t){return je(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function jf(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const ot={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var un;(function(e){e.pop="pop",e.push="push"})(un||(un={}));var en;(function(e){e.back="back",e.forward="forward",e.unknown=""})(en||(en={}));function Df(e){if(!e)if(Mt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Lf(e)}const Hf=/^[^#]+#/;function Bf(e,t){return e.replace(Hf,"#")+t}function kf(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const qn=()=>({left:window.scrollX,top:window.scrollY});function Vf(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=kf(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Dr(e,t){return(history.state?history.state.position-t:-1)+e}const Cs=new Map;function Kf(e,t){Cs.set(e,t)}function Uf(e){const t=Cs.get(e);return Cs.delete(e),t}let Wf=()=>location.protocol+"//"+location.host;function Ai(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),$r(c,"")}return $r(n,e)+s+r}function qf(e,t,n,s){let r=[],o=[],i=null;const l=({state:p})=>{const m=Ai(e,location),E=n.value,P=t.value;let B=0;if(p){if(n.value=m,t.value=p,i&&i===E){i=null;return}B=P?p.position-P.position:0}else s(m);r.forEach(F=>{F(n.value,E,{delta:B,type:un.pop,direction:B?B>0?en.forward:en.back:en.unknown})})};function c(){i=n.value}function d(p){r.push(p);const m=()=>{const E=r.indexOf(p);E>-1&&r.splice(E,1)};return o.push(m),m}function f(){const{history:p}=window;p.state&&p.replaceState(Z({},p.state,{scroll:qn()}),"")}function a(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",f)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",f,{passive:!0}),{pauseListeners:c,listen:d,destroy:a}}function Hr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?qn():null}}function Gf(e){const{history:t,location:n}=window,s={value:Ai(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,d,f){const a=e.indexOf("#"),p=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+c:Wf()+e+c;try{t[f?"replaceState":"pushState"](d,"",p),r.value=d}catch(m){console.error(m),n[f?"replace":"assign"](p)}}function i(c,d){const f=Z({},t.state,Hr(r.value.back,c,r.value.forward,!0),d,{position:r.value.position});o(c,f,!0),s.value=c}function l(c,d){const f=Z({},r.value,t.state,{forward:c,scroll:qn()});o(f.current,f,!0);const a=Z({},Hr(s.value,c,null),{position:f.position+1},d);o(c,a,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function Lu(e){e=Df(e);const t=Gf(e),n=qf(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=Z({location:"",base:e,go:s,createHref:Bf.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function zf(e){return typeof e=="string"||e&&typeof e=="object"}function Pi(e){return typeof e=="string"||typeof e=="symbol"}const Ti=Symbol("");var Br;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Br||(Br={}));function jt(e,t){return Z(new Error,{type:e,[Ti]:!0},t)}function Qe(e,t){return e instanceof Error&&Ti in e&&(t==null||!!(e.type&t))}const kr="[^/]+?",Jf={sensitive:!1,strict:!1,start:!0,end:!0},Qf=/[.+*?^${}()[\]/\\]/g;function Yf(e,t){const n=Z({},Jf,t),s=[];let r=n.start?"^":"";const o=[];for(const d of e){const f=d.length?[]:[90];n.strict&&!d.length&&(r+="/");for(let a=0;a<d.length;a++){const p=d[a];let m=40+(n.sensitive?.25:0);if(p.type===0)a||(r+="/"),r+=p.value.replace(Qf,"\\$&"),m+=40;else if(p.type===1){const{value:E,repeatable:P,optional:B,regexp:F}=p;o.push({name:E,repeatable:P,optional:B});const L=F||kr;if(L!==kr){m+=10;try{new RegExp(`(${L})`)}catch(I){throw new Error(`Invalid custom RegExp for param "${E}" (${L}): `+I.message)}}let N=P?`((?:${L})(?:/(?:${L}))*)`:`(${L})`;a||(N=B&&d.length<2?`(?:/${N})`:"/"+N),B&&(N+="?"),r+=N,m+=20,B&&(m+=-8),P&&(m+=-20),L===".*"&&(m+=-50)}f.push(m)}s.push(f)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(d){const f=d.match(i),a={};if(!f)return null;for(let p=1;p<f.length;p++){const m=f[p]||"",E=o[p-1];a[E.name]=m&&E.repeatable?m.split("/"):m}return a}function c(d){let f="",a=!1;for(const p of e){(!a||!f.endsWith("/"))&&(f+="/"),a=!1;for(const m of p)if(m.type===0)f+=m.value;else if(m.type===1){const{value:E,repeatable:P,optional:B}=m,F=E in d?d[E]:"";if(je(F)&&!P)throw new Error(`Provided param "${E}" is an array but it is not repeatable (* or + modifiers)`);const L=je(F)?F.join("/"):F;if(!L)if(B)p.length<2&&(f.endsWith("/")?f=f.slice(0,-1):a=!0);else throw new Error(`Missing required param "${E}"`);f+=L}}return f||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Xf(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Oi(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Xf(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Vr(s))return 1;if(Vr(r))return-1}return r.length-s.length}function Vr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Zf={type:0,value:""},eu=/[a-zA-Z0-9_]/;function tu(e){if(!e)return[[]];if(e==="/")return[[Zf]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,d="",f="";function a(){d&&(n===0?o.push({type:0,value:d}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:d,regexp:f,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function p(){d+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(d&&a(),i()):c===":"?(a(),n=1):p();break;case 4:p(),n=s;break;case 1:c==="("?n=2:eu.test(c)?p():(a(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?f[f.length-1]=="\\"?f=f.slice(0,-1)+c:n=3:f+=c;break;case 3:a(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,f="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),a(),i(),r}function nu(e,t,n){const s=Yf(tu(e.path),n),r=Z(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function su(e,t){const n=[],s=new Map;t=qr({strict:!1,end:!0,sensitive:!1},t);function r(a){return s.get(a)}function o(a,p,m){const E=!m,P=Ur(a);P.aliasOf=m&&m.record;const B=qr(t,a),F=[P];if("alias"in a){const I=typeof a.alias=="string"?[a.alias]:a.alias;for(const k of I)F.push(Ur(Z({},P,{components:m?m.record.components:P.components,path:k,aliasOf:m?m.record:P})))}let L,N;for(const I of F){const{path:k}=I;if(p&&k[0]!=="/"){const ne=p.record.path,G=ne[ne.length-1]==="/"?"":"/";I.path=p.record.path+(k&&G+k)}if(L=nu(I,p,B),m?m.alias.push(L):(N=N||L,N!==L&&N.alias.push(L),E&&a.name&&!Wr(L)&&i(a.name)),Mi(L)&&c(L),P.children){const ne=P.children;for(let G=0;G<ne.length;G++)o(ne[G],L,m&&m.children[G])}m=m||L}return N?()=>{i(N)}:Zt}function i(a){if(Pi(a)){const p=s.get(a);p&&(s.delete(a),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(a);p>-1&&(n.splice(p,1),a.record.name&&s.delete(a.record.name),a.children.forEach(i),a.alias.forEach(i))}}function l(){return n}function c(a){const p=iu(a,n);n.splice(p,0,a),a.record.name&&!Wr(a)&&s.set(a.record.name,a)}function d(a,p){let m,E={},P,B;if("name"in a&&a.name){if(m=s.get(a.name),!m)throw jt(1,{location:a});B=m.record.name,E=Z(Kr(p.params,m.keys.filter(N=>!N.optional).concat(m.parent?m.parent.keys.filter(N=>N.optional):[]).map(N=>N.name)),a.params&&Kr(a.params,m.keys.map(N=>N.name))),P=m.stringify(E)}else if(a.path!=null)P=a.path,m=n.find(N=>N.re.test(P)),m&&(E=m.parse(P),B=m.record.name);else{if(m=p.name?s.get(p.name):n.find(N=>N.re.test(p.path)),!m)throw jt(1,{location:a,currentLocation:p});B=m.record.name,E=Z({},p.params,a.params),P=m.stringify(E)}const F=[];let L=m;for(;L;)F.unshift(L.record),L=L.parent;return{name:B,path:P,params:E,matched:F,meta:ou(F)}}e.forEach(a=>o(a));function f(){n.length=0,s.clear()}return{addRoute:o,resolve:d,removeRoute:i,clearRoutes:f,getRoutes:l,getRecordMatcher:r}}function Kr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Ur(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:ru(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function ru(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Wr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ou(e){return e.reduce((t,n)=>Z(t,n.meta),{})}function qr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function iu(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Oi(e,t[o])<0?s=o:n=o+1}const r=lu(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function lu(e){let t=e;for(;t=t.parent;)if(Mi(t)&&Oi(e,t)===0)return t}function Mi({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function cu(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Ei," "),i=o.indexOf("="),l=fn(i<0?o:o.slice(0,i)),c=i<0?null:fn(o.slice(i+1));if(l in t){let d=t[l];je(d)||(d=t[l]=[d]),d.push(c)}else t[l]=c}return t}function Gr(e){let t="";for(let n in e){const s=e[n];if(n=Tf(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(je(s)?s.map(o=>o&&ws(o)):[s&&ws(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function fu(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=je(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const uu=Symbol(""),zr=Symbol(""),Gn=Symbol(""),Js=Symbol(""),Rs=Symbol("");function Vt(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function ut(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const d=p=>{p===!1?c(jt(4,{from:n,to:t})):p instanceof Error?c(p):zf(p)?c(jt(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),l())},f=o(()=>e.call(s&&s.instances[r],t,n,d));let a=Promise.resolve(f);e.length<3&&(a=a.then(d)),a.catch(p=>c(p))})}function fs(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(bi(c)){const f=(c.__vccOpts||c)[t];f&&o.push(ut(f,n,s,i,l,r))}else{let d=c();o.push(()=>d.then(f=>{if(!f)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const a=vf(f)?f.default:f;i.mods[l]=f,i.components[l]=a;const m=(a.__vccOpts||a)[t];return m&&ut(m,n,s,i,l,r)()}))}}return o}function Jr(e){const t=Le(Gn),n=Le(Js),s=Me(()=>{const c=Nt(e.to);return t.resolve(c)}),r=Me(()=>{const{matched:c}=s.value,{length:d}=c,f=c[d-1],a=n.matched;if(!f||!a.length)return-1;const p=a.findIndex($t.bind(null,f));if(p>-1)return p;const m=Qr(c[d-2]);return d>1&&Qr(f)===m&&a[a.length-1].path!==m?a.findIndex($t.bind(null,c[d-2])):p}),o=Me(()=>r.value>-1&&gu(n.params,s.value.params)),i=Me(()=>r.value>-1&&r.value===n.matched.length-1&&Ri(n.params,s.value.params));function l(c={}){if(pu(c)){const d=t[Nt(e.replace)?"replace":"push"](Nt(e.to)).catch(Zt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:Me(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function au(e){return e.length===1?e[0]:e}const hu=Ho({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Jr,setup(e,{slots:t}){const n=an(Jr(e)),{options:s}=Le(Gn),r=Me(()=>({[Yr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Yr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&au(t.default(n));return e.custom?o:Gs("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),du=hu;function pu(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function gu(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!je(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Qr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Yr=(e,t,n)=>e??t??n,mu=Ho({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Le(Rs),r=Me(()=>e.route||s.value),o=Le(zr,0),i=Me(()=>{let d=Nt(o);const{matched:f}=r.value;let a;for(;(a=f[d])&&!a.components;)d++;return d}),l=Me(()=>r.value.matched[i.value]);bn(zr,Me(()=>i.value+1)),bn(uu,l),bn(Rs,r);const c=ks();return Qt(()=>[c.value,l.value,e.name],([d,f,a],[p,m,E])=>{f&&(f.instances[a]=d,m&&m!==f&&d&&d===p&&(f.leaveGuards.size||(f.leaveGuards=m.leaveGuards),f.updateGuards.size||(f.updateGuards=m.updateGuards))),d&&f&&(!m||!$t(f,m)||!p)&&(f.enterCallbacks[a]||[]).forEach(P=>P(d))},{flush:"post"}),()=>{const d=r.value,f=e.name,a=l.value,p=a&&a.components[f];if(!p)return Xr(n.default,{Component:p,route:d});const m=a.props[f],E=m?m===!0?d.params:typeof m=="function"?m(d):m:null,B=Gs(p,Z({},E,t,{onVnodeUnmounted:F=>{F.component.isUnmounted&&(a.instances[f]=null)},ref:c}));return Xr(n.default,{Component:B,route:d})||B}}});function Xr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const yu=mu;function Nu(e){const t=su(e.routes,e),n=e.parseQuery||cu,s=e.stringifyQuery||Gr,r=e.history,o=Vt(),i=Vt(),l=Vt(),c=al(ot);let d=ot;Mt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const f=ls.bind(null,v=>""+v),a=ls.bind(null,Mf),p=ls.bind(null,fn);function m(v,M){let R,$;return Pi(v)?(R=t.getRecordMatcher(v),$=M):$=v,t.addRoute($,R)}function E(v){const M=t.getRecordMatcher(v);M&&t.removeRoute(M)}function P(){return t.getRoutes().map(v=>v.record)}function B(v){return!!t.getRecordMatcher(v)}function F(v,M){if(M=Z({},M||c.value),typeof v=="string"){const g=cs(n,v,M.path),y=t.resolve({path:g.path},M),b=r.createHref(g.fullPath);return Z(g,y,{params:p(y.params),hash:fn(g.hash),redirectedFrom:void 0,href:b})}let R;if(v.path!=null)R=Z({},v,{path:cs(n,v.path,M.path).path});else{const g=Z({},v.params);for(const y in g)g[y]==null&&delete g[y];R=Z({},v,{params:a(g)}),M.params=a(M.params)}const $=t.resolve(R,M),re=v.hash||"";$.params=f(p($.params));const u=Nf(s,Z({},v,{hash:Pf(re),path:$.path})),h=r.createHref(u);return Z({fullPath:u,hash:re,query:s===Gr?fu(v.query):v.query||{}},$,{redirectedFrom:void 0,href:h})}function L(v){return typeof v=="string"?cs(n,v,c.value.path):Z({},v)}function N(v,M){if(d!==v)return jt(8,{from:M,to:v})}function I(v){return G(v)}function k(v){return I(Z(L(v),{replace:!0}))}function ne(v){const M=v.matched[v.matched.length-1];if(M&&M.redirect){const{redirect:R}=M;let $=typeof R=="function"?R(v):R;return typeof $=="string"&&($=$.includes("?")||$.includes("#")?$=L($):{path:$},$.params={}),Z({query:v.query,hash:v.hash,params:$.path!=null?{}:v.params},$)}}function G(v,M){const R=d=F(v),$=c.value,re=v.state,u=v.force,h=v.replace===!0,g=ne(R);if(g)return G(Z(L(g),{state:typeof g=="object"?Z({},re,g.state):re,force:u,replace:h}),M||R);const y=R;y.redirectedFrom=M;let b;return!u&&Ff(s,$,R)&&(b=jt(16,{to:y,from:$}),De($,$,!0,!1)),(b?Promise.resolve(b):K(y,$)).catch(_=>Qe(_)?Qe(_,2)?_:st(_):X(_,y,$)).then(_=>{if(_){if(Qe(_,2))return G(Z({replace:h},L(_.to),{state:typeof _.to=="object"?Z({},re,_.to.state):re,force:u}),M||y)}else _=O(y,$,!0,h,re);return Q(y,$,_),_})}function W(v,M){const R=N(v,M);return R?Promise.reject(R):Promise.resolve()}function T(v){const M=Rt.values().next().value;return M&&typeof M.runWithContext=="function"?M.runWithContext(v):v()}function K(v,M){let R;const[$,re,u]=vu(v,M);R=fs($.reverse(),"beforeRouteLeave",v,M);for(const g of $)g.leaveGuards.forEach(y=>{R.push(ut(y,v,M))});const h=W.bind(null,v,M);return R.push(h),Te(R).then(()=>{R=[];for(const g of o.list())R.push(ut(g,v,M));return R.push(h),Te(R)}).then(()=>{R=fs(re,"beforeRouteUpdate",v,M);for(const g of re)g.updateGuards.forEach(y=>{R.push(ut(y,v,M))});return R.push(h),Te(R)}).then(()=>{R=[];for(const g of u)if(g.beforeEnter)if(je(g.beforeEnter))for(const y of g.beforeEnter)R.push(ut(y,v,M));else R.push(ut(g.beforeEnter,v,M));return R.push(h),Te(R)}).then(()=>(v.matched.forEach(g=>g.enterCallbacks={}),R=fs(u,"beforeRouteEnter",v,M,T),R.push(h),Te(R))).then(()=>{R=[];for(const g of i.list())R.push(ut(g,v,M));return R.push(h),Te(R)}).catch(g=>Qe(g,8)?g:Promise.reject(g))}function Q(v,M,R){l.list().forEach($=>T(()=>$(v,M,R)))}function O(v,M,R,$,re){const u=N(v,M);if(u)return u;const h=M===ot,g=Mt?history.state:{};R&&($||h?r.replace(v.fullPath,Z({scroll:h&&g&&g.scroll},re)):r.push(v.fullPath,re)),c.value=v,De(v,M,R,h),st()}let z;function ae(){z||(z=r.listen((v,M,R)=>{if(!pn.listening)return;const $=F(v),re=ne($);if(re){G(Z(re,{replace:!0,force:!0}),$).catch(Zt);return}d=$;const u=c.value;Mt&&Kf(Dr(u.fullPath,R.delta),qn()),K($,u).catch(h=>Qe(h,12)?h:Qe(h,2)?(G(Z(L(h.to),{force:!0}),$).then(g=>{Qe(g,20)&&!R.delta&&R.type===un.pop&&r.go(-1,!1)}).catch(Zt),Promise.reject()):(R.delta&&r.go(-R.delta,!1),X(h,$,u))).then(h=>{h=h||O($,u,!1),h&&(R.delta&&!Qe(h,8)?r.go(-R.delta,!1):R.type===un.pop&&Qe(h,20)&&r.go(-1,!1)),Q($,u,h)}).catch(Zt)}))}let ye=Vt(),se=Vt(),q;function X(v,M,R){st(v);const $=se.list();return $.length?$.forEach(re=>re(v,M,R)):console.error(v),Promise.reject(v)}function Ge(){return q&&c.value!==ot?Promise.resolve():new Promise((v,M)=>{ye.add([v,M])})}function st(v){return q||(q=!v,ae(),ye.list().forEach(([M,R])=>v?R(v):M()),ye.reset()),v}function De(v,M,R,$){const{scrollBehavior:re}=e;if(!Mt||!re)return Promise.resolve();const u=!R&&Uf(Dr(v.fullPath,0))||($||!R)&&history.state&&history.state.scroll||null;return Vs().then(()=>re(v,M,u)).then(h=>h&&Vf(h)).catch(h=>X(h,v,M))}const Ee=v=>r.go(v);let Ct;const Rt=new Set,pn={currentRoute:c,listening:!0,addRoute:m,removeRoute:E,clearRoutes:t.clearRoutes,hasRoute:B,getRoutes:P,resolve:F,options:e,push:I,replace:k,go:Ee,back:()=>Ee(-1),forward:()=>Ee(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:se.add,isReady:Ge,install(v){const M=this;v.component("RouterLink",du),v.component("RouterView",yu),v.config.globalProperties.$router=M,Object.defineProperty(v.config.globalProperties,"$route",{enumerable:!0,get:()=>Nt(c)}),Mt&&!Ct&&c.value===ot&&(Ct=!0,I(r.location).catch(re=>{}));const R={};for(const re in ot)Object.defineProperty(R,re,{get:()=>c.value[re],enumerable:!0});v.provide(Gn,M),v.provide(Js,wo(R)),v.provide(Rs,c);const $=v.unmount;Rt.add(v),v.unmount=function(){Rt.delete(v),Rt.size<1&&(d=ot,z&&z(),z=null,c.value=ot,Ct=!1,q=!1),$()}}};function Te(v){return v.reduce((M,R)=>M.then(()=>T(R)),Promise.resolve())}return pn}function vu(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(d=>$t(d,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(d=>$t(d,c))||r.push(c))}return[n,s,r]}function Fu(){return Le(Gn)}function $u(e){return Le(Js)}export{Ms as A,Uo as B,Nu as C,Lu as D,Ko as E,Ue as F,Ou as G,Mu as H,Cu as T,ui as a,xu as b,Eu as c,Iu as d,Ce as e,bu as f,ks as g,yc as h,Ec as i,_u as j,Vs as k,Me as l,wu as m,Is as n,Vo as o,Qt as p,_c as q,Su as r,Pu as s,Ui as t,Fu as u,Ru as v,wl as w,Au as x,Tu as y,$u as z};
