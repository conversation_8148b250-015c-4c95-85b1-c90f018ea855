package com.youkate.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 公司信息实体类
 */
@Entity
@Table(name = "company_info")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyInfo {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "公司名称不能为空")
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description; // 公司描述
    
    @Column(length = 200)
    private String address; // 公司地址
    
    @Column(length = 50)
    private String phone; // 联系电话
    
    @Column(length = 100)
    private String email; // 邮箱
    
    @Column(length = 100)
    private String website; // 官网
    
    @Column(name = "business_hours", length = 100)
    private String businessHours; // 营业时间
    
    @Column(columnDefinition = "TEXT")
    private String mission; // 企业使命
    
    @Column(columnDefinition = "TEXT")
    private String vision; // 企业愿景
    
    @Column(name = "company_values", columnDefinition = "TEXT")
    private String values; // 企业价值观
    
    @Column(name = "founded_year")
    private Integer foundedYear; // 成立年份
    
    @Column(name = "employee_count")
    private Integer employeeCount; // 员工数量
    
    @Column(name = "logo_url", length = 500)
    private String logoUrl; // Logo URL
    
    @Column(name = "coordinates", length = 100)
    private String coordinates; // 地理坐标
    
    @Column(name = "social_media", columnDefinition = "TEXT")
    private String socialMedia; // JSON格式存储社交媒体信息
    
    @Column(name = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
