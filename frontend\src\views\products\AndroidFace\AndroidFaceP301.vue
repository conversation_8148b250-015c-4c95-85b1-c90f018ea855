<template>
  <div>
    <!-- 页面头部横幅 -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white pt-24 pb-8 relative overflow-hidden">
      <div class="absolute inset-0 bg-black opacity-10"></div>
      <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-4" data-aos="fade-up">P301-2D-2W 台式人脸消费机</h1>
          <p class="text-xl md:text-2xl" data-aos="fade-up" data-aos-delay="200">智能识别，安全便捷</p>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="absolute top-10 right-10 w-20 h-20 bg-white opacity-10 rounded-full animate-bounce"></div>
      <div class="absolute bottom-10 left-10 w-16 h-16 bg-white opacity-10 rounded-full animate-bounce" style="animation-delay: 1s;"></div>
    </section>

    <!-- 产品详情主体 -->
    <section class="pt-20 pb-0 bg-white">
      <div class="w-full">
        <!-- 重新设计的布局：左侧导航 + 右侧2x2网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <!-- 左侧分类导航 -->
          <div class="lg:col-span-1 lg:pl-4 xl:pl-8 2xl:pl-16">
            <div class="lg:ml-4 xl:ml-8 2xl:ml-16" style="margin-top: -1.5rem;">
              <ProductNavigation
                :current-category="currentCategory"
                :current-product="currentProduct"
              />
            </div>
          </div>

          <!-- 右侧内容区域：向右移动，增加右边距 -->
          <div class="lg:col-span-3 container mx-auto px-4 lg:px-0 lg:pr-0 xl:pr-0 2xl:pr-0 lg:ml-8 xl:ml-12 2xl:ml-16" ref="rightContentRef" style="position: relative;">
            <!-- 产品展示区域：缩略图 + 50%/50%布局（产品大图 + 产品简介） -->
            <div class="flex lg:flex-row flex-col mb-0" style="gap: 0;" ref="productDisplayRef">

              <!-- 1. 产品缩略图区域 - 5个缩略图 -->
              <div class="flex-shrink-0" style="width: 120px;">
                <div class="flex flex-col justify-between" style="height: 592px;">
                  <button
                    v-for="(image, index) in productImages"
                    :key="index"
                    @click="setCurrentImage(index)"
                    class="w-28 h-28 rounded-lg overflow-hidden border-2 transition-all duration-200 hover:shadow-md"
                    :class="currentImageIndex === index ? 'border-blue-500 shadow-lg ring-2 ring-blue-200' : 'border-gray-300 hover:border-blue-300'"
                  >
                    <img :src="image.thumb" :alt="image.alt" class="w-full h-full object-cover">
                  </button>
                </div>
              </div>

              <!-- 2. 右侧50%/50%布局区域 -->
              <div class="flex-1">
                <div class="grid grid-cols-1 lg:grid-cols-2" style="gap: 5px; height: 592px;">

                  <!-- 2.1 产品大图区域（左列50%） -->
                  <div class="lg:col-span-1">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden" style="height: 592px;">
                      <div
                        class="product-main-image relative overflow-hidden bg-gray-100 cursor-zoom-in"
                        @mousemove="handleMouseMove"
                        @mouseleave="handleMouseLeave"
                        @mouseenter="handleMouseEnter"
                        ref="mainImageContainer"
                        style="width: 100%; height: 592px;"
                      >
                        <img
                          :src="currentImage"
                          alt="P301-2D-2W台式人脸消费机"
                          class="w-full h-full object-contain transition-transform duration-75"
                          :style="imageTransform"
                          ref="mainImage"
                        >
                        <!-- 悬停放大镜框 - 正方形，大图的1/4大小 -->
                        <div
                          v-if="showMagnifier"
                          class="magnifier-lens absolute border border-blue-500 pointer-events-none"
                          :style="lensStyle"
                        ></div>
                      </div>
                    </div>
                  </div>

                  <!-- 2.2 产品简介区域（右列50%） -->
                  <div class="lg:col-span-1">
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden relative" style="height: 592px;">
                      <div class="bg-gray-50 relative overflow-hidden" style="width: 100%; height: 592px;">
                        <!-- 放大镜显示区域 - 完全覆盖简介区域 -->
                        <div
                          v-if="showMagnifier"
                          class="magnified-view absolute inset-0 w-full h-full bg-cover bg-no-repeat z-10"
                          :style="magnifiedStyle"
                        ></div>

                        <!-- 产品简介内容 - 重新设计的布局 -->
                        <div class="absolute inset-0 p-8 flex flex-col justify-start">
                          <!-- 产品标签 -->
                          <div class="flex flex-wrap gap-3 mb-6">
                            <span class="bg-red-100 text-red-600 px-3 py-2 rounded-full text-sm font-semibold shadow-sm">热销产品</span>
                            <span class="bg-blue-100 text-blue-600 px-3 py-2 rounded-full text-sm font-semibold shadow-sm">人脸识别</span>
                            <span class="bg-green-100 text-green-600 px-3 py-2 rounded-full text-sm font-semibold shadow-sm">安卓系统</span>
                          </div>

                          <!-- 产品标题 -->
                          <h2 class="text-2xl font-bold text-gray-800 mb-6 leading-tight">P301-2D-2W 台式人脸消费机</h2>

                          <!-- 产品简介 -->
                          <div class="flex-1">
                            <p class="text-gray-700 text-base leading-relaxed mb-4">
                              采用先进的人脸识别技术，支持离线识别，识别速度快，准确率高。
                            </p>
                            <p class="text-gray-700 text-base leading-relaxed mb-4">
                              搭载安卓操作系统，界面友好，操作简单。
                            </p>
                            <p class="text-gray-700 text-base leading-relaxed">
                              适用于学校食堂、企业餐厅、医院等各种消费场景，为用户提供安全便捷的支付体验。
                            </p>
              </div>
            </div>
          </div>
        </div>
      </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- 跨区域导航栏 - 带渐隐渐现效果 -->
    <div ref="floatingNav" class="fixed left-1/2 transform -translate-x-1/2 lg:left-20 lg:transform-none top-24 z-40 lg:block hidden transition-all duration-500 ease-in-out"
         style="opacity: 0; transform: translateX(-20px);">
      <div class="bg-gray-50 rounded-xl p-6 shadow-lg">
        <h3 class="text-lg font-bold text-gray-800 mb-4">页面导航</h3>
        <nav class="space-y-2">
          <a href="#product-params" class="flex items-center p-3 rounded-lg hover:bg-green-50 transition duration-300 text-gray-600 hover:text-green-600 cursor-pointer">
            <i class="fas fa-cogs mr-3"></i>
            技术规格
          </a>
          <a href="#product-details" class="flex items-center p-3 rounded-lg hover:bg-blue-50 transition duration-300 text-gray-600 hover:text-blue-600 cursor-pointer">
            <i class="fas fa-info-circle mr-3"></i>
            产品详情
          </a>
        </nav>
      </div>
    </div>

    <!-- 技术规格部分 -->
    <section id="product-params" class="bg-gray-50" style="margin-top: -200px; padding-top: 0; padding-bottom: 0;">
      <div class="container mx-auto px-4 pt-6 pb-16">
        <!-- 标题与内容对齐 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div class="lg:col-span-1"></div>
          <div class="lg:col-span-3">
            <div class="mb-12">
              <h2 class="text-3xl font-bold text-gray-800 mb-4">技术规格</h2>
            </div>
            <div class="bg-white rounded-lg shadow-md p-8">
              <h3 class="text-xl font-bold text-gray-800 mb-6">技术参数</h3>
              <div class="space-y-4">
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">产品型号：</span>
                  <span class="text-gray-600">P301-2D-2W</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">操作系统：</span>
                  <span class="text-gray-600">Android 8.1</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">处理器：</span>
                  <span class="text-gray-600">ARM Cortex-A7 四核 1.2GHz</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">内存：</span>
                  <span class="text-gray-600">1GB DDR3</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">存储：</span>
                  <span class="text-gray-600">8GB eMMC</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">屏幕尺寸：</span>
                  <span class="text-gray-600">8英寸 IPS触摸屏</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">分辨率：</span>
                  <span class="text-gray-600">1280×800</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">摄像头：</span>
                  <span class="text-gray-600">200万像素双目摄像头</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">识别距离：</span>
                  <span class="text-gray-600">0.3-1.5米</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">识别速度：</span>
                  <span class="text-gray-600">≤1秒</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">存储容量：</span>
                  <span class="text-gray-600">3万张人脸</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">通讯方式：</span>
                  <span class="text-gray-600">WiFi/以太网/4G(可选)</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">工作温度：</span>
                  <span class="text-gray-600">-10℃~60℃</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">工作湿度：</span>
                  <span class="text-gray-600">10%~90%RH</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">电源：</span>
                  <span class="text-gray-600">DC 12V/3A</span>
                </div>
                <div class="flex">
                  <span class="w-32 font-medium text-gray-700">产品尺寸：</span>
                  <span class="text-gray-600">280×180×45mm</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品详情部分 -->
    <section id="product-details" class="py-0 bg-white">
      <div class="container mx-auto px-4 py-16">
        <!-- 标题与内容对齐 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div class="lg:col-span-1"></div>
          <div class="lg:col-span-3">
            <div class="mb-12">
              <h2 class="text-3xl font-bold text-gray-800 mb-4">产品详情</h2>
            </div>
            <div class="bg-white rounded-lg shadow-md p-8">
              <div class="space-y-8">
                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-4">产品概述</h3>
                  <p class="text-gray-600 leading-relaxed">
                    P301-2D-2W台式人脸消费机是一款专为现代消费场景设计的智能终端设备。该产品采用先进的人脸识别技术，结合Android操作系统的便捷性，为用户提供安全、快速、便民的消费体验。设备支持离线识别功能，即使在网络不稳定的环境下也能正常工作，确保消费流程的连续性和稳定性。
                  </p>
                </div>

                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-4">核心功能特点</h3>
                  <div class="space-y-3">
                    <p class="text-gray-600"><span class="font-medium">高精度人脸识别：</span>采用深度学习算法和双目摄像头技术，识别准确率高达99.7%，有效防止误识别和拒识现象。</p>
                    <p class="text-gray-600"><span class="font-medium">快速响应处理：</span>识别速度≤1秒，从人脸检测到完成支付整个流程仅需数秒时间，大大提升了用户体验和消费效率。</p>
                    <p class="text-gray-600"><span class="font-medium">大容量存储：</span>设备可存储多达3万张人脸信息，满足大型机构和企业的使用需求。</p>
                    <p class="text-gray-600"><span class="font-medium">多网络连接：</span>支持WiFi、以太网和4G网络连接，确保在各种网络环境下都能正常工作。</p>
                  </div>
                </div>

                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-4">适用场景</h3>
                  <div class="space-y-3">
                    <p class="text-gray-600"><span class="font-medium">教育机构：</span>适用于学校食堂、图书馆、宿舍等场所，学生无需携带校园卡，通过人脸识别即可完成消费。</p>
                    <p class="text-gray-600"><span class="font-medium">企业园区：</span>在企业餐厅、便利店等场所使用，员工可以享受便捷的消费体验。</p>
                    <p class="text-gray-600"><span class="font-medium">医疗机构：</span>医院食堂、药房等场所，医护人员在繁忙的工作中可以快速完成消费。</p>
                    <p class="text-gray-600"><span class="font-medium">商业场所：</span>连锁餐厅、咖啡店等商业场所，为会员提供个性化服务。</p>
                  </div>
                </div>

                <div>
                  <h3 class="text-xl font-bold text-gray-800 mb-4">技术优势</h3>
                  <div class="space-y-3">
                    <p class="text-gray-600"><span class="font-medium">安全可靠：</span>采用多重安全防护机制，包括活体检测、加密传输、安全存储等，确保用户信息和交易数据的安全性。</p>
                    <p class="text-gray-600"><span class="font-medium">稳定耐用：</span>设备采用工业级硬件设计，工作温度范围-10℃~60℃，能够适应各种恶劣环境。</p>
                    <p class="text-gray-600"><span class="font-medium">易于维护：</span>支持远程升级和维护功能，系统管理员可以通过网络对设备进行远程管理。</p>
                    <p class="text-gray-600"><span class="font-medium">用户友好：</span>基于Android 8.1系统开发，界面简洁直观，操作简单易懂。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AOS from 'aos'
import ProductNavigation from '../../../components/ProductNavigation.vue'

export default {
  name: 'AndroidFaceP301',
  components: {
    ProductNavigation
  },
  setup() {
    const currentCategory = ref('android-face')
    const currentProduct = ref('p301-2d-2w')
    const currentImageIndex = ref(0)
    const showMagnifier = ref(false)
    const mouseX = ref(0)
    const mouseY = ref(0)
    const mainImageContainer = ref(null)
    const mainImage = ref(null)

    // 右侧容器引用（保留用于其他功能）
    const rightContentRef = ref(null)
    const productDisplayRef = ref(null)
    const productParamsRef = ref(null)

    // 使用您提供的真实产品图片 - 扩展到5个图片
    const productImages = ref([
      {
        full: '/images/products/p301-front.png',
        thumb: '/images/products/p301-front.png',
        alt: 'P301-2D-2W正面视图'
      },
      {
        full: '/images/products/p301-side.png',
        thumb: '/images/products/p301-side.png',
        alt: 'P301-2D-2W侧面视图'
      },
      {
        full: '/images/products/p301-back.png',
        thumb: '/images/products/p301-back.png',
        alt: 'P301-2D-2W背面视图'
      },
      {
        full: '/images/products/p301-front.png',
        thumb: '/images/products/p301-front.png',
        alt: 'P301-2D-2W正面细节'
      },
      {
        full: '/images/products/p301-side.png',
        thumb: '/images/products/p301-side.png',
        alt: 'P301-2D-2W侧面细节'
      }
    ])

    const currentImage = computed(() => {
      return productImages.value[currentImageIndex.value]?.full || ''
    })

    const setCurrentImage = (index) => {
      currentImageIndex.value = index
    }

    const handleMouseEnter = () => {
      showMagnifier.value = true
    }

    // 高性能鼠标移动事件处理，使用RAF节流
    let rafId = null
    let lastUpdateTime = 0
    const handleMouseMove = (event) => {
      if (!mainImageContainer.value || !showMagnifier.value) return

      // 取消之前的动画帧请求
      if (rafId) {
        cancelAnimationFrame(rafId)
      }

      // 使用requestAnimationFrame确保60fps性能
      rafId = requestAnimationFrame((currentTime) => {
        // 限制更新频率到60fps
        if (currentTime - lastUpdateTime >= 16) {
          const rect = mainImageContainer.value.getBoundingClientRect()
          mouseX.value = event.clientX - rect.left
          mouseY.value = event.clientY - rect.top
          lastUpdateTime = currentTime
        }
      })
    }

    const handleMouseLeave = () => {
      showMagnifier.value = false
      // 清理动画帧请求
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }
    }

    // 放大镜镜头样式 - 精确计算，确保完美同步
    const lensStyle = computed(() => {
      if (!mainImageContainer.value) return {}

      const rect = mainImageContainer.value.getBoundingClientRect()
      // 边长为大图边长的1/2，实现面积1/4覆盖
      const lensSize = Math.min(rect.width, rect.height) / 2

      // 精确的边界约束计算
      const maxX = rect.width - lensSize
      const maxY = rect.height - lensSize
      const constrainedX = Math.max(0, Math.min(mouseX.value - lensSize / 2, maxX))
      const constrainedY = Math.max(0, Math.min(mouseY.value - lensSize / 2, maxY))

      return {
        width: `${lensSize}px`,
        height: `${lensSize}px`,
        left: `${constrainedX}px`,
        top: `${constrainedY}px`,
        borderRadius: '6px',
        transform: 'translate3d(0, 0, 0)', // GPU加速
        willChange: 'left, top, transform',
        boxShadow: '0 0 0 2px rgba(59, 130, 246, 0.8), 0 0 15px rgba(59, 130, 246, 0.3)',
        // 缓存计算结果到data属性，供magnifiedStyle使用
        '--lens-x': constrainedX,
        '--lens-y': constrainedY,
        '--lens-size': lensSize
      }
    })

    // 图片变换样式
    const imageTransform = computed(() => {
      if (!showMagnifier.value) return {}
      return {
        transform: 'scale(1.01)'
      }
    })

    // 放大显示样式 - 完全重构，实现完美的"所见即所得"效果
    const magnifiedStyle = computed(() => {
      if (!showMagnifier.value || !mainImageContainer.value) return {}

      const rect = mainImageContainer.value.getBoundingClientRect()
      const lensSize = Math.min(rect.width, rect.height) / 2

      // 与lensStyle使用完全相同的计算逻辑，确保100%同步
      const maxX = rect.width - lensSize
      const maxY = rect.height - lensSize
      const constrainedX = Math.max(0, Math.min(mouseX.value - lensSize / 2, maxX))
      const constrainedY = Math.max(0, Math.min(mouseY.value - lensSize / 2, maxY))

      // 核心算法：完美的1:1映射
      // 目标：放大镜框选中的区域(lensSize×lensSize) → 右侧显示区域(rect.width×rect.height)

      // 计算缩放因子：右侧显示区域 ÷ 放大镜框尺寸
      const scaleFactorX = rect.width / lensSize   // X方向缩放因子 = 2
      const scaleFactorY = rect.height / lensSize  // Y方向缩放因子 = 2

      // 背景图片尺寸：原图尺寸 × 缩放因子
      const backgroundWidth = rect.width * scaleFactorX
      const backgroundHeight = rect.height * scaleFactorY

      // 背景位置：将放大镜框区域精确映射到右侧显示区域
      // 放大镜框左上角(constrainedX, constrainedY) → 右侧显示区域左上角(0, 0)
      const backgroundX = -constrainedX * scaleFactorX
      const backgroundY = -constrainedY * scaleFactorY

      return {
        backgroundImage: `url(${currentImage.value})`,
        backgroundPosition: `${backgroundX}px ${backgroundY}px`,
        backgroundSize: `${backgroundWidth}px ${backgroundHeight}px`,
        backgroundRepeat: 'no-repeat',
        transform: 'translate3d(0, 0, 0)', // GPU加速
        willChange: 'background-position',
        transition: 'none', // 移除过渡以获得最佳性能
        filter: 'contrast(1.03) brightness(1.01)', // 轻微增强
        imageRendering: 'crisp-edges' // 确保清晰的像素渲染
      }
    })



    // 浮动导航栏控制
    const floatingNav = ref(null)

    const initFloatingNav = () => {
      if (!floatingNav.value) return

      const handleScroll = () => {
        // 找到产品信息区块（包含产品描述的区域）
        const productInfoElement = document.querySelector('#product-description-bottom')

        if (!productInfoElement) return

        // 获取产品信息区块的底部位置
        const productInfoRect = productInfoElement.getBoundingClientRect()
        const productInfoBottom = productInfoRect.bottom + window.scrollY

        // 获取当前滚动位置
        const currentScrollTop = window.scrollY

        // 检查是否滚动超过了产品信息区块的底部
        if (currentScrollTop > productInfoBottom) {
          // 渐现效果 - 滚动超过产品信息区块底部后显示
          floatingNav.value.style.opacity = '1'
          floatingNav.value.style.transform = 'translateX(0)'
        } else {
          // 渐隐效果 - 还没有滚动超过产品信息区块底部时隐藏
          floatingNav.value.style.opacity = '0'
          floatingNav.value.style.transform = 'translateX(-20px)'
        }
      }

      // 监听滚动事件
      window.addEventListener('scroll', handleScroll)

      // 初始检查
      handleScroll()
    }

    onMounted(() => {
      AOS.init({
        duration: 1000,
        once: true
      })

      // 初始化浮动导航栏
      setTimeout(() => {
        initFloatingNav()
      }, 100)


    })

    // 清理资源
    onUnmounted(() => {
      if (rafId) {
        cancelAnimationFrame(rafId)
        rafId = null
      }

    })

    return {
      currentCategory,
      currentProduct,
      currentImageIndex,
      currentImage,
      productImages,
      showMagnifier,
      mouseX,
      mouseY,
      mainImageContainer,
      mainImage,
      setCurrentImage,
      handleMouseEnter,
      handleMouseMove,
      handleMouseLeave,
      lensStyle,
      imageTransform,
      magnifiedStyle,
      floatingNav,
      rightContentRef,
      productDisplayRef,
      productParamsRef
    }
  }
}
</script>

<style scoped>
/* 放大镜相关样式 - 高性能优化版本 */
.magnifier-lens {
  /* 移除过渡动画，使用纯GPU加速获得最佳性能 */
  will-change: left, top, transform;
  transform: translate3d(0, 0, 0); /* GPU加速 */
  backface-visibility: hidden;
  /* 细边框设计 */
  border-width: 1px;
  border-style: solid;
  /* 优化渲染 */
  contain: layout style paint;
}

.magnified-view {
  border-radius: 12px;
  will-change: background-position;
  border: 1px solid #e5e7eb;
  transform: translate3d(0, 0, 0); /* GPU加速 */
  backface-visibility: hidden;
  /* 优化背景渲染 */
  contain: layout style paint;
  image-rendering: crisp-edges;
}

.product-main-image {
  background: #f8f9fa;
  border-radius: 12px;
  transform: translate3d(0, 0, 0); /* GPU加速 */
  /* 优化容器渲染 */
  contain: layout style paint;
}

.product-main-image img {
  will-change: transform;
  transform: translate3d(0, 0, 0); /* GPU加速 */
  /* 确保图片清晰渲染 */
  image-rendering: crisp-edges;
}

/* 缩略图样式 - 5个缩略图更小尺寸 */
.space-y-2 button {
  transition: all 0.2s ease-out;
}

.space-y-2 button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 产品简介区域样式 */
.aspect-square {
  position: relative;
}

/* 放大镜覆盖效果 */
.magnified-view {
  border-radius: 12px;
}

/* 产品简介区域样式优化 */
.product-intro-content {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}



/* 响应式调整 */
@media (max-width: 1024px) {
  /* 缩略图在中等屏幕上水平排列 */
  .space-y-2 {
    display: flex;
    flex-direction: row;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .space-y-2 > * + * {
    margin-top: 0;
  }

  .space-y-2 button {
    width: 114px;
    height: 114px;
    flex-shrink: 0;
  }

  /* 50%/50%布局在中等屏幕上变为垂直堆叠 */
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 5px;
  }
}

@media (max-width: 768px) {
  /* 在小屏幕上完全垂直布局 */
  .space-y-2 button {
    width: 100px;
    height: 100px;
  }
}





/* 扩展导航栏滑动范围 */
.sticky {
  position: sticky;
  top: 6rem;
  z-index: 40;
  /* 让导航栏能够滑动到页面底部 */
  align-self: flex-start;
  height: fit-content;
}

/* 优化GPU加速 */
.product-main-image,
.magnifier-lens,
.magnified-view {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 确保技术规格区域不会影响客服组件显示 */
#product-params {
  position: relative;
  z-index: 1;
}

/* 确保客服组件在所有区域都能正常显示 */
.customer-service-float,
.customer-service-toggle {
  z-index: 9999 !important;
}


</style>