<template>
  <!-- 导航栏 -->
  <nav class="fixed w-full top-0 bg-white shadow-md z-50">
    <div class="container mx-auto px-4">
      <div class="flex justify-between items-center py-4">
        <!-- LOGO -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center">
            <img src="/images/products/szjocat.jpg" alt="深圳市优卡特实业有限公司标志，蓝色现代简约设计，展示公司品牌形象的科技风格logo" class="navbar-logo w-auto max-w-none object-contain hover:opacity-80 transition duration-300">
          </router-link>
        </div>

        <!-- 导航菜单 -->
        <div class="hidden lg:flex items-center space-x-6 nav-menu-bubbles absolute right-5 top-1/2 transform -translate-y-1/2">
          <router-link to="/" class="bubbles animate-fade-in" style="animation-delay: 0.1s;">
            <span class="text">首页</span>
          </router-link>


          <div class="nav-item group animate-fade-in" style="animation-delay: 0.3s;">
            <router-link to="/products" class="bubbles">
              <span class="text">产品中心</span>
              <i class="fas fa-chevron-down text-xs"></i>
            </router-link>
            <div class="mega-menu py-2">
              <!-- 智能消费机系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/smart-consumer" class="menu-item-link">
                  <span>智能消费机系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                  <router-link to="/products/smart-consumer/home-face" class="submenu-item">安卓人脸消费终端</router-link>
                  <router-link to="/products/smart-consumer/real-qr" class="submenu-item">安卓二维码消费终端</router-link>
                  <router-link to="/products/smart-consumer/ar-terminal" class="submenu-item">AR消费终端</router-link>
                </div>
              </div>

              <!-- AI智能算台系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/ai-platform" class="menu-item-link">
                  <span>AI智能算台系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                  <router-link to="/products/ai-platform/product-recognition" class="submenu-item">AI商品识别算台</router-link>
                  <router-link to="/products/ai-platform/smart-weighing" class="submenu-item">智能称重算台</router-link>
                </div>
              </div>

              <!-- 智能水控机系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/water-control" class="menu-item-link">
                  <span>智能水控机系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                  <router-link to="/products/water-control/smart-water" class="submenu-item">智能一体水控机</router-link>
                  <router-link to="/products/water-control/split-water" class="submenu-item">智能分体水控机</router-link>
                </div>
              </div>

              <!-- 智能电控机系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/electric-control" class="menu-item-link">
                  <span>智能电控机系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                  <router-link to="/products/electric-control/timing-electric" class="submenu-item">计时计量电控机</router-link>
                  <router-link to="/products/electric-control/timing-control" class="submenu-item">计时电控机</router-link>
                </div>
              </div>

              <!-- 智能水表/电表系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/smart-meter" class="menu-item-link">
                  <span>智能水表/电表系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                  <router-link to="/products/smart-meter/water-meter" class="submenu-item">智能水表</router-link>
                  <router-link to="/products/smart-meter/electric-meter" class="submenu-item">智能电表</router-link>
                </div>
              </div>

              <!-- 共享洗衣机系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/shared-washer" class="menu-item-link">
                  <span>共享洗衣机系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                </div>
              </div>

              <!-- 共享吹风机系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/shared-dryer" class="menu-item-link">
                  <span>共享吹风机系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                </div>
              </div>

              <!-- 门禁/考勤系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/access-attendance" class="menu-item-link">
                  <span>门禁/考勤系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                </div>
              </div>

              <!-- 共享视频电话机系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/shared-video-phone" class="menu-item-link">
                  <span>共享视频电话机系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                </div>
              </div>

              <!-- 访客机系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/visitor-machine" class="menu-item-link">
                  <span>访客机系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                </div>
              </div>

              <!-- 智能自助机系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/smart-self-service" class="menu-item-link">
                  <span>智能自助机系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                  <router-link to="/products/smart-self-service/android-self" class="submenu-item">安卓自助机</router-link>
                  <router-link to="/products/smart-self-service/windows-self" class="submenu-item">Windows自助机</router-link>
                </div>
              </div>

              <!-- 掌静脉系列 -->
              <div class="menu-item-with-submenu">
                <router-link to="/products/palm-vein" class="menu-item-link">
                  <span>掌静脉系列</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </router-link>
                <div class="submenu">
                  <router-link to="/products/palm-vein/palm-consumer" class="submenu-item">掌静脉消费机</router-link>
                  <router-link to="/products/palm-vein/palm-water" class="submenu-item">掌静脉水控机</router-link>
                  <router-link to="/products/palm-vein/palm-electric" class="submenu-item">掌静脉电控机</router-link>
                  <router-link to="/products/palm-vein/palm-washer" class="submenu-item">掌静脉共享洗衣机</router-link>
                  <router-link to="/products/palm-vein/palm-dryer" class="submenu-item">掌静脉共享吹风机</router-link>
                  <router-link to="/products/palm-vein/palm-access" class="submenu-item">掌静脉门禁/考勤机</router-link>
                </div>
              </div>
            </div>
          </div>

          <div class="nav-item group animate-fade-in" style="animation-delay: 0.4s;">
            <router-link to="/solutions" class="bubbles">
              <span class="text">解决方案</span>
              <i class="fas fa-chevron-down text-xs"></i>
            </router-link>
            <div class="mega-menu py-2">
              <router-link to="/solutions/smart-campus" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">智慧校园解决方案</router-link>
              <router-link to="/solutions/enterprise-park" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">企业园区解决方案</router-link>
              <router-link to="/solutions/medical-institution" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">医疗机构解决方案</router-link>
              <router-link to="/solutions/government-agency" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">政府机关解决方案</router-link>
              <router-link to="/solutions/factory-park" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">工厂园区解决方案</router-link>
              <router-link to="/solutions/smart-hotel" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">智慧酒店解决方案</router-link>
            </div>
          </div>

          <div class="nav-item group animate-fade-in" style="animation-delay: 0.5s;">
            <router-link to="/news" class="bubbles">
              <span class="text">新闻中心</span>
              <i class="fas fa-chevron-down text-xs"></i>
            </router-link>
            <div class="mega-menu py-2">
              <router-link to="/news/company" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">企业新闻</router-link>
              <router-link to="/news/industry" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">行业资讯</router-link>
            </div>
          </div>

          <div class="nav-item group animate-fade-in" style="animation-delay: 0.2s;">
            <router-link to="/about" class="bubbles">
              <span class="text">关于我们</span>
              <i class="fas fa-chevron-down text-xs"></i>
            </router-link>
            <div class="mega-menu py-2">
              <router-link to="/about/history" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">发展历程</router-link>
              <router-link to="/about/team" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">企业团队</router-link>
              <router-link to="/about/honors" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">企业荣誉</router-link>
              <router-link to="/about/careers" class="block px-4 py-2 hover:bg-gray-100 text-gray-700">招贤纳士</router-link>
            </div>
          </div>

          
          <router-link to="/technical-support" class="bubbles animate-fade-in" style="animation-delay: 0.6s;">
            <span class="text">技术支持</span>
          </router-link>

          <router-link to="/contact" class="bubbles animate-fade-in" style="animation-delay: 0.65s;">
            <span class="text">联系我们</span>
          </router-link>
        </div>

        <!-- 移动端菜单按钮 -->
        <div class="lg:hidden">
          <button class="text-gray-800 focus:outline-none">
            <i class="fas fa-bars text-2xl"></i>
          </button>
        </div>
      </div>
    </div>
  </nav>
</template>

<script>
export default {
  name: 'Navbar'
}
</script>

<style>
/* 导航栏特定样式 */

/* 气泡动画按钮样式 */
.bubbles {
  --c1: #1a5fb4; /* 同背景色 */
  --c2: #1a5fb4; /* 使用网站主色调 */
  --size-letter: 16px;
  padding: 0.5em 1em;
  font-size: var(--size-letter);

  background-color: transparent;
  border: calc(var(--size-letter) / 6) solid var(--c2);
  border-radius: 0.2em;
  cursor: pointer;

  overflow: hidden;
  position: relative;
  transition: 300ms cubic-bezier(0.83, 0, 0.17, 1);
  /* 确保伪元素不会超出边界 */
  isolation: isolate;
}

.bubbles > .text {
  font-weight: 700;
  color: var(--c2);
  position: relative;
  z-index: 1;
  transition: color 700ms cubic-bezier(0.83, 0, 0.17, 1);
}

/* 禁用背景动画效果 */
.bubbles::before {
  display: none;
}

/* 移除 after 伪元素，只使用一个方形覆盖 */
.bubbles::after {
  display: none;
}

.bubbles:hover > .text {
  color: #1a5fb4 !important;
}

.bubbles:hover::before {
  transform: scaleX(1);
}

.bubbles:active {
  scale: 0.98;
  filter: brightness(0.9);
}

/* 导航菜单样式调整 */
.nav-menu-bubbles {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 移除之前的响应式布局调整 */
/* 已使用绝对定位实现精确对齐 */

/* 下拉菜单样式调整 */
.nav-item .bubbles {
  display: inline-flex;
  align-items: center;
}

.nav-item .bubbles i {
  margin-left: 0.25rem;
  font-size: 0.75rem;
  position: relative;
  z-index: 1;
  color: var(--c2);
  transition: color 700ms cubic-bezier(0.83, 0, 0.17, 1);
}

.nav-item .bubbles:hover i {
  color: #1a5fb4 !important;
}

/* 下拉菜单样式 */
.mega-menu {
  position: absolute;
  background: rgba(255, 255, 255, 0.98);
  width: 220px;
  z-index: 100;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(15px);
  transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out, visibility 0.4s ease-in-out;
  border-radius: 8px;
  overflow: visible !important;
}

.nav-item:hover .mega-menu,
.mega-menu:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(5px);
}

/* 多级菜单项样式 */
.menu-item-with-submenu {
  position: relative !important;
  display: block !important;
}

.menu-item-link {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  color: #374151 !important;
  text-decoration: none !important;
  transition: background-color 0.2s ease;
  width: 100%;
  box-sizing: border-box;
}

.menu-item-link:hover {
  background-color: #f3f4f6 !important;
  color: #374151 !important;
}

.menu-item-link i {
  opacity: 0.6;
  transition: opacity 0.2s ease, transform 0.2s ease;
  margin-left: auto;
}

.menu-item-with-submenu:hover .menu-item-link i {
  opacity: 1;
  transform: translateX(2px);
}

/* 二级子菜单样式 */
.submenu {
  position: absolute !important;
  left: 100% !important;
  top: 0 !important;
  background: rgba(255, 255, 255, 0.98) !important;
  width: 200px !important;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
  border-radius: 8px !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: translateX(-10px) !important;
  transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out, visibility 0.3s ease-in-out !important;
  z-index: 1000 !important;
  overflow: hidden !important;
  pointer-events: none;
}

.menu-item-with-submenu:hover .submenu,
.submenu:hover {
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateX(0) !important;
  pointer-events: auto !important;
}

.submenu-item {
  display: block !important;
  padding: 0.75rem 1rem !important;
  color: #374151 !important;
  text-decoration: none !important;
  font-size: 0.9rem !important;
  transition: background-color 0.2s ease, color 0.2s ease !important;
  border-bottom: 1px solid #f3f4f6 !important;
  width: 100% !important;
  box-sizing: border-box !important;
  white-space: nowrap !important;
}

.submenu-item:last-child {
  border-bottom: none !important;
}

.submenu-item:hover {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Tailwind CSS 动画定义 */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.7s ease-out;
}

/* Logo样式优化 */
.navbar-logo {
  max-height: 2.5rem;
  min-height: 2rem;
}

@media (min-width: 768px) {
  .navbar-logo {
    max-height: 3rem;
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .submenu {
    position: static;
    width: 100%;
    box-shadow: none;
    border-radius: 0;
    background: #f9fafb;
    margin-left: 1rem;
    opacity: 1;
    visibility: visible;
    transform: none;
    transition: none;
  }

  .menu-item-with-submenu:hover .submenu {
    transform: none;
  }

  .submenu-item {
    padding-left: 2rem;
    font-size: 0.85rem;
  }
}

/* 移动端样式 */
.rotate-180 {
  transform: rotate(180deg);
}

/* 菜单动画优化 */
.menu-item-with-submenu {
  overflow: visible;
}

/* 防止菜单被遮挡 */
.nav-item {
  position: relative;
  z-index: 50;
}

.mega-menu {
  z-index: 100;
}

.submenu {
  z-index: 1001;
}

/* 调试样式 - 确保子菜单可见 */
.menu-item-with-submenu:hover .submenu {
  display: block !important;
}

/* 修复可能的样式冲突 */
.mega-menu .menu-item-with-submenu {
  position: relative !important;
}

.mega-menu .submenu {
  position: absolute !important;
  left: 100% !important;
  top: 0 !important;
}
</style>
