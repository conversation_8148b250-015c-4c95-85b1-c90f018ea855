package com.youkate.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品实体类
 */
@Entity
@Table(name = "products")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Product {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "产品名称不能为空")
    @Column(nullable = false, length = 100)
    private String name;
    
    @NotBlank(message = "产品描述不能为空")
    @Column(nullable = false, length = 500)
    private String description;
    
    @NotBlank(message = "产品价格不能为空")
    @Column(nullable = false, length = 50)
    private String price;
    
    @Column(name = "image_url", length = 500)
    private String imageUrl;
    
    @Column(name = "detail_images", columnDefinition = "TEXT")
    private String detailImages; // JSON格式存储多张图片
    
    @NotBlank(message = "产品分类不能为空")
    @Column(nullable = false, length = 50)
    private String category;
    
    @Column(length = 50)
    private String badge; // 产品徽章：热销、推荐、新品等
    
    @Column(columnDefinition = "TEXT")
    private String features; // JSON格式存储产品特性
    
    @Column(columnDefinition = "TEXT")
    private String specifications; // JSON格式存储产品规格
    
    @Column(name = "theme_color", length = 50)
    private String themeColor; // 产品主题色
    
    @Column(name = "sort_order")
    private Integer sortOrder = 0; // 排序字段
    
    @Column(name = "is_active")
    private Boolean isActive = true; // 是否启用
    
    @Column(name = "created_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
